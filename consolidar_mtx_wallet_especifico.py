#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import logging
from datetime import datetime

# Agregar el directorio actual al path para poder importar
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

def consolidar_mtx_wallet():
    """
    Ejecuta la consolidación específica de MTX_WALLET_ORA usando el código corregido.
    """
    
    print("=== CONSOLIDACIÓN ESPECÍFICA MTX_WALLET_ORA ===")
    print(f"Iniciando proceso: {datetime.now()}")
    
    try:
        # Importar las funciones necesarias
        from app_raw_consolidado import procesar_consolidacion_tabla, logging
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Ejecutar consolidación para MTX_WALLET_ORA específicamente
        tabla_nombre = "MTX_WALLET_ORA"
        seccion_img = "IMG_FLOW_04"  # Sección que contiene MTX_WALLET_ORA
        
        print(f"Procesando tabla: {tabla_nombre}")
        print(f"Sección: {seccion_img}")
        
        # Llamar a la función de consolidación
        resultado = procesar_consolidacion_tabla(seccion_img, tabla_nombre)
        
        if resultado:
            print(f"✅ Consolidación de {tabla_nombre} completada exitosamente")
        else:
            print(f"❌ Error en la consolidación de {tabla_nombre}")
            
    except ImportError as e:
        print(f"Error de importación: {str(e)}")
        print("Verificando que el módulo app_raw_consolidado esté disponible...")
        
    except Exception as e:
        print(f"Error durante la consolidación: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    consolidar_mtx_wallet()
