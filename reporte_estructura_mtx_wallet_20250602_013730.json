{"comparacion": {"timestamp": "2025-06-02T01:37:30.115138", "resumen": {"origen": {"archivo": "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-013525_chunk_0.parquet", "registros": 80, "columnas": 31}, "destino": {"archivo": "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet", "registros": 80, "columnas": 37}}, "diferencias_columnas": {"status": "✅ Columnas originales preservadas correctamente", "metadatos_agregados": ["data_lake_consolidation_version", "source_year", "data_lake_consolidated_timestamp", "source_file", "source_day", "source_month"]}, "diferencias_tipos": {"LAST_TRANSFER_ON": {"origen": "datetime64[ns]", "destino": "object"}, "data_lake_last_modified": {"origen": "datetime64[us]", "destino": "object"}, "CREATED_ON": {"origen": "datetime64[ns]", "destino": "object"}, "DATA_LAKE_PARTITION_DATE": {"origen": "datetime64[ns]", "destino": "object"}, "MODIFIED_ON": {"origen": "datetime64[ns]", "destino": "object"}}, "diferencias_registros": {"status": "✅ Todos los registros preservados"}, "integridad_preservada": false, "recomendaciones": ["⚠️ Tipos de datos modificados - revisar preservación"]}, "estructura_origen": {"nombre": "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-013525_chunk_0.parquet", "total_registros": 80, "total_columnas": 31, "columnas": {"WALLET_NUMBER": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 80}, "PIN_NUMBER": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 2}, "PIN_MODIFIED_ON": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "PIN_STATUS": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "PIN_REQUIRED": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "VALID_FROM_DATE": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "EXPIRY_DATE": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "CREATED_BY": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "CREATED_ON": {"tipo": "datetime64[ns]", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "MODIFIED_BY": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 3}, "MODIFIED_ON": {"tipo": "datetime64[ns]", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 3}, "LAST_TRANSFER_ON": {"tipo": "datetime64[ns]", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 3}, "LAST_TRANSFER_TYPE": {"tipo": "object", "valores_nulos": "77", "porcentaje_nulos": 96.25, "valores_unicos": 1}, "WALLET_LOCK": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "PAYMENT_TYPE_ID": {"tipo": "int64", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 2}, "USER_ID": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 41}, "MSISDN": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "STATUS": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 2}, "USER_TYPE": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "PAYMENT_METHOD_TYPE_ID": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "IS_PRIMARY": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "PROVIDER_ID": {"tipo": "int64", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 13}, "MPAY_PROFILE_ID": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "USER_GRADE": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "DESCRIPTION": {"tipo": "object", "valores_nulos": "39", "porcentaje_nulos": 48.75, "valores_unicos": 2}, "WALLET_REF_ID": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "ACCOUNT_GROUP_ID": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "ISSUER_ID": {"tipo": "float64", "valores_nulos": "29", "porcentaje_nulos": 36.25, "valores_unicos": 19}, "DATA_LAKE_PARTITION_DATE": {"tipo": "datetime64[ns]", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 3}, "RNUM": {"tipo": "int64", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 80}, "data_lake_last_modified": {"tipo": "datetime64[us]", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}}, "tipos_de_datos": {"object": ["WALLET_NUMBER", "PIN_NUMBER", "PIN_MODIFIED_ON", "PIN_STATUS", "PIN_REQUIRED", "VALID_FROM_DATE", "EXPIRY_DATE", "CREATED_BY", "MODIFIED_BY", "LAST_TRANSFER_TYPE", "WALLET_LOCK", "USER_ID", "MSISDN", "STATUS", "USER_TYPE", "PAYMENT_METHOD_TYPE_ID", "IS_PRIMARY", "MPAY_PROFILE_ID", "USER_GRADE", "DESCRIPTION", "WALLET_REF_ID", "ACCOUNT_GROUP_ID"], "datetime64[ns]": ["CREATED_ON", "MODIFIED_ON", "LAST_TRANSFER_ON", "DATA_LAKE_PARTITION_DATE"], "int64": ["PAYMENT_TYPE_ID", "PROVIDER_ID", "RNUM"], "float64": ["ISSUER_ID"], "datetime64[us]": ["data_lake_last_modified"]}, "estadisticas_nulos": {"PIN_MODIFIED_ON": {"cantidad_nulos": "80", "porcentaje": 100.0}, "PIN_STATUS": {"cantidad_nulos": "80", "porcentaje": 100.0}, "VALID_FROM_DATE": {"cantidad_nulos": "80", "porcentaje": 100.0}, "EXPIRY_DATE": {"cantidad_nulos": "80", "porcentaje": 100.0}, "LAST_TRANSFER_TYPE": {"cantidad_nulos": "77", "porcentaje": 96.25}, "MSISDN": {"cantidad_nulos": "80", "porcentaje": 100.0}, "IS_PRIMARY": {"cantidad_nulos": "80", "porcentaje": 100.0}, "MPAY_PROFILE_ID": {"cantidad_nulos": "80", "porcentaje": 100.0}, "DESCRIPTION": {"cantidad_nulos": "39", "porcentaje": 48.75}, "WALLET_REF_ID": {"cantidad_nulos": "80", "porcentaje": 100.0}, "ACCOUNT_GROUP_ID": {"cantidad_nulos": "80", "porcentaje": 100.0}, "ISSUER_ID": {"cantidad_nulos": "29", "porcentaje": 36.25}}, "muestra_datos": {"WALLET_NUMBER": ["102IND03B", "103IND03B", "104IND03B"], "PIN_NUMBER": ["o9bkit9SleWu3tbCteMBgw==", "o9bkit9SleWu3tbCteMBgw==", "o9bkit9SleWu3tbCteMBgw=="], "PIN_MODIFIED_ON": [], "PIN_STATUS": [], "PIN_REQUIRED": ["N", "N", "N"], "VALID_FROM_DATE": [], "EXPIRY_DATE": [], "CREATED_BY": ["MDAR0000000040", "MDAR0000000040", "MDAR0000000040"], "CREATED_ON": ["2001-01-01 00:00:00", "2001-01-01 00:00:00", "2001-01-01 00:00:00"], "MODIFIED_BY": ["MDAR0000000040", "MDAR0000000040", "MDAR0000000040"], "MODIFIED_ON": ["2001-01-01 00:00:00", "2001-01-01 00:00:00", "2001-01-01 00:00:00"], "LAST_TRANSFER_ON": ["2009-02-23 00:00:00", "2009-02-23 00:00:00", "2009-02-23 00:00:00"], "LAST_TRANSFER_TYPE": ["OPTW", "OPTW", "OPTW"], "WALLET_LOCK": ["N", "N", "N"], "PAYMENT_TYPE_ID": ["11", "11", "11"], "USER_ID": ["IND03B", "IND03B", "IND03B"], "MSISDN": [], "STATUS": ["Y", "Y", "Y"], "USER_TYPE": ["OPERATOR", "OPERATOR", "OPERATOR"], "PAYMENT_METHOD_TYPE_ID": ["WALLET", "WALLET", "WALLET"], "IS_PRIMARY": [], "PROVIDER_ID": ["102", "103", "104"], "MPAY_PROFILE_ID": [], "USER_GRADE": ["OPT", "OPT", "OPT"], "DESCRIPTION": ["Expense Account", "Expense Account", "Expense Account"], "WALLET_REF_ID": [], "ACCOUNT_GROUP_ID": [], "ISSUER_ID": ["520.0", "504.0", "505.0"], "DATA_LAKE_PARTITION_DATE": ["2001-01-01 00:00:00", "2001-01-01 00:00:00", "2001-01-01 00:00:00"], "RNUM": ["1", "2", "3"], "data_lake_last_modified": ["2025-06-02 01:35:25.424340", "2025-06-02 01:35:25.424340", "2025-06-02 01:35:25.424340"]}}, "estructura_destino": {"nombre": "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet", "total_registros": 80, "total_columnas": 37, "columnas": {"WALLET_NUMBER": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 80}, "PIN_NUMBER": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 2}, "PIN_MODIFIED_ON": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "PIN_STATUS": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "PIN_REQUIRED": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "VALID_FROM_DATE": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "EXPIRY_DATE": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "CREATED_BY": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "CREATED_ON": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "MODIFIED_BY": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 3}, "MODIFIED_ON": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 3}, "LAST_TRANSFER_ON": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 3}, "LAST_TRANSFER_TYPE": {"tipo": "object", "valores_nulos": "77", "porcentaje_nulos": 96.25, "valores_unicos": 1}, "WALLET_LOCK": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "PAYMENT_TYPE_ID": {"tipo": "int64", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 2}, "USER_ID": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 41}, "MSISDN": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "STATUS": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 2}, "USER_TYPE": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "PAYMENT_METHOD_TYPE_ID": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "IS_PRIMARY": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "PROVIDER_ID": {"tipo": "int64", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 13}, "MPAY_PROFILE_ID": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "USER_GRADE": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "DESCRIPTION": {"tipo": "object", "valores_nulos": "39", "porcentaje_nulos": 48.75, "valores_unicos": 2}, "WALLET_REF_ID": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "ACCOUNT_GROUP_ID": {"tipo": "object", "valores_nulos": "80", "porcentaje_nulos": 100.0, "valores_unicos": 0}, "ISSUER_ID": {"tipo": "float64", "valores_nulos": "29", "porcentaje_nulos": 36.25, "valores_unicos": 19}, "DATA_LAKE_PARTITION_DATE": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 3}, "RNUM": {"tipo": "int64", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 80}, "data_lake_last_modified": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "source_file": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "source_year": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "source_month": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "source_day": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "data_lake_consolidated_timestamp": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}, "data_lake_consolidation_version": {"tipo": "object", "valores_nulos": "0", "porcentaje_nulos": 0.0, "valores_unicos": 1}}, "tipos_de_datos": {"object": ["WALLET_NUMBER", "PIN_NUMBER", "PIN_MODIFIED_ON", "PIN_STATUS", "PIN_REQUIRED", "VALID_FROM_DATE", "EXPIRY_DATE", "CREATED_BY", "CREATED_ON", "MODIFIED_BY", "MODIFIED_ON", "LAST_TRANSFER_ON", "LAST_TRANSFER_TYPE", "WALLET_LOCK", "USER_ID", "MSISDN", "STATUS", "USER_TYPE", "PAYMENT_METHOD_TYPE_ID", "IS_PRIMARY", "MPAY_PROFILE_ID", "USER_GRADE", "DESCRIPTION", "WALLET_REF_ID", "ACCOUNT_GROUP_ID", "DATA_LAKE_PARTITION_DATE", "data_lake_last_modified", "source_file", "source_year", "source_month", "source_day", "data_lake_consolidated_timestamp", "data_lake_consolidation_version"], "int64": ["PAYMENT_TYPE_ID", "PROVIDER_ID", "RNUM"], "float64": ["ISSUER_ID"]}, "estadisticas_nulos": {"PIN_MODIFIED_ON": {"cantidad_nulos": "80", "porcentaje": 100.0}, "PIN_STATUS": {"cantidad_nulos": "80", "porcentaje": 100.0}, "VALID_FROM_DATE": {"cantidad_nulos": "80", "porcentaje": 100.0}, "EXPIRY_DATE": {"cantidad_nulos": "80", "porcentaje": 100.0}, "LAST_TRANSFER_TYPE": {"cantidad_nulos": "77", "porcentaje": 96.25}, "MSISDN": {"cantidad_nulos": "80", "porcentaje": 100.0}, "IS_PRIMARY": {"cantidad_nulos": "80", "porcentaje": 100.0}, "MPAY_PROFILE_ID": {"cantidad_nulos": "80", "porcentaje": 100.0}, "DESCRIPTION": {"cantidad_nulos": "39", "porcentaje": 48.75}, "WALLET_REF_ID": {"cantidad_nulos": "80", "porcentaje": 100.0}, "ACCOUNT_GROUP_ID": {"cantidad_nulos": "80", "porcentaje": 100.0}, "ISSUER_ID": {"cantidad_nulos": "29", "porcentaje": 36.25}}, "muestra_datos": {"WALLET_NUMBER": ["102IND03B", "103IND03B", "104IND03B"], "PIN_NUMBER": ["o9bkit9SleWu3tbCteMBgw==", "o9bkit9SleWu3tbCteMBgw==", "o9bkit9SleWu3tbCteMBgw=="], "PIN_MODIFIED_ON": [], "PIN_STATUS": [], "PIN_REQUIRED": ["N", "N", "N"], "VALID_FROM_DATE": [], "EXPIRY_DATE": [], "CREATED_BY": ["MDAR0000000040", "MDAR0000000040", "MDAR0000000040"], "CREATED_ON": ["2001-01-01 00:00:00", "2001-01-01 00:00:00", "2001-01-01 00:00:00"], "MODIFIED_BY": ["MDAR0000000040", "MDAR0000000040", "MDAR0000000040"], "MODIFIED_ON": ["2001-01-01 00:00:00", "2001-01-01 00:00:00", "2001-01-01 00:00:00"], "LAST_TRANSFER_ON": ["2009-02-23 00:00:00", "2009-02-23 00:00:00", "2009-02-23 00:00:00"], "LAST_TRANSFER_TYPE": ["OPTW", "OPTW", "OPTW"], "WALLET_LOCK": ["N", "N", "N"], "PAYMENT_TYPE_ID": ["11", "11", "11"], "USER_ID": ["IND03B", "IND03B", "IND03B"], "MSISDN": [], "STATUS": ["Y", "Y", "Y"], "USER_TYPE": ["OPERATOR", "OPERATOR", "OPERATOR"], "PAYMENT_METHOD_TYPE_ID": ["WALLET", "WALLET", "WALLET"], "IS_PRIMARY": [], "PROVIDER_ID": ["102", "103", "104"], "MPAY_PROFILE_ID": [], "USER_GRADE": ["OPT", "OPT", "OPT"], "DESCRIPTION": ["Expense Account", "Expense Account", "Expense Account"], "WALLET_REF_ID": [], "ACCOUNT_GROUP_ID": [], "ISSUER_ID": ["520.0", "504.0", "505.0"], "DATA_LAKE_PARTITION_DATE": ["2001-01-01 00:00:00", "2001-01-01 00:00:00", "2001-01-01 00:00:00"], "RNUM": ["1", "2", "3"], "data_lake_last_modified": ["2025-06-02 01:14:45", "2025-06-02 01:14:45", "2025-06-02 01:14:45"], "source_file": ["PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-011444_chunk_0.parquet", "PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-011444_chunk_0.parquet", "PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-011444_chunk_0.parquet"], "source_year": ["2001", "2001", "2001"], "source_month": ["01", "01", "01"], "source_day": ["01", "01", "01"], "data_lake_consolidated_timestamp": ["2025-06-02 01:32:55", "2025-06-02 01:32:55", "2025-06-02 01:32:55"], "data_lake_consolidation_version": ["1.0", "1.0", "1.0"]}}}