#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script específico para analizar y corregir problemas con la tabla IMG_FLOW_42
que mostró problemas en la consolidación según el log.
"""

import os
import pandas as pd
import numpy as np
import logging
import boto3
from io import BytesIO
import sys

# Importar funciones del proyecto
from app_raw_consolidado import normalizar_tipos_datos
from tabla_config_simple import detectar_clave_primaria_tabla, validar_clave_primaria, cargar_configuracion_simple
from tabla_config_simple import TABLA_KEYS_CONFIG

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Constantes de S3
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"

def leer_parquet_desde_s3(bucket: str, key: str) -> pd.DataFrame:
    """Lee un archivo Parquet desde S3"""
    try:
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df = pd.read_parquet(buffer)
        return df
    except Exception as e:
        logging.warning(f"Error leyendo Parquet desde S3 {bucket}/{key}: {str(e)}")
        return pd.DataFrame()

def escribir_parquet_a_s3(df: pd.DataFrame, bucket: str, key: str) -> None:
    """Escribe un DataFrame como archivo Parquet en S3"""
    try:
        s3_client = boto3.client('s3')
        buffer = BytesIO()
        df.to_parquet(buffer, index=False, engine='pyarrow')
        buffer.seek(0)
        
        s3_client.put_object(
            Bucket=bucket,
            Key=key,
            Body=buffer.getvalue(),
            ContentType='application/octet-stream'
        )
        logging.info(f"Archivo Parquet escrito exitosamente: {bucket}/{key} ({len(df)} registros)")
        
    except Exception as e:
        logging.error(f"Error escribiendo Parquet a S3 {bucket}/{key}: {str(e)}")

def verificar_archivo_existe(bucket: str, key: str) -> bool:
    """Verifica si un archivo existe en S3"""
    try:
        s3_client = boto3.client('s3')
        response = s3_client.head_object(Bucket=bucket, Key=key)
        return True
    except Exception:
        return False

def analizar_img_flow_42():
    """Analiza la estructura y posibles claves primarias de IMG_FLOW_42"""
    logging.info("🔍 Analizando estructura de IMG_FLOW_42...")
    
    # Usar el archivo de RESUMEN que nos proporcionaron
    resumen_key = "RESUMEN/IMG_FLOW_42/RESUMEN_IMG_FLOW_42.parquet"
    if not verificar_archivo_existe(S3_BRONZE_BUCKET, resumen_key):
        logging.error(f"❌ Archivo de resumen no encontrado: {S3_BRONZE_BUCKET}/{resumen_key}")
        return
    
    # Leer el archivo de resumen
    df_consolidado = leer_parquet_desde_s3(S3_BRONZE_BUCKET, resumen_key)
    if df_consolidado.empty:
        logging.error("❌ Archivo de resumen vacío")
        return
    
    logging.info(f"✅ Archivo consolidado leído: {len(df_consolidado)} registros")
    
    # Mostrar información de columnas
    logging.info(f"📊 Columnas disponibles: {df_consolidado.columns.tolist()}")
    logging.info(f"📊 Tipos de datos: {df_consolidado.dtypes}")
    
    # Buscar posibles columnas de ID para usar como clave primaria
    posibles_pks = []
    
    # 1. Buscar columnas con _ID en el nombre
    columnas_id = [col for col in df_consolidado.columns if '_ID' in col]
    posibles_pks.extend(columnas_id)
    
    # 2. Buscar columnas con número en el nombre
    columnas_numero = [col for col in df_consolidado.columns if 'NUM' in col]
    posibles_pks.extend(columnas_numero)
    
    # 3. Buscar columnas con identificador en el nombre
    columnas_ident = [col for col in df_consolidado.columns if 'IDENT' in col]
    posibles_pks.extend(columnas_ident)
    
    # 4. Buscar columnas con CODE o CODIGO en el nombre
    columnas_code = [col for col in df_consolidado.columns if 'CODE' in col or 'CODIGO' in col]
    posibles_pks.extend(columnas_code)
    
    # Eliminar duplicados
    posibles_pks = list(set(posibles_pks))
    logging.info(f"📊 Posibles columnas para clave primaria: {posibles_pks}")
    
    # Analizar unicidad de cada posible clave primaria
    for col in posibles_pks:
        if col in df_consolidado.columns:
            total_registros = len(df_consolidado)
            
            # Contar valores únicos, nulos y vacíos
            valores_unicos = df_consolidado[col].nunique()
            valores_nulos = df_consolidado[col].isna().sum()
            valores_vacios = (df_consolidado[col] == '').sum() if df_consolidado[col].dtype == 'object' else 0
            
            # Calcular porcentajes
            pct_unicidad = valores_unicos / total_registros * 100
            pct_nulos = valores_nulos / total_registros * 100
            pct_vacios = valores_vacios / total_registros * 100
            
            logging.info(f"📊 {col}:")
            logging.info(f"   - Valores únicos: {valores_unicos}/{total_registros} ({pct_unicidad:.1f}%)")
            logging.info(f"   - Valores nulos: {valores_nulos}/{total_registros} ({pct_nulos:.1f}%)")
            logging.info(f"   - Valores vacíos: {valores_vacios}/{total_registros} ({pct_vacios:.1f}%)")
            
            # Verificar si es buena candidata para clave primaria
            if pct_unicidad > 95 and (pct_nulos + pct_vacios) < 5:
                logging.info(f"✅ {col} es buena candidata para clave primaria")
                recomendar_clave_primaria("IMG_FLOW_42", col)
    
    # Revisar configuración actual
    actual = TABLA_KEYS_CONFIG.get("IMG_FLOW_42", None)
    logging.info(f"🔧 Configuración actual de IMG_FLOW_42: {actual}")
    
    # Prueba de detección automática
    claves_detectadas = detectar_clave_primaria_tabla("IMG_FLOW_42", df_consolidado)
    logging.info(f"🔍 Claves detectadas automáticamente: {claves_detectadas}")
    
    return df_consolidado

def recomendar_clave_primaria(tabla: str, columna_pk: str):
    """Muestra recomendación para configurar una clave primaria"""
    config_file = os.path.join(os.path.dirname(__file__), 'config', 'tabla_primary_keys_simple.ini')
    
    logging.info(f"💡 RECOMENDACIÓN: Agregar al archivo {config_file}:")
    logging.info(f"[{tabla}]")
    logging.info(f"key = {columna_pk}")
    logging.info("")
    logging.info("Luego ejecutar:")
    logging.info("$ python3 app_raw_consolidado.py IMG_FLOW_42")

def corregir_configuracion():
    """Guarda la configuración de IMG_FLOW_42 en el archivo de configuración"""
    config_file = os.path.join(os.path.dirname(__file__), 'config', 'tabla_primary_keys_simple.ini')
    
    import configparser
    config = configparser.ConfigParser()
    
    # Cargar configuración existente si el archivo existe
    if os.path.exists(config_file):
        config.read(config_file, encoding='utf-8')
    
    # Agregar o actualizar la sección IMG_FLOW_42
    if 'IMG_FLOW_42' not in config:
        config['IMG_FLOW_42'] = {}
    
    # Asignar como clave primaria la columna elegida (se debe modificar según análisis)
    # Usar la columna más adecuada según el análisis
    config['IMG_FLOW_42']['key'] = 'CASE_ID'  # ¡MODIFICAR SEGÚN ANÁLISIS!
    
    # Guardar configuración
    with open(config_file, 'w', encoding='utf-8') as f:
        config.write(f)
    
    logging.info(f"✅ Configuración guardada exitosamente en {config_file}")
    
def main():
    print("=" * 60)
    print("🛠️  HERRAMIENTA DE ANÁLISIS Y CORRECCIÓN DE IMG_FLOW_42")
    print("=" * 60)
    
    # Paso 1: Analizar estructura de tabla
    df = analizar_img_flow_42()
    
    if df is not None and not df.empty:
        # Mostrar las primeras filas para análisis
        print("\n📊 Primeras filas del DataFrame:")
        print(df.head(5).to_string())
        
        # Mostrar estadísticas básicas
        print("\n📊 Información de tipos de datos:")
        print(df.dtypes)
        
        # Mostrar cantidad de valores nulos por columna
        print("\n📊 Valores nulos por columna:")
        null_counts = df.isna().sum()
        print(null_counts[null_counts > 0])
    
    # Si desea guardar la configuración automáticamente, descomente la siguiente línea
    # después de revisar el análisis y determinar la columna correcta:
    # corregir_configuracion()
    
    print("\n🛠️  Para aplicar las correcciones, ejecute:")
    print("$ python3 app_raw_consolidado.py IMG_FLOW_42\n")

if __name__ == "__main__":
    main()
