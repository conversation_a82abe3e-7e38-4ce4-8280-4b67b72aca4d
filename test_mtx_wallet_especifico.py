#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para probar específicamente la tabla MTX_WALLET_ORA
"""

import os
import sys
import pandas as pd
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Agregar el directorio actual al path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from tabla_config_simple import (
    detectar_clave_primaria_tabla,
    validar_clave_primaria,
    TABLA_KEYS_CONFIG
)

def probar_mtx_wallet():
    """Probar detección y validación de MTX_WALLET_ORA"""
    print("=" * 70)
    print("🧪 PRUEBA ESPECÍFICA: MTX_WALLET_ORA")
    print("=" * 70)
    
    # 1. Mostrar configuración para MTX_WALLET_ORA
    print(f"📋 Configuración cargada:")
    for tabla, claves in TABLA_KEYS_CONFIG.items():
        if 'MTX_WALLET' in tabla:
            print(f"   {tabla} → {claves}")
    
    # 2. Crear DataFrame de prueba realista
    print(f"\n📊 Creando DataFrame de prueba...")
    
    # Simulando datos reales de MTX_WALLET_ORA
    df_test = pd.DataFrame({
        'WALLET_NUMBER': ['W0001', 'W0002', 'W0003', 'W0004', 'W0005'],
        'USER_ID': [1001, 1002, 1003, 1004, 1005],
        'WALLET_TYPE': ['PRIMARY', 'SECONDARY', 'PRIMARY', 'SECONDARY', 'PRIMARY'],
        'BALANCE': [1000.50, 250.75, 0.0, 1500.25, 999.99],
        'STATUS': ['ACTIVE', 'ACTIVE', 'INACTIVE', 'ACTIVE', 'ACTIVE'],
        'CREATION_DATE': ['2024-01-15', '2024-02-20', '2024-03-10', '2024-04-05', '2024-05-12'],
        'LAST_TRANSACTION_DATE': ['2024-05-29', '2024-05-28', '2024-03-15', '2024-05-30', '2024-05-30']
    })
    
    print(f"   Filas: {len(df_test)}")
    print(f"   Columnas: {list(df_test.columns)}")
    print(f"\n   Muestra de datos:")
    print(df_test.head(3).to_string(index=False))
    
    # 3. Detectar clave primaria
    print(f"\n🔑 Detectando clave primaria...")
    claves_detectadas = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_test)
    print(f"   Resultado: {claves_detectadas}")
    
    # 4. Validar clave primaria detectada
    print(f"\n✅ Validando clave primaria...")
    if claves_detectadas:
        es_valida = validar_clave_primaria(df_test, claves_detectadas)
        print(f"   ¿Es válida?: {es_valida}")
        
        # 5. Análisis detallado de la columna clave
        if len(claves_detectadas) == 1:
            col_clave = claves_detectadas[0]
            print(f"\n🔍 Análisis detallado de {col_clave}:")
            print(f"   Valores únicos: {df_test[col_clave].nunique()}")
            print(f"   Total registros: {len(df_test)}")
            print(f"   Valores nulos: {df_test[col_clave].isna().sum()}")
            print(f"   Valores vacíos: {(df_test[col_clave].astype(str).str.strip() == '').sum()}")
            print(f"   Valores: {df_test[col_clave].tolist()}")
            
            # Unicidad
            unicidad = df_test[col_clave].nunique() / len(df_test)
            print(f"   Porcentaje unicidad: {unicidad:.1%}")
    else:
        print("   ❌ No se detectaron claves primarias")
    
    # 6. Probar con datos problemáticos
    print(f"\n🚨 Probando con datos problemáticos...")
    
    df_problematico = df_test.copy()
    # Agregar valores problemáticos
    df_problematico.loc[len(df_problematico)] = ['', 1006, 'PRIMARY', 500.0, 'ACTIVE', '2024-05-30', '2024-05-30']
    df_problematico.loc[len(df_problematico)] = [None, 1007, 'SECONDARY', 750.0, 'ACTIVE', '2024-05-30', '2024-05-30']
    df_problematico.loc[len(df_problematico)] = ['W0001', 1008, 'PRIMARY', 300.0, 'ACTIVE', '2024-05-30', '2024-05-30']  # Duplicado
    
    print(f"   DataFrame problemático: {len(df_problematico)} filas")
    
    claves_detectadas_prob = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_problematico)
    print(f"   Claves detectadas: {claves_detectadas_prob}")
    
    if claves_detectadas_prob:
        es_valida_prob = validar_clave_primaria(df_problematico, claves_detectadas_prob)
        print(f"   ¿Es válida con problemas?: {es_valida_prob}")
        
        if len(claves_detectadas_prob) == 1:
            col_clave = claves_detectadas_prob[0]
            unicidad_prob = df_problematico[col_clave].nunique() / len(df_problematico)
            print(f"   Unicidad con problemas: {unicidad_prob:.1%}")
    
    print("\n" + "=" * 70)

if __name__ == "__main__":
    probar_mtx_wallet()
