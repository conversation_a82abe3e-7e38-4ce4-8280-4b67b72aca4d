#!/usr/bin/env python3
"""
Script para diagnosticar el problema específico en la función alinear_tipos_datos_puro()
usando datos de ejemplo para simular el problema.
"""

import pandas as pd
import numpy as np
import logging
from typing import Tuple

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def crear_datos_ejemplo():
    """Crea datos de ejemplo que simulan los archivos Bronze Zone"""
    
    # DataFrame existente (archivo 1)
    df_existente = pd.DataFrame({
        'WALLET_ID': [1, 2, 3],
        'USER_ID': ['U001', 'U002', 'U003'],
        'BALANCE': [100.50, 200.00, 150.75],
        'STATUS': ['ACTIVE', 'INACTIVE', 'ACTIVE'],
        # Algunas columnas con NaN originales
        'PIN_NUMBER': ['1234', np.nan, '5678'],
        'LAST_UPDATE': pd.to_datetime(['2025-01-01', '2025-01-02', np.nan])
    })
    
    # DataFrame nuevos (archivo 2) - con COLUMNAS ADICIONALES
    df_nuevos = pd.DataFrame({
        'WALLET_ID': [4, 5],
        'USER_ID': ['U004', 'U005'],
        'BALANCE': [75.25, 300.00],
        'STATUS': ['ACTIVE', 'PENDING'],
        'PIN_NUMBER': ['9999', '1111'],
        'LAST_UPDATE': pd.to_datetime(['2025-01-03', '2025-01-04']),
        # COLUMNAS NUEVAS que no están en df_existente
        'PIN_MODIFIED_ON': pd.to_datetime(['2025-01-01', '2025-01-02']),
        'PIN_STATUS': ['ENABLED', 'DISABLED'],
        'DESCRIPTION': ['Wallet 4', 'Wallet 5']
    })
    
    return df_existente, df_nuevos

def analizar_nan_por_columna(df: pd.DataFrame, nombre: str):
    """Analiza NaN por columna en un DataFrame"""
    logging.info(f"📊 === ANÁLISIS NaN - {nombre} ===")
    total_nan = df.isna().sum().sum()
    logging.info(f"Total NaN: {total_nan}")
    
    for col in df.columns:
        nan_count = df[col].isna().sum()
        if nan_count > 0:
            logging.info(f"   {col}: {nan_count} NaN")
    return total_nan

def simular_alinear_tipos_datos_original(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Simulación EXACTA de la función problemática alinear_tipos_datos_puro()
    """
    logging.info("🔴 === SIMULANDO FUNCIÓN ORIGINAL (PROBLEMÁTICA) ===")
    
    if df_existente.empty or df_nuevos.empty:
        return df_existente, df_nuevos
    
    # Análisis inicial
    nan_existente_antes = analizar_nan_por_columna(df_existente, "EXISTENTE ANTES")
    nan_nuevos_antes = analizar_nan_por_columna(df_nuevos, "NUEVOS ANTES")
    
    # Obtener columnas comunes
    columnas_comunes = set(df_existente.columns) & set(df_nuevos.columns)
    logging.info(f"🔗 Columnas comunes: {sorted(columnas_comunes)}")
    
    df_existente_copia = df_existente.copy()
    df_nuevos_copia = df_nuevos.copy()
    
    # Alinear tipos de datos para columnas comunes
    for columna in columnas_comunes:
        tipo_existente = df_existente_copia[columna].dtype
        tipo_nuevo = df_nuevos_copia[columna].dtype
        
        if tipo_existente != tipo_nuevo:
            logging.info(f"🔄 Alineando tipos para {columna}: {tipo_existente} -> {tipo_nuevo} -> string")
            try:
                df_existente_copia[columna] = df_existente_copia[columna].astype(str)
                df_nuevos_copia[columna] = df_nuevos_copia[columna].astype(str)
            except Exception as e:
                logging.warning(f"⚠️ Error alineando tipos para {columna}: {e}")
    
    # PROBLEMA: Agregar columnas faltantes con valores por defecto
    columnas_solo_existente = set(df_existente.columns) - set(df_nuevos.columns)
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    
    logging.info(f"📝 Columnas solo en existente: {sorted(columnas_solo_existente)}")
    logging.info(f"📝 Columnas solo en nuevos: {sorted(columnas_solo_nuevos)}")
    
    for columna in columnas_solo_nuevos:
        logging.info(f"➕ Agregando columna '{columna}' al existente con valor ''")
        df_existente_copia[columna] = ''  # AQUÍ ESTÁ EL PROBLEMA
        
    for columna in columnas_solo_existente:
        logging.info(f"➕ Agregando columna '{columna}' al nuevo con valor ''")
        df_nuevos_copia[columna] = ''  # AQUÍ ESTÁ EL PROBLEMA
    
    # Análisis final
    nan_existente_despues = analizar_nan_por_columna(df_existente_copia, "EXISTENTE DESPUÉS")
    nan_nuevos_despues = analizar_nan_por_columna(df_nuevos_copia, "NUEVOS DESPUÉS")
    
    logging.info(f"🚨 CAMBIO EN NaN - Existente: +{nan_existente_despues - nan_existente_antes}")
    logging.info(f"🚨 CAMBIO EN NaN - Nuevos: +{nan_nuevos_despues - nan_nuevos_antes}")
    
    return df_existente_copia, df_nuevos_copia

def simular_alinear_tipos_datos_corregido(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Versión CORREGIDA que no introduce NaN adicionales
    """
    logging.info("🟢 === SIMULANDO FUNCIÓN CORREGIDA ===")
    
    if df_existente.empty or df_nuevos.empty:
        return df_existente, df_nuevos
    
    # Análisis inicial
    nan_existente_antes = analizar_nan_por_columna(df_existente, "EXISTENTE ANTES (CORREGIDO)")
    nan_nuevos_antes = analizar_nan_por_columna(df_nuevos, "NUEVOS ANTES (CORREGIDO)")
    
    columnas_comunes = set(df_existente.columns) & set(df_nuevos.columns)
    df_existente_copia = df_existente.copy()
    df_nuevos_copia = df_nuevos.copy()
    
    # Alinear tipos de datos para columnas comunes
    for columna in columnas_comunes:
        tipo_existente = df_existente_copia[columna].dtype
        tipo_nuevo = df_nuevos_copia[columna].dtype
        
        if tipo_existente != tipo_nuevo:
            logging.info(f"🔄 Alineando tipos para {columna}: {tipo_existente} -> {tipo_nuevo} -> string")
            try:
                df_existente_copia[columna] = df_existente_copia[columna].astype(str)
                df_nuevos_copia[columna] = df_nuevos_copia[columna].astype(str)
            except Exception as e:
                logging.warning(f"⚠️ Error alineando tipos para {columna}: {e}")
    
    # CORRECCIÓN: Agregar columnas faltantes preservando tipos apropiados
    columnas_solo_existente = set(df_existente.columns) - set(df_nuevos.columns)
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    
    for columna in columnas_solo_nuevos:
        # Inferir el tipo de datos de la columna en df_nuevos
        tipo_columna = df_nuevos_copia[columna].dtype
        logging.info(f"➕ Agregando columna '{columna}' (tipo: {tipo_columna}) al existente")
        
        if pd.api.types.is_numeric_dtype(tipo_columna):
            df_existente_copia[columna] = pd.NaType()  # Para numéricos, usar NaN apropiado
        elif pd.api.types.is_datetime64_any_dtype(tipo_columna):
            df_existente_copia[columna] = pd.NaT  # Para fechas, usar NaT
        else:
            df_existente_copia[columna] = None  # Para otros tipos, usar None
            
    for columna in columnas_solo_existente:
        # Inferir el tipo de datos de la columna en df_existente
        tipo_columna = df_existente_copia[columna].dtype
        logging.info(f"➕ Agregando columna '{columna}' (tipo: {tipo_columna}) al nuevo")
        
        if pd.api.types.is_numeric_dtype(tipo_columna):
            df_nuevos_copia[columna] = pd.NaType()
        elif pd.api.types.is_datetime64_any_dtype(tipo_columna):
            df_nuevos_copia[columna] = pd.NaT
        else:
            df_nuevos_copia[columna] = None
    
    # Análisis final
    nan_existente_despues = analizar_nan_por_columna(df_existente_copia, "EXISTENTE DESPUÉS (CORREGIDO)")
    nan_nuevos_despues = analizar_nan_por_columna(df_nuevos_copia, "NUEVOS DESPUÉS (CORREGIDO)")
    
    logging.info(f"✅ CAMBIO EN NaN - Existente: +{nan_existente_despues - nan_existente_antes}")
    logging.info(f"✅ CAMBIO EN NaN - Nuevos: +{nan_nuevos_despues - nan_nuevos_antes}")
    
    return df_existente_copia, df_nuevos_copia

def main():
    """Función principal"""
    logging.info("🚀 INICIANDO DIAGNÓSTICO CON DATOS DE EJEMPLO")
    
    # Crear datos de ejemplo
    df_existente, df_nuevos = crear_datos_ejemplo()
    
    logging.info("\n" + "="*80)
    logging.info("📋 DATOS INICIALES")
    logging.info(f"DF Existente: {df_existente.shape} - Columnas: {list(df_existente.columns)}")
    logging.info(f"DF Nuevos: {df_nuevos.shape} - Columnas: {list(df_nuevos.columns)}")
    
    # Simular función problemática
    logging.info("\n" + "="*80)
    df_existente_problema, df_nuevos_problema = simular_alinear_tipos_datos_original(df_existente, df_nuevos)
    
    # Simular función corregida
    logging.info("\n" + "="*80)
    df_existente_corregido, df_nuevos_corregido = simular_alinear_tipos_datos_corregido(df_existente, df_nuevos)
    
    # Resumen final
    logging.info("\n" + "="*80)
    logging.info("🎯 RESUMEN FINAL - COMPARACIÓN DE MÉTODOS")
    logging.info("Original vs Problemático vs Corregido:")
    logging.info(f"   Existente NaN: {df_existente.isna().sum().sum()} -> {df_existente_problema.isna().sum().sum()} -> {df_existente_corregido.isna().sum().sum()}")
    logging.info(f"   Nuevos NaN: {df_nuevos.isna().sum().sum()} -> {df_nuevos_problema.isna().sum().sum()} -> {df_nuevos_corregido.isna().sum().sum()}")
    
    # Mostrar ejemplos de las columnas agregadas
    logging.info("\n📝 EJEMPLO DE COLUMNAS AGREGADAS:")
    logging.info("Función problemática (agrega '') :")
    logging.info(f"   PIN_MODIFIED_ON en existente: {df_existente_problema['PIN_MODIFIED_ON'].iloc[0]}")
    logging.info("Función corregida (preserva tipos):")
    logging.info(f"   PIN_MODIFIED_ON en existente: {df_existente_corregido['PIN_MODIFIED_ON'].iloc[0]}")

if __name__ == "__main__":
    main()
