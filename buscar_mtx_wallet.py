#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para buscar archivos relacionados con MTX_WALLET en S3
"""

import boto3
import logging

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def buscar_mtx_wallet_en_s3():
    """
    Busca archivos relacionados con MTX_WALLET en diferentes ubicaciones de S3
    """
    
    s3_client = boto3.client('s3')
    bucket = "prd-datalake-silver-zone-637423440311"
    
    # Posibles ubicaciones y nombres
    ubicaciones_posibles = [
        "PDP_PROD10_MAINDB/MTX_WALLET_ORA/",
        "PDP_PROD10_MAINDB/MTX_WALLET/", 
        "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/",
        "PDP_PROD10_MAINDBBUS/MTX_WALLET/"
    ]
    
    logging.info(f"🔍 Buscando archivos MTX_WALLET en bucket: {bucket}")
    
    for ubicacion in ubicaciones_posibles:
        logging.info(f"📁 Verificando: s3://{bucket}/{ubicacion}")
        
        try:
            response = s3_client.list_objects_v2(
                Bucket=bucket,
                Prefix=ubicacion,
                MaxKeys=5
            )
            
            if 'Contents' in response:
                archivos = response['Contents']
                logging.info(f"✅ Encontrados {len(archivos)} archivos en {ubicacion}")
                for archivo in archivos[:3]:
                    key = archivo['Key']
                    size = archivo['Size']
                    logging.info(f"   - {key} ({size} bytes)")
                return ubicacion, archivos
            else:
                logging.info(f"❌ No se encontraron archivos en {ubicacion}")
                
        except Exception as e:
            logging.error(f"❌ Error accediendo a {ubicacion}: {str(e)}")
    
    # Buscar cualquier cosa que contenga "MTX_WALLET"
    logging.info(f"🔍 Buscando cualquier carpeta que contenga 'MTX_WALLET'...")
    
    try:
        # Listar todas las carpetas en PDP_PROD10_MAINDB
        response = s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix="PDP_PROD10_MAINDB/",
            Delimiter="/"
        )
        
        if 'CommonPrefixes' in response:
            carpetas_maindb = [prefix['Prefix'] for prefix in response['CommonPrefixes']]
            wallet_folders = [f for f in carpetas_maindb if 'WALLET' in f.upper()]
            
            if wallet_folders:
                logging.info(f"✅ Carpetas relacionadas con WALLET en PDP_PROD10_MAINDB:")
                for folder in wallet_folders:
                    logging.info(f"   - {folder}")
            else:
                logging.info(f"❌ No se encontraron carpetas con 'WALLET' en PDP_PROD10_MAINDB")
        
        # Listar todas las carpetas en PDP_PROD10_MAINDBBUS
        response = s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix="PDP_PROD10_MAINDBBUS/",
            Delimiter="/"
        )
        
        if 'CommonPrefixes' in response:
            carpetas_maindbbus = [prefix['Prefix'] for prefix in response['CommonPrefixes']]
            wallet_folders = [f for f in carpetas_maindbbus if 'WALLET' in f.upper()]
            
            if wallet_folders:
                logging.info(f"✅ Carpetas relacionadas con WALLET en PDP_PROD10_MAINDBBUS:")
                for folder in wallet_folders:
                    logging.info(f"   - {folder}")
                    
                    # Verificar contenido de la primera carpeta encontrada
                    if folder == wallet_folders[0]:
                        response_inner = s3_client.list_objects_v2(
                            Bucket=bucket,
                            Prefix=folder,
                            MaxKeys=5
                        )
                        
                        if 'Contents' in response_inner:
                            archivos = response_inner['Contents']
                            logging.info(f"   📁 Contenido de {folder} ({len(archivos)} archivos):")
                            for archivo in archivos[:3]:
                                key = archivo['Key']
                                size = archivo['Size']
                                logging.info(f"      - {key} ({size} bytes)")
                            return folder, archivos
            else:
                logging.info(f"❌ No se encontraron carpetas con 'WALLET' en PDP_PROD10_MAINDBBUS")
                
    except Exception as e:
        logging.error(f"❌ Error buscando carpetas: {str(e)}")
    
    return None, []

if __name__ == "__main__":
    logging.info("🚀 Iniciando búsqueda de MTX_WALLET...")
    
    ubicacion, archivos = buscar_mtx_wallet_en_s3()
    
    if ubicacion:
        logging.info(f"✅ MTX_WALLET encontrado en: {ubicacion}")
    else:
        logging.warning(f"❌ MTX_WALLET no encontrado en ninguna ubicación")
    
    logging.info("✅ Búsqueda completada")
