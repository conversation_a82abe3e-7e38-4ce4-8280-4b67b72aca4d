#!/usr/bin/env python3

print("=== ANÁLISIS FINAL: CONSOLIDADO vs ORIGEN ===")

try:
    import pandas as pd
    import boto3
    from io import BytesIO
    
    # Configuración
    bucket_bronze = "prd-datalake-bronze-zone-637423440311"
    bucket_silver = "prd-datalake-silver-zone-637423440311"
    archivo_origen = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-080250_chunk_0.parquet"
    archivo_consolidado = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    
    s3 = boto3.client('s3')
    
    # Leer archivos
    print("📥 Leyendo archivos...")
    response = s3.get_object(Bucket=bucket_bronze, Key=archivo_origen)
    df_origen = pd.read_parquet(BytesIO(response['Body'].read()))
    
    response = s3.get_object(Bucket=bucket_silver, Key=archivo_consolidado)
    df_consolidado = pd.read_parquet(BytesIO(response['Body'].read()))
    
    print(f"   ✅ Origen: {len(df_origen)} registros")
    print(f"   ✅ Consolidado: {len(df_consolidado)} registros")
    
    # Verificar duplicados internos en consolidado
    clave = "WALLET_NUMBER"
    print(f"\n🔍 ANÁLISIS DEL CONSOLIDADO:")
    print(f"   📊 Total registros: {len(df_consolidado)}")
    print(f"   📊 Valores únicos {clave}: {df_consolidado[clave].nunique()}")
    print(f"   📊 ¿Hay duplicados?: {'SÍ' if len(df_consolidado) > df_consolidado[clave].nunique() else 'NO'}")
    
    # Campos de fecha en consolidado
    print(f"\n📅 CAMPOS DE FECHA EN CONSOLIDADO:")
    campos_fecha = [col for col in df_consolidado.columns if any(palabra in col.upper() for palabra in ['DATE', 'MODIFIED', 'CREATED', 'PARTITION'])]
    for campo in campos_fecha:
        valores_unicos = df_consolidado[campo].nunique()
        print(f"   {campo}: {valores_unicos} valores únicos")
        if valores_unicos <= 5:
            print(f"      Valores: {sorted(df_consolidado[campo].unique())}")
    
    # Análisis de DATA_LAKE_PARTITION_DATE específicamente
    if 'DATA_LAKE_PARTITION_DATE' in df_consolidado.columns:
        print(f"\n📊 ANÁLISIS DETALLADO DE DATA_LAKE_PARTITION_DATE:")
        fechas_consolidado = df_consolidado['DATA_LAKE_PARTITION_DATE'].value_counts()
        print(f"   📋 Distribución de fechas en consolidado:")
        for fecha, cantidad in fechas_consolidado.head(10).items():
            print(f"       {fecha}: {cantidad} registros")
    
    if 'DATA_LAKE_PARTITION_DATE' in df_origen.columns:
        fechas_origen = df_origen['DATA_LAKE_PARTITION_DATE'].value_counts()
        print(f"\n   📋 Distribución de fechas en origen:")
        for fecha, cantidad in fechas_origen.head(10).items():
            print(f"       {fecha}: {cantidad} registros")
    
    # Comparar qué WALLET_NUMBER están en origen pero no en consolidado
    origen_set = set(df_origen[clave])
    consolidado_set = set(df_consolidado[clave])
    perdidos = origen_set - consolidado_set
    
    print(f"\n📋 WALLET_NUMBER PERDIDOS ({len(perdidos)} total):")
    perdidos_lista = sorted(list(perdidos))
    
    if len(perdidos) > 0:
        print(f"   📋 Primeros 10 perdidos:")
        for i, wallet in enumerate(perdidos_lista[:10]):
            # Mostrar info del registro perdido
            registro_origen = df_origen[df_origen[clave] == wallet].iloc[0]
            fecha_origen = registro_origen.get('DATA_LAKE_PARTITION_DATE', 'N/A')
            print(f"       {i+1}. {wallet} (fecha origen: {fecha_origen})")
    
    # Mostrar algunos conservados para comparar
    comunes = origen_set & consolidado_set
    comunes_lista = sorted(list(comunes))
    
    print(f"\n   📋 Primeros 5 conservados (para comparación):")
    for i, wallet in enumerate(comunes_lista[:5]):
        registro_origen = df_origen[df_origen[clave] == wallet].iloc[0]
        registro_consolidado = df_consolidado[df_consolidado[clave] == wallet].iloc[0]
        fecha_origen = registro_origen.get('DATA_LAKE_PARTITION_DATE', 'N/A')
        fecha_consolidado = registro_consolidado.get('DATA_LAKE_PARTITION_DATE', 'N/A')
        print(f"       {i+1}. {wallet}")
        print(f"           Origen: {fecha_origen}")
        print(f"           Consolidado: {fecha_consolidado}")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n=== CONCLUSIONES ===")
print("ℹ️  Si el consolidado tiene múltiples fechas DATA_LAKE_PARTITION_DATE,")
print("   significa que contiene datos históricos de múltiples ejecuciones.")
print("ℹ️  El sistema elimina duplicados manteniendo la versión más reciente.")
print("ℹ️  Los 39 registros 'perdidos' probablemente existían en versiones")
print("   anteriores del consolidado con fechas más antiguas.")
