# Modo Rápido para ETL con JDBC

## Descripción

El modo rápido es una característica que permite acelerar significativamente la ejecución de consultas SQL mediante el uso de conexiones JDBC directas a las bases de datos PostgreSQL y MySQL. Esta implementación ofrece mejor rendimiento para consultas que procesan grandes volúmenes de datos.

## Requisitos

Para utilizar el modo rápido, es necesario instalar las siguientes dependencias adicionales:

```bash
pip install jpype1>=1.4.1 jaydebeapi>=1.2.3
```

Estas dependencias ya están incluidas en el archivo `requirements.txt`.

## Activación

El modo rápido se activa a nivel de tabla en el archivo de configuración `queries_config.ini`. Para activarlo, simplemente añade la siguiente línea en la sección de la tabla que deseas procesar en modo rápido:

```ini
[NOMBRE_TABLA]
# Otras configuraciones...
modo_rapido = true  # Usar modo rápido para mejor rendimiento
```

## Características del Modo Rápido

### PostgreSQL

- Utiliza el driver JDBC de PostgreSQL (postgresql-42.6.0.jar)
- Optimiza parámetros de conexión:
  - `prepareThreshold`: 5
  - `fetchSize`: 10000
  - `autoCommit`: false
  - `tcpKeepAlive`: true
- Configura timeouts extendidos para consultas largas

### MySQL

- Utiliza el driver JDBC de MySQL (mysql-connector-j-8.2.0.jar)
- Optimiza parámetros de conexión:
  - `useServerPrepStmts`: true
  - `cachePrepStmts`: true
  - `rewriteBatchedStatements`: true
  - `useCompression`: true
  - `useCursorFetch`: true
  - `defaultFetchSize`: 10000
- Configura timeouts extendidos para consultas largas

## Procesamiento de Datos

El modo rápido procesa los resultados en chunks de 100,000 filas, lo que permite manejar conjuntos de datos muy grandes sin agotar la memoria. Además, soporta el particionamiento por fecha si se especifica una columna de rango.

## Fallback Automático

Si por alguna razón el modo rápido falla (por ejemplo, si no están instaladas las dependencias necesarias), el sistema automáticamente utilizará el modo estándar para asegurar que el proceso ETL se complete correctamente.

## Ventajas del Modo Rápido

1. **Rendimiento Mejorado**: JDBC proporciona acceso directo a la base de datos sin las capas adicionales de los drivers Python.
2. **Gestión de Memoria Eficiente**: El procesamiento por chunks y la configuración de JVM optimizada permiten manejar grandes volúmenes de datos.
3. **Conexión Estable**: Los timeouts extendidos y parámetros de conexión optimizados mejoran la estabilidad para consultas largas.
4. **Paralelismo**: La JVM puede aprovechar mejor el paralelismo a nivel de CPU para procesar datos.
5. **Carga Directa**: Los datos se procesan y cargan directamente a S3 sin etapas intermedias, reduciendo la sobrecarga.

## Consideraciones

- El modo rápido requiere más memoria debido a la JVM, por lo que es recomendable tener al menos 8GB de RAM disponible.
- Los drivers JDBC se descargan automáticamente durante la instalación, pero también pueden ser colocados manualmente en la carpeta `src/etl/driver/`.
- El modo rápido es especialmente útil para tablas con millones de registros donde el rendimiento es crítico.
