#!/usr/bin/env python3
"""
Validador de preservación de estructura completa origen → destino
Verifica que alinear_tipos_datos_puro() preserve EXACTAMENTE la estructura del origen
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime, date

# Agregar el directorio actual al path para importar la función
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar la función corregida
from app_raw_consolidado_puro import alinear_tipos_datos_puro

def crear_datos_origen_completos():
    """Crea datos de origen con todos los tipos de datos posibles"""
    
    # Simular datos del Bronze Zone (origen) con estructura variada
    datos_origen = pd.DataFrame({
        # Tipos numéricos
        'ID_TRANSACCION': [1, 2, 3, 4],  # int64
        'MONTO': [100.50, 200.75, np.nan, 300.25],  # float64 con NaN
        
        # Tipos de fecha/hora
        'FECHA_CREACION': pd.to_datetime(['2024-01-01 10:30:00', '2024-01-02 15:45:00', 
                                         '2024-01-03 08:15:00', '2024-01-04 20:22:00']),  # datetime64[ns]
        'FECHA_SOLO': pd.to_datetime(['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04']).date,  # date
        
        # Tipos texto/object
        'ISSUER_ID': ['ISSUER_A', np.nan, 'ISSUER_C', 'ISSUER_D'],  # object con NaN
        'STATUS': ['ACTIVE', 'PENDING', 'COMPLETED', 'FAILED'],  # object sin NaN
        
        # Tipos booleanos
        'ES_VALIDO': [True, False, True, np.nan],  # boolean con NaN
        
        # Tipos categóricos
        'CATEGORIA': pd.Categorical(['A', 'B', 'A', 'C']),  # category
        
        # Tipos específicos de pandas
        'OBSERVACIONES': ['Nota1', pd.NA, 'Nota3', 'Nota4'],  # object con pd.NA
    })
    
    return datos_origen

def crear_datos_consolidado_diferentes():
    """Crea datos del consolidado con tipos DIFERENTES para probar la alineación"""
    
    # Simular datos del Silver Zone (consolidado) con tipos diferentes
    datos_consolidado = pd.DataFrame({
        # Mismas columnas pero tipos diferentes
        'ID_TRANSACCION': ['1', '2', '3'],  # object en lugar de int64
        'MONTO': ['100.50', '200.75', '300.25'],  # object en lugar de float64
        'FECHA_CREACION': ['2023-12-01 09:00:00', '2023-12-02 14:30:00', '2023-12-03 16:45:00'],  # object en lugar de datetime
        'FECHA_SOLO': ['2023-12-01', '2023-12-02', '2023-12-03'],  # object en lugar de date
        'ISSUER_ID': [1.0, 2.0, 3.0],  # float64 en lugar de object
        'STATUS': [1, 2, 3],  # int64 en lugar de object
        'ES_VALIDO': ['True', 'False', 'True'],  # object en lugar de boolean
        'CATEGORIA': ['X', 'Y', 'Z'],  # object en lugar de category
        'OBSERVACIONES': [1, 2, 3],  # int64 en lugar de object
    })
    
    return datos_consolidado

def validar_preservacion_estructura():
    """Valida que la estructura del origen se preserve exactamente"""
    
    print("🔍 VALIDACIÓN DE PRESERVACIÓN DE ESTRUCTURA COMPLETA")
    print("=" * 70)
    
    # Crear datos de prueba
    df_origen = crear_datos_origen_completos()
    df_consolidado = crear_datos_consolidado_diferentes()
    
    print("📊 ESTRUCTURA DEL ORIGEN (Bronze Zone):")
    print("-" * 50)
    for col in df_origen.columns:
        dtype = df_origen[col].dtype
        nan_count = df_origen[col].isna().sum()
        print(f"  {col:20} | {str(dtype):15} | NaN: {nan_count}")
    
    print(f"\n📊 ESTRUCTURA DEL CONSOLIDADO ANTES (Silver Zone):")
    print("-" * 50)
    for col in df_consolidado.columns:
        dtype = df_consolidado[col].dtype
        nan_count = df_consolidado[col].isna().sum()
        print(f"  {col:20} | {str(dtype):15} | NaN: {nan_count}")
    
    # Aplicar la función de alineación
    print(f"\n🔧 APLICANDO alinear_tipos_datos_puro()...")
    df_consolidado_aligned, df_origen_aligned = alinear_tipos_datos_puro(df_consolidado, df_origen)
    
    print(f"\n📊 ESTRUCTURA DESPUÉS DE ALINEACIÓN:")
    print("-" * 50)
    print("CONSOLIDADO ALINEADO:")
    for col in df_consolidado_aligned.columns:
        dtype = df_consolidado_aligned[col].dtype
        nan_count = df_consolidado_aligned[col].isna().sum()
        print(f"  {col:20} | {str(dtype):15} | NaN: {nan_count}")
    
    print(f"\nORIGEN (debería mantenerse igual):")
    for col in df_origen_aligned.columns:
        dtype = df_origen_aligned[col].dtype
        nan_count = df_origen_aligned[col].isna().sum()
        print(f"  {col:20} | {str(dtype):15} | NaN: {nan_count}")
    
    # Validación crítica: verificar que el consolidado adoptó la estructura del origen
    print(f"\n🎯 VALIDACIÓN CRÍTICA - PRESERVACIÓN DE ESTRUCTURA:")
    print("=" * 70)
    
    errores = []
    exitos = []
    
    for col in df_origen.columns:
        if col in df_consolidado_aligned.columns:
            tipo_origen = df_origen[col].dtype
            tipo_consolidado_final = df_consolidado_aligned[col].dtype
            
            # Para datetime, puede haber pequeñas diferencias en la precisión
            if pd.api.types.is_datetime64_any_dtype(tipo_origen) and pd.api.types.is_datetime64_any_dtype(tipo_consolidado_final):
                exitos.append(f"✅ {col}: {tipo_origen} → {tipo_consolidado_final} (datetime preservado)")
            elif str(tipo_origen) == str(tipo_consolidado_final):
                exitos.append(f"✅ {col}: {tipo_origen} (preservado exacto)")
            else:
                errores.append(f"❌ {col}: ORIGEN={tipo_origen} ≠ CONSOLIDADO={tipo_consolidado_final}")
    
    # Mostrar resultados
    if exitos:
        print("ÉXITOS:")
        for exito in exitos:
            print(f"  {exito}")
    
    if errores:
        print(f"\nERRORES DE PRESERVACIÓN:")
        for error in errores:
            print(f"  {error}")
    
    # Verificar preservación de NaN
    print(f"\n🔍 VERIFICACIÓN DE PRESERVACIÓN DE NaN:")
    print("-" * 50)
    for col in df_origen.columns:
        if col in df_consolidado_aligned.columns:
            nan_origen = df_origen[col].isna().sum()
            nan_consolidado = df_consolidado_aligned[col].isna().sum()
            
            # Verificar que no se convirtieron NaN a 'nan' string
            if df_consolidado_aligned[col].dtype == 'object':
                string_nan_count = (df_consolidado_aligned[col] == 'nan').sum()
                if string_nan_count > 0:
                    print(f"  ❌ {col}: {string_nan_count} conversiones NaN → 'nan' string")
                else:
                    print(f"  ✅ {col}: Sin conversiones NaN → 'nan'")
    
    # Resultado final
    print(f"\n{'='*70}")
    if len(errores) == 0:
        print("🎉 ÉXITO TOTAL: Estructura del origen preservada al 100%")
        print("   📄 Bronze Zone y Silver Zone son 'como dos gotas de agua' 💧💧")
        print("   🔧 Función genérica funcionando correctamente")
        return True
    else:
        print(f"❌ FALLOS DETECTADOS: {len(errores)} columnas con estructura incorrecta")
        print("   🔧 La función necesita ajustes adicionales")
        return False

def probar_concat_final():
    """Prueba el concat final para verificar compatibilidad"""
    
    print(f"\n🔗 PRUEBA DE CONCATENACIÓN FINAL:")
    print("-" * 50)
    
    df_origen = crear_datos_origen_completos()
    df_consolidado = crear_datos_consolidado_diferentes()
    
    # Alinear
    df_consolidado_aligned, df_origen_aligned = alinear_tipos_datos_puro(df_consolidado, df_origen)
    
    try:
        # Intentar concat
        df_final = pd.concat([df_consolidado_aligned, df_origen_aligned], ignore_index=True)
        print(f"✅ Concatenación exitosa: {len(df_final)} filas totales")
        
        # Verificar estructura final
        print(f"\nEstructura final después de concat:")
        for col in df_final.columns:
            dtype = df_final[col].dtype
            nan_count = df_final[col].isna().sum()
            string_nan = (df_final[col] == 'nan').sum() if dtype == 'object' else 0
            print(f"  {col:20} | {str(dtype):15} | NaN: {nan_count:2} | 'nan': {string_nan}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en concatenación: {e}")
        return False

if __name__ == "__main__":
    # Ejecutar validaciones
    estructura_ok = validar_preservacion_estructura()
    concat_ok = probar_concat_final()
    
    print(f"\n{'='*70}")
    print(f"📊 RESUMEN FINAL:")
    print(f"  Preservación de estructura: {'✅ ÉXITO' if estructura_ok else '❌ FALLO'}")
    print(f"  Compatibilidad concat:      {'✅ ÉXITO' if concat_ok else '❌ FALLO'}")
    
    if estructura_ok and concat_ok:
        print(f"\n🎉 FUNCIÓN GENÉRICA LISTA PARA PRODUCCIÓN")
        print(f"   Funciona para cualquier tabla automáticamente")
    else:
        print(f"\n🔧 NECESITA AJUSTES ADICIONALES")
