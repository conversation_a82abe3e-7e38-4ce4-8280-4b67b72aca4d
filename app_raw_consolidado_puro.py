#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Consolidación Puro - Preserva Estructura Original
============================================================

Este script consolida archivos manteniendo la estructura original del Bronze Zone.
Principio: "Mínimas transformaciones, máxima preservación de datos originales"

Transformaciones aplicadas:
- ✅ Consolidación física (unir archivos)
- ✅ Deduplicación por clave de negocio
- ❌ NO metadatos de consolidación
- ❌ NO transformación de tipos de datos
- ❌ NO normalización agresiva
- ❌ NO limpieza de NULLs (se preservan como están)
- ❌ NO columnas adicionales (estructura IDÉNTICA)

Autor: Sistema ETL
Fecha: 2025-06-02
"""

import boto3
import pandas as pd
import numpy as np
import argparse
import logging
import warnings
from io import BytesIO
from botocore.exceptions import ClientError
import os
from typing import List, Dict, Tuple, Optional
import sys
from collections import defaultdict
import hashlib
import numpy as np
from datetime import datetime, date
import io

# Suprimir warnings de pandas
warnings.filterwarnings('ignore')

# Importar configuración simple de tablas
from tabla_config_simple import detectar_clave_primaria_tabla, validar_clave_primaria

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Constantes
S3_LANDING_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_RAW_BUCKET = "prd-datalake-silver-zone-637423440311"

def preservar_tipos_originales(df: pd.DataFrame) -> pd.DataFrame:
    """
    Preserva EXACTAMENTE la estructura original sin agregar NINGUNA columna.
    Como 2 gotas de agua: origen y destino deben ser idénticos.
    
    Args:
        df (pd.DataFrame): DataFrame original
        
    Returns:
        pd.DataFrame: DataFrame IDÉNTICO al original (mismas columnas, tipos, estructura)
    """
    if df.empty:
        return df
    
    # NO agregar NINGUNA columna de metadatos
    # NO modificar tipos de datos
    # NO agregar información adicional
    # PRESERVAR estructura EXACTAMENTE como está
    
    df_preservado = df.copy()
    
    # SOLO corregir problemas críticos que impidan la serialización Parquet
    # pero manteniendo los tipos originales cuando sea posible
    for col in df_preservado.columns:
        try:
            dtype = df_preservado[col].dtype
            
            # CORREGIDO: Manejar infinitos SOLO si realmente existen
            if np.issubdtype(dtype, np.floating):
                # Verificar si realmente hay infinitos antes de reemplazar
                has_inf = np.isinf(df_preservado[col]).any()
                if has_inf:
                    inf_count = np.isinf(df_preservado[col]).sum()
                    df_preservado[col] = df_preservado[col].replace([np.inf, -np.inf], np.nan)
                    logging.warning(f"⚠️ Columna {col}: {inf_count} valores infinitos reemplazados por NaN (requerido para Parquet)")
                else:
                    logging.debug(f"✅ Columna {col}: No contiene infinitos, preservada sin cambios")
            
            # NO convertir datetime a string - mantener tipos originales
            # NO agregar metadatos de consolidación
            # NO modificar estructura original
            
        except Exception as e:
            logging.warning(f"Error mínimo en columna {col}: {str(e)}")
    
    logging.info(f"Estructura IDÉNTICA preservada: {len(df_preservado.columns)} columnas (sin modificaciones)")
    return df_preservado

def leer_archivo_resumen(seccion_img: str) -> pd.DataFrame:
    """
    Lee el archivo de resumen Parquet de S3.
    
    Args:
        seccion_img (str): Nombre de la sección (ej: IMG_FLOW_04)
        
    Returns:
        pd.DataFrame: DataFrame con el contenido del archivo de resumen
    """
    try:
        s3_client = boto3.client('s3')
        resumen_key = f"RESUMEN/{seccion_img}/RESUMEN_{seccion_img}.parquet"
        
        try:
            response = s3_client.get_object(Bucket=S3_LANDING_BUCKET, Key=resumen_key)
            buffer = BytesIO(response['Body'].read())
            df = pd.read_parquet(buffer)
            logging.info(f"Archivo de resumen leído exitosamente: {len(df)} registros encontrados")
            return df
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                logging.warning(f"No se encontró el archivo de resumen: {resumen_key}")
                return pd.DataFrame()
            else:
                logging.warning(f"Error accediendo al archivo: {str(e)}")
                return pd.DataFrame()
            
    except Exception as e:
        logging.warning(f"Error leyendo archivo de resumen: {str(e)}")
        return pd.DataFrame()

def leer_parquet_desde_s3(bucket: str, key: str) -> pd.DataFrame:
    """
    Lee un archivo Parquet desde S3 preservando tipos originales.
    
    Args:
        bucket (str): Nombre del bucket
        key (str): Ruta del archivo
        
    Returns:
        pd.DataFrame: DataFrame con estructura original preservada
    """
    try:
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df = pd.read_parquet(buffer)
        logging.debug(f"Archivo leído preservando estructura: {bucket}/{key}")
        return df
    except Exception as e:
        logging.warning(f"Error leyendo Parquet desde S3 {bucket}/{key}: {str(e)}")
        return pd.DataFrame()

def escribir_parquet_a_s3_puro(df: pd.DataFrame, bucket: str, key: str) -> None:
    """
    Escribe un DataFrame como archivo Parquet en S3 preservando estructura.
    
    Args:
        df (pd.DataFrame): DataFrame a escribir
        bucket (str): Nombre del bucket
        key (str): Ruta donde escribir el archivo
    """
    try:
        # SOLO preservación mínima, NO normalización agresiva
        logging.info(f"Preservando estructura original antes de escribir {bucket}/{key}")
        df_preservado = preservar_tipos_originales(df)
        
        s3_client = boto3.client('s3')
        buffer = BytesIO()
        df_preservado.to_parquet(buffer, index=False, engine='pyarrow')
        buffer.seek(0)
        
        s3_client.put_object(
            Bucket=bucket,
            Key=key,
            Body=buffer.getvalue(),
            ContentType='application/octet-stream'
        )
        logging.info(f"Archivo Parquet consolidado (estructura preservada) escrito: {bucket}/{key} ({len(df_preservado)} registros)")
        
    except Exception as e:
        logging.error(f"Error escribiendo Parquet a S3 {bucket}/{key}: {str(e)}")
        raise

def generar_clave_registro_pura(row: pd.Series, tabla_path: str = "", claves_primarias: List[str] = None) -> str:
    """
    Genera una clave única para un registro preservando valores originales.
    
    Args:
        row (pd.Series): Fila del DataFrame
        tabla_path (str): Ruta de la tabla para contexto
        claves_primarias (List[str]): Lista de columnas clave primaria (opcional)
        
    Returns:
        str: Clave única del registro
    """
    # Usar claves primarias proporcionadas o detectar automáticamente
    if not claves_primarias:
        tabla_nombre = tabla_path.split('/')[-1] if tabla_path else "unknown"
        df_temp = pd.DataFrame([row])
        
        # PRIORIDAD 1: Usar configuración del archivo .ini (CORREGIDO)
        try:
            claves_primarias = detectar_clave_primaria_tabla(tabla_nombre, df_temp)
            if claves_primarias:
                logging.debug(f"🔧 CORREGIDO: Usando clave configurada en .ini: {claves_primarias}")
        except Exception as e:
            logging.warning(f"Error detectando clave desde configuración: {str(e)}")
            claves_primarias = None
        
        # FALLBACK: Detección automática solo si NO está configurado
        if not claves_primarias:
            logging.debug(f"⚠️ Fallback: tabla {tabla_nombre} no configurada, usando detección automática")
            if 'WALLET_ID' in row.index:
                claves_primarias = ['WALLET_ID']
            elif 'USER_ID' in row.index:
                claves_primarias = ['USER_ID']
            elif 'ID' in row.index:
                claves_primarias = ['ID']
    
    if claves_primarias:
        try:
            if len(claves_primarias) == 1:
                # Preservar valor original sin transformaciones
                clave_valor = str(row[claves_primarias[0]])
                if clave_valor and clave_valor not in ['nan', 'None', '']:
                    logging.debug(f"Clave primaria preservada: {claves_primarias[0]} = {clave_valor}")
                    return clave_valor
            else:
                # Clave compuesta preservando valores originales
                valores_clave = []
                for col in claves_primarias:
                    if col in row.index:
                        valor = str(row[col])
                        if valor and valor not in ['nan', 'None', '']:
                            valores_clave.append(valor)
                
                if len(valores_clave) == len(claves_primarias):
                    clave_compuesta = '|'.join(valores_clave)
                    logging.debug(f"Clave compuesta preservada: {clave_compuesta}")
                    return clave_compuesta
        except Exception as e:
            logging.warning(f"Error usando claves detectadas {claves_primarias}: {str(e)}")
    
    # Fallback: usar hash completo preservando estructura original
    logging.debug(f"Usando hash completo como clave")
    
    # Columnas de metadatos a excluir (NO incluir las columnas que YA NO agregamos)
    columnas_metadatos = [
        'DATA_LAKE_PARTITION_DATE',
        'RNUM', 
        'data_lake_last_modified'
    ]
    
    # Filtrar columnas excluyendo metadatos
    columnas_datos = [col for col in sorted(row.index) if col not in columnas_metadatos]
    
    # Crear string con los valores ORIGINALES
    valores_string = '|'.join(str(row[col]) for col in columnas_datos)
    
    # Generar hash MD5
    return hashlib.md5(valores_string.encode('utf-8')).hexdigest()

def eliminar_duplicados_puro(df: pd.DataFrame, tabla_path: str) -> pd.DataFrame:
    """
    Elimina duplicados preservando la estructura original de datos.
    Mantiene la versión más reciente basada en DATA_LAKE_PARTITION_DATE.
    
    Args:
        df (pd.DataFrame): DataFrame con posibles duplicados
        tabla_path (str): Ruta de la tabla
        
    Returns:
        pd.DataFrame: DataFrame sin duplicados, estructura preservada
    """
    if df.empty:
        return df
    
    registros_originales = len(df)
    
    # Generar claves para cada registro preservando valores originales
    df_con_claves = df.copy()
    df_con_claves['_clave_registro'] = df_con_claves.apply(
        lambda row: generar_clave_registro_pura(row, tabla_path), axis=1
    )
    
    # ORDENAR por DATA_LAKE_PARTITION_DATE (más reciente primero) antes de eliminar duplicados
    if 'DATA_LAKE_PARTITION_DATE' in df_con_claves.columns:
        try:
            # CORREGIDO: Verificar valores existentes antes de conversión
            original_null_count = df_con_claves['DATA_LAKE_PARTITION_DATE'].isnull().sum()
            
            # Convertir a datetime si no lo está para ordenar correctamente
            if df_con_claves['DATA_LAKE_PARTITION_DATE'].dtype == 'object':
                # Preservar valores originales nulos/válidos
                df_con_claves['DATA_LAKE_PARTITION_DATE'] = pd.to_datetime(
                    df_con_claves['DATA_LAKE_PARTITION_DATE'], 
                    errors='coerce'
                )
                
                # Verificar si se introdujeron nuevos NaN
                new_null_count = df_con_claves['DATA_LAKE_PARTITION_DATE'].isnull().sum()
                if new_null_count > original_null_count:
                    added_nulls = new_null_count - original_null_count
                    logging.warning(f"⚠️ CONVERSIÓN FECHA: {added_nulls} valores inválidos convertidos a NaT en DATA_LAKE_PARTITION_DATE")
                else:
                    logging.debug(f"✅ CONVERSIÓN FECHA: Sin pérdida de datos en DATA_LAKE_PARTITION_DATE")
            
            # Ordenar por fecha descendente (más reciente primero) - compatible con pandas antiguo
            df_con_claves = df_con_claves.sort_values('DATA_LAKE_PARTITION_DATE', ascending=False)
            logging.debug(f"Tabla {tabla_path}: Ordenado por DATA_LAKE_PARTITION_DATE para mantener versiones más recientes")
        except Exception as e:
            logging.warning(f"Error ordenando por DATA_LAKE_PARTITION_DATE en {tabla_path}: {str(e)}")
    
    # Eliminar duplicados por clave de negocio (manteniendo el primero = más reciente)
    df_sin_duplicados = df_con_claves.drop_duplicates(subset=['_clave_registro'], keep='first')
    
    # Remover columna temporal de clave
    df_sin_duplicados = df_sin_duplicados.drop(columns=['_clave_registro'])
    
    registros_finales = len(df_sin_duplicados)
    duplicados_eliminados = registros_originales - registros_finales
    
    if duplicados_eliminados > 0:
        logging.info(f"Tabla {tabla_path}: Eliminados {duplicados_eliminados} duplicados de {registros_originales} registros (manteniendo versiones más recientes)")
    else:
        logging.info(f"Tabla {tabla_path}: No se encontraron duplicados en {registros_originales} registros")
    
    return df_sin_duplicados

def consolidar_tabla_pura(tabla_path: str, archivos_info: List[Dict]) -> None:
    """
    Consolida una tabla específica preservando estructura original IDÉNTICA.
    
    Args:
        tabla_path (str): Ruta de la tabla (db_parent/tabla_nombre)
        archivos_info (List[Dict]): Lista de información de archivos
    """
    logging.info(f"🔄 Iniciando consolidación PURA para tabla: {tabla_path}")
    
    dataframes = []
    archivos_procesados = 0
    
    for archivo_info in archivos_info:
        try:
            # Leer archivo preservando estructura EXACTA
            ruta_archivo = archivo_info['ruta_completa']
            df_archivo = leer_parquet_desde_s3(S3_LANDING_BUCKET, ruta_archivo)
            
            if not df_archivo.empty:
                # NO agregar NINGUNA columna de metadatos
                # Mantener estructura IDÉNTICA al origen
                dataframes.append(df_archivo)
                archivos_procesados += 1
                logging.debug(f"Archivo procesado preservando estructura IDÉNTICA: {ruta_archivo}")
            else:
                logging.warning(f"Archivo vacío encontrado: {ruta_archivo}")
                
        except Exception as e:
            logging.error(f"Error procesando archivo {archivo_info.get('ruta_completa', 'unknown')}: {str(e)}")
            continue
    
    if not dataframes:
        logging.warning(f"No se pudieron procesar archivos para tabla {tabla_path}")
        return
    
    # Consolidar preservando estructura IDÉNTICA (sin modificaciones)
    logging.info(f"Uniendo {len(dataframes)} archivos preservando estructura IDÉNTICA")
    df_consolidado = pd.concat(dataframes, ignore_index=True)
    
    # SOLO eliminar duplicados, NO agregar columnas, NO modificar tipos
    df_final = eliminar_duplicados_puro(df_consolidado, tabla_path)
    
    # Escribir consolidado preservando estructura IDÉNTICA
    archivo_consolidado_key = f"{tabla_path}/consolidado_puro.parquet"
    escribir_parquet_a_s3_puro(df_final, S3_RAW_BUCKET, archivo_consolidado_key)
    
    logging.info(f"✅ Consolidación PURA completada para {tabla_path}: {len(df_final)} registros finales de {archivos_procesados} archivos (estructura IDÉNTICA preservada)")

def agrupar_archivos_por_tabla(df: pd.DataFrame) -> Dict[str, List[Dict]]:
    """
    Agrupa los archivos por db_parent y tabla_nombre.
    
    Args:
        df (pd.DataFrame): DataFrame con la información de los archivos
        
    Returns:
        Dict[str, List[Dict]]: Diccionario agrupado por tabla
    """
    grupos = defaultdict(list)
    
    for _, row in df.iterrows():
        # Clave de agrupación: db_parent/tabla_nombre
        clave_tabla = f"{row['db_parent']}/{row['tabla_nombre']}"
        
        # Información del archivo
        archivo_info = {
            'nombre_archivo': row['nombre_archivo'],
            'año': row['año'],
            'mes': row['mes'],
            'dia': row['dia'],
            'ruta_completa': f"{row['db_parent']}/{row['tabla_nombre']}/{row['año']}/{row['mes']}/{row['dia']}/{row['nombre_archivo']}"
        }
        
        grupos[clave_tabla].append(archivo_info)
    
    logging.info(f"Archivos agrupados en {len(grupos)} tablas diferentes")
    for tabla, archivos in grupos.items():
        logging.info(f"  - {tabla}: {len(archivos)} archivos")
    
    return dict(grupos)

def verificar_archivo_existe(bucket: str, key: str) -> bool:
    """
    Verifica si un archivo existe en S3.
    
    Args:
        bucket (str): Nombre del bucket
        key (str): Ruta del archivo
        
    Returns:
        bool: True si existe, False si no
    """
    try:
        s3_client = boto3.client('s3')
        s3_client.head_object(Bucket=bucket, Key=key)
        return True
    except ClientError as e:
        if e.response['Error']['Code'] == '404':
            return False
        else:
            raise e

def leer_consolidado_existente_puro(tabla_path: str) -> pd.DataFrame:
    """
    Lee el archivo consolidado existente si existe (versión pura).
    
    Args:
        tabla_path (str): Ruta de la tabla (db_parent/tabla_nombre)
        
    Returns:
        pd.DataFrame: DataFrame consolidado existente o vacío si no existe
    """
    archivo_consolidado_key = f"{tabla_path}/consolidado_puro.parquet"
    
    if verificar_archivo_existe(S3_RAW_BUCKET, archivo_consolidado_key):
        logging.info(f"🔍 Encontrado archivo consolidado existente: {archivo_consolidado_key}")
        df_existente = leer_parquet_desde_s3(S3_RAW_BUCKET, archivo_consolidado_key)
        if not df_existente.empty:
            # Solo preservar tipos originales críticos, NO agregar metadatos
            df_existente = preservar_tipos_originales(df_existente)
            logging.info(f"✅ Archivo consolidado leído preservando estructura original: {len(df_existente)} registros existentes")
            return df_existente
        else:
            logging.warning(f"⚠️ Archivo consolidado existe pero está vacío")
            return pd.DataFrame()
    else:
        logging.info(f"📝 No existe archivo consolidado previo, será primera consolidación")
        return pd.DataFrame()

def alinear_tipos_datos_puro(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Alinea los tipos de datos entre el DataFrame existente y nuevo para evitar errores en el merge (versión pura).
    
    Args:
        df_existente (pd.DataFrame): DataFrame consolidado existente
        df_nuevos (pd.DataFrame): DataFrame con nuevos datos
        
    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: DataFrames con tipos alineados
    """
    if df_existente.empty or df_nuevos.empty:
        return df_existente, df_nuevos
    
    # Obtener columnas comunes
    columnas_comunes = set(df_existente.columns) & set(df_nuevos.columns)
    
    if not columnas_comunes:
        logging.warning("⚠️ No hay columnas comunes entre DataFrames existente y nuevo")
        return df_existente, df_nuevos
    
    df_existente_copia = df_existente.copy()
    df_nuevos_copia = df_nuevos.copy()
    
    for columna in columnas_comunes:
        tipo_existente = df_existente_copia[columna].dtype
        tipo_nuevo = df_nuevos_copia[columna].dtype
        
        # LÓGICA CORREGIDA: Preservar SIEMPRE el tipo del origen (Bronze Zone)
        # El consolidado debe tener la estructura exacta del origen
        if tipo_existente != tipo_nuevo:
            logging.debug(f"Alineando tipos para columna {columna}: existente={tipo_existente} -> origen={tipo_nuevo}")
            try:
                # El existente (consolidado) debe adoptar el tipo del origen (nuevos)
                if pd.api.types.is_datetime64_any_dtype(tipo_nuevo):
                    # Si origen es datetime, convertir existente a datetime preservando NaN
                    df_existente_copia[columna] = pd.to_datetime(df_existente_copia[columna], errors='coerce')
                elif pd.api.types.is_numeric_dtype(tipo_nuevo):
                    # Si origen es numérico, convertir existente a numérico preservando NaN
                    df_existente_copia[columna] = pd.to_numeric(df_existente_copia[columna], errors='coerce')
                elif pd.api.types.is_bool_dtype(tipo_nuevo):
                    # Si origen es boolean, convertir existente a boolean
                    df_existente_copia[columna] = df_existente_copia[columna].astype('boolean')
                else:
                    # Para object y otros tipos, convertir preservando NaN como pd.NA
                    mask_nan_existente = df_existente_copia[columna].isna()
                    df_existente_copia[columna] = df_existente_copia[columna].astype(tipo_nuevo)
                    df_existente_copia.loc[mask_nan_existente, columna] = pd.NA
                
                logging.info(f"✅ Tipo alineado: {columna} adoptó tipo del origen: {tipo_nuevo}")
            except Exception as e:
                logging.warning(f"Error alineando tipos para columna {columna}: {str(e)}")
                # Fallback: usar conversión básica con preservación de NaN
                try:
                    mask_nan_existente = df_existente_copia[columna].isna()
                    df_existente_copia[columna] = df_existente_copia[columna].astype(tipo_nuevo)
                    df_existente_copia.loc[mask_nan_existente, columna] = pd.NA
                except:
                    logging.error(f"Fallback falló para columna {columna}, manteniendo tipo existente")
    
    # CORRECCIÓN CRÍTICA: Agregar columnas faltantes con valores nulos apropiados para preservar integridad de datos
    columnas_solo_existente = set(df_existente.columns) - set(df_nuevos.columns)
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    
    for columna in columnas_solo_nuevos:
        # CORRECCIÓN: Inferir el tipo de datos correcto de la columna origen y usar valor nulo apropiado
        tipo_columna = df_nuevos_copia[columna].dtype
        
        if pd.api.types.is_bool_dtype(tipo_columna):
            # Para boolean, usar None que se convierte apropiadamente
            df_existente_copia[columna] = None
        elif pd.api.types.is_datetime64_any_dtype(tipo_columna):
            # Para fechas, usar pd.NaT (Not a Time)
            df_existente_copia[columna] = pd.NaT
        elif pd.api.types.is_numeric_dtype(tipo_columna):
            # Para numéricos, usar np.nan
            df_existente_copia[columna] = np.nan
        else:
            # Para otros tipos (string, object), usar pd.NA para evitar conversión a 'nan'
            df_existente_copia[columna] = pd.NA
            
        logging.info(f"➕ Agregada columna faltante al existente: {columna} (tipo: {tipo_columna})")
    
    for columna in columnas_solo_existente:
        # CORRECCIÓN: Inferir el tipo de datos correcto de la columna origen y usar valor nulo apropiado
        tipo_columna = df_existente_copia[columna].dtype
        
        if pd.api.types.is_bool_dtype(tipo_columna):
            df_nuevos_copia[columna] = None
        elif pd.api.types.is_datetime64_any_dtype(tipo_columna):
            df_nuevos_copia[columna] = pd.NaT
        elif pd.api.types.is_numeric_dtype(tipo_columna):
            df_nuevos_copia[columna] = np.nan
        else:
            # Para otros tipos (string, object), usar pd.NA para evitar conversión a 'nan'
            df_nuevos_copia[columna] = pd.NA
            
        logging.info(f"➕ Agregada columna faltante al nuevo: {columna} (tipo: {tipo_columna})")
    
    return df_existente_copia, df_nuevos_copia

def merge_incremental_puro(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame, tabla_path: str) -> pd.DataFrame:
    """
    Realiza un merge incremental entre datos existentes y nuevos preservando estructura original.
    
    Args:
        df_existente (pd.DataFrame): Datos existentes en el consolidado
        df_nuevos (pd.DataFrame): Nuevos datos a procesar
        tabla_path (str): Ruta de la tabla para logging
        
    Returns:
        pd.DataFrame: DataFrame consolidado final con estructura idéntica
    """
    if df_existente.empty:
        logging.info(f"🆕 Primera consolidación para {tabla_path}: {len(df_nuevos)} registros nuevos")
        return df_nuevos
    
    if df_nuevos.empty:
        logging.info(f"♻️ No hay datos nuevos para {tabla_path}, manteniendo {len(df_existente)} registros existentes")
        return df_existente
    
    logging.info(f"🔄 Iniciando merge incremental PURO para {tabla_path}")
    
    # Alinear tipos de datos antes del merge
    logging.debug("🔧 Alineando tipos de datos entre DataFrames existente y nuevo")
    df_existente_alineado, df_nuevos_alineado = alinear_tipos_datos_puro(df_existente, df_nuevos)
    
    # Detectar clave primaria para esta tabla
    tabla_nombre = tabla_path.split('/')[-1]
    claves_primarias = detectar_clave_primaria_tabla(tabla_nombre, df_nuevos_alineado)
    
    # Validar clave primaria detectada
    if claves_primarias and validar_clave_primaria(df_nuevos_alineado, claves_primarias):
        logging.info(f"🔑 Usando clave primaria detectada para {tabla_path}: {claves_primarias}")
    else:
        claves_primarias = None
        logging.warning(f"⚠️ No se pudo detectar clave primaria válida para {tabla_path}, usando hash completo")
    
    # Generar claves únicas para ambos DataFrames alineados
    df_existente_copia = df_existente_alineado.copy()
    df_nuevos_copia = df_nuevos_alineado.copy()
    
    df_existente_copia['_clave_registro'] = df_existente_copia.apply(
        lambda row: generar_clave_registro_pura(row, tabla_path, claves_primarias), axis=1
    )
    df_nuevos_copia['_clave_registro'] = df_nuevos_copia.apply(
        lambda row: generar_clave_registro_pura(row, tabla_path, claves_primarias), axis=1
    )
    
    # Alinear tipos de datos entre existente y nuevos después de agregar claves
    df_existente_copia, df_nuevos_copia = alinear_tipos_datos_puro(df_existente_copia, df_nuevos_copia)
    
    # Identificar tipos de registros
    claves_existentes = set(df_existente_copia['_clave_registro'])
    claves_nuevas = set(df_nuevos_copia['_clave_registro'])
    
    # Registros que se mantienen (están en existentes pero NO en nuevos)
    claves_mantener = claves_existentes - claves_nuevas
    df_mantener = df_existente_copia[df_existente_copia['_clave_registro'].isin(claves_mantener)]
    
    # Registros completamente nuevos (están en nuevos pero NO en existentes) - INSERTAR
    claves_insertar = claves_nuevas - claves_existentes
    df_insertar = df_nuevos_copia[df_nuevos_copia['_clave_registro'].isin(claves_insertar)]
    
    # Registros que existen en ambos (misma clave) - ACTUALIZAR con versión más reciente
    claves_actualizar = claves_existentes & claves_nuevas
    df_actualizar = df_nuevos_copia[df_nuevos_copia['_clave_registro'].isin(claves_actualizar)]
    
    # Estadísticas del merge
    logging.info(f"📊 === Estadísticas de merge PURO para {tabla_path} ===")
    logging.info(f"🔑 Clave primaria utilizada: {claves_primarias if claves_primarias else 'Hash completo'}")
    logging.info(f"📦 Registros existentes: {len(df_existente_copia)}")
    logging.info(f"📥 Registros nuevos procesados: {len(df_nuevos_copia)}")
    logging.info(f"   ♻️ Registros mantenidos (sin cambios): {len(df_mantener)}")
    logging.info(f"   ➕ Registros nuevos (insertar): {len(df_insertar)}")
    logging.info(f"   🔄 Registros actualizados (reemplazar): {len(df_actualizar)}")
    
    # Combinar: mantener + insertar + actualizar (NO duplicados)
    dataframes_finales = []
    
    if not df_mantener.empty:
        dataframes_finales.append(df_mantener)
    if not df_insertar.empty:
        dataframes_finales.append(df_insertar)
    if not df_actualizar.empty:
        dataframes_finales.append(df_actualizar)
    
    if dataframes_finales:
        df_final = pd.concat(dataframes_finales, ignore_index=True)
        # Eliminar la columna auxiliar de clave para preservar estructura IDÉNTICA
        df_final.drop('_clave_registro', axis=1, inplace=True)
        
        logging.info(f"✅ Merge PURO completado: {len(df_final)} registros totales en el consolidado final")
        return df_final
    else:
        logging.warning(f"⚠️ No hay datos para consolidar en {tabla_path}")
        return pd.DataFrame()

def consolidar_archivos_nuevos_puro(tabla_path: str, archivos_info: List[Dict]) -> pd.DataFrame:
    """
    Consolida solo los archivos nuevos de una tabla (versión pura).
    
    Args:
        tabla_path (str): Ruta de la tabla (db_parent/tabla_nombre)
        archivos_info (List[Dict]): Lista con información de los archivos
        
    Returns:
        pd.DataFrame: DataFrame con los nuevos datos consolidados
    """
    dataframes = []
    archivos_leidos = 0
    archivos_fallidos = 0
    
    logging.info(f"📖 Leyendo archivos nuevos para tabla: {tabla_path}")
    
    for archivo_info in archivos_info:
        try:
            ruta_completa = archivo_info['ruta_completa']
            
            # Verificar si el archivo existe
            if not verificar_archivo_existe(S3_LANDING_BUCKET, ruta_completa):
                logging.warning(f"⚠️ Archivo no encontrado, omitiendo: {ruta_completa}")
                archivos_fallidos += 1
                continue
            
            # Leer el archivo Parquet preservando estructura EXACTA
            df = leer_parquet_desde_s3(S3_LANDING_BUCKET, ruta_completa)
            
            if not df.empty:
                # Solo preservar tipos originales críticos, NO agregar metadatos
                df = preservar_tipos_originales(df)
                
                # NO agregar columnas de metadatos - mantener datos originales únicamente
                dataframes.append(df)
                archivos_leidos += 1
                logging.debug(f"📄 Archivo leído preservando estructura: {ruta_completa} ({len(df)} registros)")
            else:
                logging.warning(f"⚠️ Archivo vacío, omitiendo: {ruta_completa}")
                archivos_fallidos += 1
                
        except Exception as e:
            logging.warning(f"❌ Error procesando archivo {archivo_info['ruta_completa']}: {str(e)}")
            archivos_fallidos += 1
            continue
    
    # Consolidar todos los DataFrames nuevos
    if dataframes:
        df_consolidado = pd.concat(dataframes, ignore_index=True)
        logging.info(f"✅ Nuevos datos consolidados para {tabla_path}: {len(df_consolidado)} registros")
        logging.info(f"   📖 Archivos leídos: {archivos_leidos}")
        logging.info(f"   ❌ Archivos fallidos: {archivos_fallidos}")

        # Eliminar duplicados preservando versión más reciente por DATA_LAKE_PARTITION_DATE
        df_consolidado = eliminar_duplicados_puro(df_consolidado, tabla_path)

        return df_consolidado
    else:
        logging.warning(f"⚠️ No se pudieron leer archivos nuevos para la tabla {tabla_path}")
        return pd.DataFrame()

def consolidar_archivos_tabla_incremental_puro(tabla_path: str, archivos_info: List[Dict]) -> pd.DataFrame:
    """
    Consolida archivos de una tabla usando lógica incremental preservando estructura original.
    
    Args:
        tabla_path (str): Ruta de la tabla (db_parent/tabla_nombre)
        archivos_info (List[Dict]): Lista con información de los archivos
        
    Returns:
        pd.DataFrame: DataFrame consolidado final con estructura idéntica
    """
    logging.info(f"🚀 Iniciando consolidación incremental PURA para tabla: {tabla_path}")
    
    # 1. Leer datos existentes (si existen)
    df_existente = leer_consolidado_existente_puro(tabla_path)
    
    # 2. Leer y consolidar nuevos datos
    df_nuevos = consolidar_archivos_nuevos_puro(tabla_path, archivos_info)
    
    # 3. Hacer merge incremental preservando estructura idéntica
    df_final = merge_incremental_puro(df_existente, df_nuevos, tabla_path)
    
    return df_final

def consolidar_tabla_pura_incremental(tabla_path: str, archivos_info: List[Dict]) -> None:
    """
    Consolida una tabla específica usando lógica incremental y preservando estructura original IDÉNTICA.
    
    Args:
        tabla_path (str): Ruta de la tabla (db_parent/tabla_nombre)
        archivos_info (List[Dict]): Lista de información de archivos
    """
    logging.info(f"🔄 Iniciando consolidación PURA INCREMENTAL para tabla: {tabla_path}")
    
    # Verificar si ya existe un consolidado
    archivo_consolidado_key = f"{tabla_path}/consolidado_puro.parquet"
    existe_previo = verificar_archivo_existe(S3_RAW_BUCKET, archivo_consolidado_key)
    
    if existe_previo:
        logging.info(f"📄 Modo: Actualización incremental (preservando datos históricos)")
    else:
        logging.info(f"🆕 Modo: Primera consolidación completa")
    
    # Procesar con lógica incremental
    df_consolidado = consolidar_archivos_tabla_incremental_puro(tabla_path, archivos_info)
    
    if not df_consolidado.empty:
        # Escribir archivo consolidado actualizado preservando estructura IDÉNTICA
        escribir_parquet_a_s3_puro(df_consolidado, S3_RAW_BUCKET, archivo_consolidado_key)
        
        logging.info(f"✅ Consolidación PURA INCREMENTAL completada para {tabla_path}")
        logging.info(f"   📊 Registros finales: {len(df_consolidado)}")
        logging.info(f"   🔍 Estructura: {len(df_consolidado.columns)} columnas (IDÉNTICA preservada)")
        logging.info(f"   💾 Archivo: {archivo_consolidado_key}")
    else:
        logging.warning(f"⚠️ No se generó archivo consolidado para tabla: {tabla_path}")

def main():
    """
    Función principal para consolidación pura.
    """
    parser = argparse.ArgumentParser(description='Consolidación Pura - Preserva Estructura Original')
    parser.add_argument('seccion', help='Nombre de la sección a consolidar (ej: IMG_FLOW_42)')
    
    args = parser.parse_args()
    
    print("🚀 Iniciando consolidación PURA preservando estructura original...")
    print("📋 Parseando argumentos...")
    print(f"✅ Argumentos parseados: {args.seccion}")
    print()
    
    print("=== Iniciando consolidación PURA de archivos ===")
    print(f"Sección: {args.seccion}")
    print(f"Bucket origen: {S3_LANDING_BUCKET}")
    print(f"Bucket destino: {S3_RAW_BUCKET}")
    print("Modo: Consolidación PURA preservando estructura original")
    
    # Leer archivo de resumen
    print(f"🔍 Verificando archivo de resumen: RESUMEN/{args.seccion}/RESUMEN_{args.seccion}.parquet")
    df_resumen = leer_archivo_resumen(args.seccion)
    
    if df_resumen.empty:
        print(f"❌ No se encontró el archivo de resumen: RESUMEN/{args.seccion}/RESUMEN_{args.seccion}.parquet")
        print("❌ No hay archivos para procesar.")
        return
    
    print(f"✅ Archivo de resumen encontrado, procediendo a leer...")
    print(f"📊 Resumen leído: {len(df_resumen)} registros")
    
    # Agrupar archivos por tabla
    print("🔄 Iniciando procesamiento de archivos preservando estructura...")
    grupos_tabla = agrupar_archivos_por_tabla(df_resumen)
    
    if not grupos_tabla:
        print("❌ No se encontraron tablas para procesar")
        return
    
    # Procesar cada tabla con lógica incremental preservando estructura
    for tabla_path, archivos_info in grupos_tabla.items():
        try:
            consolidar_tabla_pura_incremental(tabla_path, archivos_info)
        except Exception as e:
            logging.error(f"Error consolidando tabla {tabla_path}: {str(e)}")
            continue
    
    print("✅ Proceso de consolidación PURA completado exitosamente")
    print("📄 Estructura original preservada en Silver Zone")

if __name__ == "__main__":
    main()
