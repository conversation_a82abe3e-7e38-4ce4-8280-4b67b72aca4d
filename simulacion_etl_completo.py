#!/usr/bin/env python3
"""
Simulación completa del flujo ETL Bronze → Silver para MTX_WALLET_ORA
Verifica que la corrección funciona en el contexto completo del proceso.
"""

import pandas as pd
import numpy as np
import sys
import os

# Agregar el directorio actual al path para importar funciones
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar las funciones principales
from app_raw_consolidado_puro import alinear_tipos_datos_puro, merge_incremental_puro

def simular_etl_completo():
    """Simula el flujo ETL completo Bronze → Silver"""
    
    print("🏗️  SIMULACIÓN ETL COMPLETO - MTX_WALLET_ORA")
    print("=" * 70)
    
    # === FASE 1: Datos existentes en Silver Zone ===
    print("📊 FASE 1: Datos existentes en Silver Zone")
    df_silver_existente = pd.DataFrame({
        'TRANSACTION_ID': ['TXN_001', 'TXN_002', 'TXN_003', 'TXN_004'],
        'ISSUER_ID': ['BANK_A', 'BANK_B', 'BANK_C', 'BANK_D'],
        'WALLET_ID': ['WALLET_001', 'WALLET_002', 'WALLET_003', 'WALLET_004'],
        'AMOUNT': [100.50, 200.75, 300.25, 400.10],
        'CURRENCY': ['USD', 'EUR', 'USD', 'GBP'],
        'TRANSACTION_DATE': pd.to_datetime(['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04']),
        'STATUS': ['COMPLETED', 'PENDING', 'COMPLETED', 'FAILED']
    })
    
    print(f"  Registros: {len(df_silver_existente)}")
    print(f"  ISSUER_ID NaN: {df_silver_existente['ISSUER_ID'].isna().sum()}")
    print(f"  ISSUER_ID dtype: {df_silver_existente['ISSUER_ID'].dtype}")
    
    # === FASE 2: Nuevos datos desde Bronze Zone ===
    print(f"\n📥 FASE 2: Nuevos datos desde Bronze Zone")
    # Simular datos como vienen del Bronze con tipos inconsistentes y NaN reales
    df_bronze_nuevos = pd.DataFrame({
        'TRANSACTION_ID': ['TXN_005', 'TXN_006', 'TXN_007', 'TXN_008', 'TXN_009'],
        'ISSUER_ID': [np.nan, 'BANK_E', np.nan, 'BANK_F', np.nan],  # 3 NaN reales
        'WALLET_ID': ['WALLET_005', 'WALLET_006', 'WALLET_007', 'WALLET_008', 'WALLET_009'],
        'AMOUNT': [150, 250, 350, 450, 550],  # int64 vs float64 en existente
        'CURRENCY': ['USD', 'EUR', 'JPY', 'USD', 'CAD'],
        'TRANSACTION_DATE': ['2024-01-05', '2024-01-06', '2024-01-07', '2024-01-08', '2024-01-09'],  # str vs datetime
        'STATUS': ['PENDING', 'COMPLETED', 'PENDING', 'COMPLETED', 'PENDING']
    })
    
    print(f"  Registros: {len(df_bronze_nuevos)}")
    print(f"  ISSUER_ID NaN: {df_bronze_nuevos['ISSUER_ID'].isna().sum()}")
    print(f"  ISSUER_ID dtype: {df_bronze_nuevos['ISSUER_ID'].dtype}")
    print(f"  AMOUNT dtype: {df_bronze_nuevos['AMOUNT'].dtype} (vs {df_silver_existente['AMOUNT'].dtype})")
    print(f"  TRANSACTION_DATE dtype: {df_bronze_nuevos['TRANSACTION_DATE'].dtype} (vs {df_silver_existente['TRANSACTION_DATE'].dtype})")
    
    # === FASE 3: Verificación inicial ===
    def count_string_nan(df, col):
        if df[col].dtype == 'object':
            return (df[col] == 'nan').sum()
        return 0
    
    inicial_nan_silver = count_string_nan(df_silver_existente, 'ISSUER_ID')
    inicial_nan_bronze = count_string_nan(df_bronze_nuevos, 'ISSUER_ID')
    
    print(f"\n🔍 FASE 3: Verificación inicial")
    print(f"  String 'nan' en Silver: {inicial_nan_silver}")
    print(f"  String 'nan' en Bronze: {inicial_nan_bronze}")
    print(f"  Total string 'nan' inicial: {inicial_nan_silver + inicial_nan_bronze}")
    
    # === FASE 4: Alineación de tipos (FUNCIÓN CORREGIDA) ===
    print(f"\n🔧 FASE 4: Alineación de tipos de datos")
    print("  Aplicando alinear_tipos_datos_puro()...")
    
    df_silver_aligned, df_bronze_aligned = alinear_tipos_datos_puro(df_silver_existente, df_bronze_nuevos)
    
    print(f"  ✓ Alineación completada")
    print(f"  ISSUER_ID dtype silver: {df_silver_aligned['ISSUER_ID'].dtype}")
    print(f"  ISSUER_ID dtype bronze: {df_bronze_aligned['ISSUER_ID'].dtype}")
    print(f"  AMOUNT dtype silver: {df_silver_aligned['AMOUNT'].dtype}")
    print(f"  AMOUNT dtype bronze: {df_bronze_aligned['AMOUNT'].dtype}")
    
    # Verificar string 'nan' post-alineación
    post_nan_silver = count_string_nan(df_silver_aligned, 'ISSUER_ID')
    post_nan_bronze = count_string_nan(df_bronze_aligned, 'ISSUER_ID')
    
    print(f"  String 'nan' post-alineación silver: {post_nan_silver}")
    print(f"  String 'nan' post-alineación bronze: {post_nan_bronze}")
    print(f"  NaN reales preservados bronze: {df_bronze_aligned['ISSUER_ID'].isna().sum()}")
    
    # === FASE 5: Merge incremental ===
    print(f"\n🔗 FASE 5: Merge incremental")
    print("  Aplicando merge_incremental_puro()...")
    
    tabla_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/"
    df_resultado = merge_incremental_puro(df_silver_aligned, df_bronze_aligned, tabla_path)
    
    print(f"  ✓ Merge completado")
    print(f"  Registros totales: {len(df_resultado)}")
    print(f"  ISSUER_ID dtype final: {df_resultado['ISSUER_ID'].dtype}")
    print(f"  ISSUER_ID NaN count final: {df_resultado['ISSUER_ID'].isna().sum()}")
    
    # === FASE 6: Verificación final crítica ===
    final_string_nan = count_string_nan(df_resultado, 'ISSUER_ID')
    
    print(f"\n🎯 FASE 6: Verificación final crítica")
    print(f"  String 'nan' en resultado final: {final_string_nan}")
    print(f"  NaN reales en resultado final: {df_resultado['ISSUER_ID'].isna().sum()}")
    
    # Mostrar algunos valores para inspección
    print(f"\n📋 Muestra de valores ISSUER_ID en resultado:")
    for i, valor in enumerate(df_resultado['ISSUER_ID'].head(10)):
        tipo_valor = "NaN real" if pd.isna(valor) else f"'{valor}'"
        print(f"    [{i}]: {tipo_valor}")
    
    # === RESUMEN FINAL ===
    print(f"\n{'='*70}")
    print(f"📈 RESUMEN EJECUTIVO - INTEGRIDAD DE DATOS")
    print(f"{'='*70}")
    print(f"  📊 Registros procesados:")
    print(f"     • Silver existente: {len(df_silver_existente)}")
    print(f"     • Bronze nuevos: {len(df_bronze_nuevos)}")
    print(f"     • Resultado final: {len(df_resultado)}")
    
    print(f"\n  🔢 Conteo de NaN:")
    print(f"     • NaN reales iniciales (Bronze): {df_bronze_nuevos['ISSUER_ID'].isna().sum()}")
    print(f"     • NaN reales finales: {df_resultado['ISSUER_ID'].isna().sum()}")
    print(f"     • Diferencia: {df_resultado['ISSUER_ID'].isna().sum() - df_bronze_nuevos['ISSUER_ID'].isna().sum()}")
    
    print(f"\n  ⚠️  Conversiones problemáticas:")
    print(f"     • String 'nan' iniciales: {inicial_nan_silver + inicial_nan_bronze}")
    print(f"     • String 'nan' finales: {final_string_nan}")
    print(f"     • Conversiones NaN → 'nan': {final_string_nan}")
    
    # Resultado del test
    if final_string_nan == 0:
        print(f"\n✅ ÉXITO COMPLETO - PROBLEMA RESUELTO")
        print(f"   🎯 Cero conversiones de NaN a string 'nan'")
        print(f"   💧 Datos Bronze y Silver 'como dos gotas de agua'")
        print(f"   🔒 Integridad de datos preservada al 100%")
        print(f"   ✨ ETL listo para producción")
        return True
    else:
        print(f"\n❌ PROBLEMA PERSISTE")
        print(f"   🚨 {final_string_nan} conversiones NaN → 'nan' detectadas")
        print(f"   🔧 Requiere corrección adicional")
        return False

if __name__ == "__main__":
    exito = simular_etl_completo()
    sys.exit(0 if exito else 1)
