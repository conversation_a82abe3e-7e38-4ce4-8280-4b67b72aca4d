#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para analizar archivos reales específicos de MTX_WALLET_ORA
"""

import os
import sys
import pandas as pd
import boto3
import logging
from io import BytesIO
from botocore.exceptions import ClientError
import warnings

# Suprimir warnings
warnings.filterwarnings('ignore')

# Configurar logging detallado
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Agregar el directorio actual al path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from tabla_config_simple import (
    detectar_clave_primaria_tabla,
    validar_clave_primaria,
    TABLA_KEYS_CONFIG
)

def analizar_archivos_reales():
    """Analiza los archivos reales específicos mencionados"""
    
    print("\n" + "="*80)
    print("🔍 ANÁLISIS DE ARCHIVOS REALES: MTX_WALLET_ORA")
    print("="*80)
    
    # Configuración de buckets
    bucket_bronze = "prd-datalake-bronze-zone-637423440311"
    
    # Archivos específicos
    archivo_parquet = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250530-164129_chunk_0.parquet"
    archivo_resumen = "RESUMEN/IMG_FLOW_42/RESUMEN_IMG_FLOW_42.parquet"
    
    print(f"📁 Bucket Bronze: {bucket_bronze}")
    print(f"📄 Archivo Parquet: {archivo_parquet}")
    print(f"📄 Archivo Resumen: {archivo_resumen}")
    
    # 1. Analizar archivo de resumen primero
    print(f"\n🔍 ANALIZANDO ARCHIVO DE RESUMEN...")
    analizar_archivo_resumen(bucket_bronze, archivo_resumen)
    
    # 2. Analizar archivo Parquet específico
    print(f"\n🔍 ANALIZANDO ARCHIVO PARQUET ESPECÍFICO...")
    analizar_archivo_parquet(bucket_bronze, archivo_parquet)
    
    print(f"\n" + "="*80)

def analizar_archivo_resumen(bucket, key):
    """Analiza el archivo de resumen"""
    try:
        print(f"   📥 Descargando resumen desde S3...")
        s3_client = boto3.client('s3')
        
        # Verificar si existe
        try:
            s3_client.head_object(Bucket=bucket, Key=key)
            print(f"   ✅ Archivo existe en S3")
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                print(f"   ❌ Archivo NO encontrado en S3")
                return
            else:
                print(f"   ❌ Error accediendo al archivo: {str(e)}")
                return
        
        # Leer archivo
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df_resumen = pd.read_parquet(buffer)
        
        print(f"   📊 Resumen cargado: {len(df_resumen)} registros")
        print(f"   📋 Columnas: {list(df_resumen.columns)}")
        
        # Buscar MTX_WALLET_ORA en el resumen
        if 'tabla_nombre' in df_resumen.columns:
            mtx_records = df_resumen[df_resumen['tabla_nombre'] == 'MTX_WALLET_ORA']
            print(f"   🔍 Registros de MTX_WALLET_ORA en resumen: {len(mtx_records)}")
            
            if len(mtx_records) > 0:
                print(f"   📄 Archivos MTX_WALLET_ORA encontrados:")
                for _, row in mtx_records.iterrows():
                    print(f"       - {row.get('nombre_archivo', 'N/A')} ({row.get('año', 'N/A')}/{row.get('mes', 'N/A')}/{row.get('dia', 'N/A')})")
            else:
                print(f"   ⚠️ No se encontraron registros de MTX_WALLET_ORA en el resumen")
        else:
            print(f"   ⚠️ Columna 'tabla_nombre' no encontrada en resumen")
            print(f"   📋 Muestra del resumen:")
            print(df_resumen.head(3).to_string(index=False))
        
    except Exception as e:
        print(f"   ❌ Error analizando resumen: {str(e)}")

def analizar_archivo_parquet(bucket, key):
    """Analiza el archivo Parquet específico"""
    try:
        print(f"   📥 Descargando archivo Parquet desde S3...")
        s3_client = boto3.client('s3')
        
        # Verificar si existe
        try:
            s3_client.head_object(Bucket=bucket, Key=key)
            print(f"   ✅ Archivo existe en S3")
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                print(f"   ❌ Archivo NO encontrado en S3")
                return
            else:
                print(f"   ❌ Error accediendo al archivo: {str(e)}")
                return
        
        # Leer archivo
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df_mtx = pd.read_parquet(buffer)
        
        print(f"   📊 Archivo MTX_WALLET_ORA cargado: {len(df_mtx)} registros")
        print(f"   📋 Columnas disponibles: {list(df_mtx.columns)}")
        
        # Mostrar muestra de los datos
        print(f"\n   📄 MUESTRA DE DATOS (primeras 3 filas):")
        print(df_mtx.head(3).to_string(index=False))
        
        # Verificar configuración vs datos reales
        print(f"\n   🔑 ANÁLISIS DE LLAVES PRIMARIAS:")
        tabla_config = TABLA_KEYS_CONFIG.get('MTX_WALLET_ORA', [])
        print(f"       Configuración esperada: {tabla_config}")
        
        if tabla_config:
            columnas_faltantes = [col for col in tabla_config if col not in df_mtx.columns]
            columnas_presentes = [col for col in tabla_config if col in df_mtx.columns]
            
            if columnas_faltantes:
                print(f"   ❌ PROBLEMA ENCONTRADO: Columnas configuradas pero NO presentes en datos:")
                for col in columnas_faltantes:
                    print(f"       - {col} (configurada pero no existe)")
                
                print(f"\n   💡 POSIBLES COLUMNAS SIMILARES EN LOS DATOS:")
                for col_config in columnas_faltantes:
                    similares = [col for col in df_mtx.columns if col_config.lower() in col.lower() or col.lower() in col_config.lower()]
                    if similares:
                        print(f"       {col_config} → Similares: {similares}")
                    else:
                        print(f"       {col_config} → No se encontraron similares")
            
            if columnas_presentes:
                print(f"   ✅ Columnas configuradas y presentes: {columnas_presentes}")
                
                # Analizar calidad de datos en columnas clave
                for col in columnas_presentes:
                    print(f"\n   🔍 ANÁLISIS DE {col}:")
                    valores_unicos = df_mtx[col].nunique()
                    valores_nulos = df_mtx[col].isna().sum()
                    valores_vacios = (df_mtx[col].astype(str).str.strip() == '').sum()
                    total_registros = len(df_mtx)
                    
                    print(f"       Total registros: {total_registros}")
                    print(f"       Valores únicos: {valores_unicos}")
                    print(f"       Valores nulos: {valores_nulos}")
                    print(f"       Valores vacíos: {valores_vacios}")
                    print(f"       Unicidad: {(valores_unicos/total_registros)*100:.1f}%")
                    
                    # Mostrar algunos valores de ejemplo
                    valores_muestra = df_mtx[col].dropna().head(10).tolist()
                    print(f"       Muestra de valores: {valores_muestra}")
        
        # Probar detección automática
        print(f"\n   🧪 PRUEBA DE DETECCIÓN AUTOMÁTICA:")
        claves_detectadas = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_mtx)
        print(f"       Claves detectadas: {claves_detectadas}")
        
        if claves_detectadas:
            es_valida = validar_clave_primaria(df_mtx, claves_detectadas)
            print(f"       ¿Validación exitosa?: {es_valida}")
            
            if not es_valida:
                print(f"   ⚠️ VALIDACIÓN FALLÓ - Analizando el problema...")
                for col in claves_detectadas:
                    if col in df_mtx.columns:
                        # Análisis detallado del problema
                        nulos = df_mtx[col].isna().sum()
                        vacios = (df_mtx[col].astype(str).str.strip() == '').sum()
                        duplicados = len(df_mtx) - df_mtx[col].nunique()
                        
                        if nulos > 0:
                            print(f"       - {col}: {nulos} valores nulos")
                        if vacios > 0:
                            print(f"       - {col}: {vacios} valores vacíos")
                        if duplicados > 0:
                            print(f"       - {col}: {duplicados} valores duplicados")
        else:
            print(f"   ❌ No se pudieron detectar claves primarias")
    
    except Exception as e:
        print(f"   ❌ Error analizando archivo Parquet: {str(e)}")
        logging.exception("Error completo:")

if __name__ == "__main__":
    analizar_archivos_reales()
