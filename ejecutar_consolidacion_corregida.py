#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 EJECUTAR CONSOLIDACIÓN CORREGIDA: MTX_WALLET_ORA - INTEGRIDAD DE DATOS
========================================================================

Este script ejecuta la consolidación usando el sistema CORREGIDO
para verificar que ahora NO se introduzcan NaN adicionales durante la consolidación.

OBJETIVO CRÍTICO:
- Cargar archivos Bronze Zone con NaN originales: 865 + 43 = 908 NaN
- Aplicar consolidación con función alinear_tipos_datos_puro() CORREGIDA
- Verificar que el consolidado Silver Zone mantenga exactamente 908 NaN (sin agregar los 43 adicionales)
- Confirmar que se resuelve el problema de integridad de datos

CORRECCIÓN APLICADA:
- Reemplazada asignación de '' por valores nulos apropiados según tipo de datos
- None para object/string, pd.NaT para datetime, np.nan para numéricos, None para boolean
"""

import sys
import os
import boto3
import pandas as pd
import logging
from datetime import datetime
from io import BytesIO

# Configurar el path
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

from app_raw_consolidado_puro import eliminar_duplicados_puro

def ejecutar_consolidacion_corregida():
    """
    Ejecuta la consolidación con el sistema corregido
    """
    print("🚀 EJECUTANDO CONSOLIDACIÓN CORREGIDA - MTX_WALLET_ORA")
    print("=" * 70)
    print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Configuración S3
    s3_client = boto3.client('s3')
    bucket_bronze = "prd-datalake-bronze-zone-637423440311"
    bucket_silver = "prd-datalake-silver-zone-637423440311"
    
    archivo_origen = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-080250_chunk_0.parquet"
    archivo_consolidado_original = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    archivo_consolidado_nuevo = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro_SISTEMA_CORREGIDO.parquet"
    
    print("📁 PASO 1: Leer archivo origen...")
    print(f"   📍 Origen: s3://{bucket_bronze}/{archivo_origen}")
    
    try:
        # Leer archivo origen
        df_origen = pd.read_parquet(f"s3://{bucket_bronze}/{archivo_origen}", engine='pyarrow')
        print(f"   ✅ Archivo origen leído: {len(df_origen)} registros")
        
        # Verificar WALLET_NUMBER únicos en origen
        wallet_unicos_origen = df_origen['WALLET_NUMBER'].nunique()
        print(f"   📊 WALLET_NUMBER únicos en origen: {wallet_unicos_origen}")
        
        print()
        
        # PASO 2: Leer consolidado actual (problemático)
        print("📁 PASO 2: Leer consolidado actual (problemático)...")
        print(f"   📍 Consolidado actual: s3://{bucket_silver}/{archivo_consolidado_original}")
        
        try:
            df_consolidado_actual = pd.read_parquet(f"s3://{bucket_silver}/{archivo_consolidado_original}", engine='pyarrow')
            wallet_unicos_actual = df_consolidado_actual['WALLET_NUMBER'].nunique()
            print(f"   ✅ Consolidado actual leído: {len(df_consolidado_actual)} registros")
            print(f"   📊 WALLET_NUMBER únicos actual: {wallet_unicos_actual}")
            print(f"   🚨 Registros perdidos: {wallet_unicos_origen - wallet_unicos_actual}")
        except Exception as e:
            print(f"   ⚠️ No se pudo leer consolidado actual: {str(e)}")
            df_consolidado_actual = None
            wallet_unicos_actual = 0
        
        print()
        
        # PASO 3: Aplicar consolidación CORREGIDA
        print("🔧 PASO 3: Aplicar consolidación CORREGIDA...")
        print("   🔧 Usando sistema corregido que respeta WALLET_NUMBER como clave primaria")
        
        # Aplicar la función corregida
        tabla_path = "MTX_WALLET_ORA"  # Nombre para detección de clave primaria
        df_consolidado_corregido = eliminar_duplicados_puro(df_origen, tabla_path)
        
        wallet_unicos_corregido = df_consolidado_corregido['WALLET_NUMBER'].nunique()
        
        print(f"   ✅ Consolidación corregida completada:")
        print(f"   📊 Registros resultantes: {len(df_consolidado_corregido)}")
        print(f"   📊 WALLET_NUMBER únicos: {wallet_unicos_corregido}")
        
        # Verificar resultado
        if wallet_unicos_corregido == wallet_unicos_origen:
            print("   🎯 ✅ ÉXITO: Se preservaron TODOS los registros únicos")
        else:
            perdidos = wallet_unicos_origen - wallet_unicos_corregido
            print(f"   ❌ PROBLEMA: Aún se perdieron {perdidos} registros únicos")
        
        print()
        
        # PASO 4: Guardar resultado corregido
        print("💾 PASO 4: Guardar consolidado corregido...")
        print(f"   📍 Destino: s3://{bucket_silver}/{archivo_consolidado_nuevo}")
        
        # Convertir a parquet y subir
        buffer = BytesIO()
        df_consolidado_corregido.to_parquet(buffer, engine='pyarrow', index=False)
        buffer.seek(0)
        
        s3_client.upload_fileobj(
            buffer, 
            bucket_silver, 
            archivo_consolidado_nuevo,
            ExtraArgs={'ContentType': 'application/octet-stream'}
        )
        
        print(f"   ✅ Archivo guardado exitosamente")
        
        print()
        
        # PASO 5: Resumen comparativo
        print("📋 PASO 5: RESUMEN COMPARATIVO")
        print("=" * 50)
        print(f"{'Métrica':<30} {'Origen':<10} {'Actual':<10} {'Corregido':<12} {'Estado'}")
        print("-" * 72)
        print(f"{'Total registros':<30} {len(df_origen):<10} {len(df_consolidado_actual) if df_consolidado_actual is not None else 'N/A':<10} {len(df_consolidado_corregido):<12} {'✅' if len(df_consolidado_corregido) >= len(df_origen) else '⚠️'}")
        print(f"{'WALLET_NUMBER únicos':<30} {wallet_unicos_origen:<10} {wallet_unicos_actual:<10} {wallet_unicos_corregido:<12} {'✅' if wallet_unicos_corregido == wallet_unicos_origen else '❌'}")
        
        if df_consolidado_actual is not None:
            recuperados = wallet_unicos_corregido - wallet_unicos_actual
            print(f"{'Registros recuperados':<30} {'-':<10} {'-':<10} {'+' + str(recuperados):<12} {'🎯' if recuperados > 0 else '➖'}")
        
        print()
        
        # RESULTADO FINAL
        if wallet_unicos_corregido == wallet_unicos_origen:
            print("🎯 ✅ CONSOLIDACIÓN CORREGIDA EXITOSA")
            print("   - Sistema corregido funciona correctamente")
            print("   - Se preservan TODOS los registros únicos")
            print("   - Bug crítico eliminado")
            print()
            print("📝 PRÓXIMOS PASOS:")
            print("   1. Validar el archivo corregido en S3")
            print("   2. Reemplazar el consolidado_puro.parquet original")
            print("   3. Monitorear futuras ejecuciones")
        else:
            print("❌ AÚN HAY PROBLEMAS EN LA CONSOLIDACIÓN")
            print("   - Se requiere investigación adicional")
            print("   - Verificar configuración de claves primarias")
    
    except Exception as e:
        print(f"❌ Error durante la consolidación: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print()
    print(f"⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    ejecutar_consolidacion_corregida()
