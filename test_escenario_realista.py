#!/usr/bin/env python3
"""
Prueba realista del escenario MTX_WALLET_ORA con diferentes tipos de datos.
Simula el caso real donde ISSUER_ID era float64 en un DataFrame y object en otro.
"""

import pandas as pd
import numpy as np
import sys
import os

# Agregar el directorio actual al path para importar la función
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar la función corregida
from app_raw_consolidado_puro import alinear_tipos_datos_puro

def test_escenario_realista():
    """Prueba el escenario realista de MTX_WALLET_ORA"""
    
    print("🎯 PRUEBA REALISTA - Escenario MTX_WALLET_ORA")
    print("=" * 60)
    
    # DataFrame existente (como viene del Silver Zone)
    df_existente = pd.DataFrame({
        'TRANSACTION_ID': ['TXN_001', 'TXN_002', 'TXN_003'],
        'ISSUER_ID': ['ISSUER_A', 'ISSUER_B', 'ISSUER_C'],  # object dtype
        'AMOUNT': [100.50, 200.75, 300.25],
        'CURRENCY': ['USD', 'EUR', 'USD'],
        'STATUS': ['COMPLETED', 'PENDING', 'COMPLETED']
    })
    
    # DataFrame nuevos (como viene del Bronze Zone con NaN reales)
    # Simula el caso donde ISSUER_ID tiene NaN como object pero con valores NaN reales
    df_nuevos = pd.DataFrame({
        'TRANSACTION_ID': ['TXN_004', 'TXN_005', 'TXN_006', 'TXN_007'],
        'ISSUER_ID': [np.nan, 'ISSUER_D', np.nan, 'ISSUER_E'],  # object con NaN reales
        'AMOUNT': [150, 250, 350, 450],  # int64 - tipo diferente
        'CURRENCY': ['GBP', 'USD', 'EUR', 'USD'],
        'STATUS': ['PENDING', 'COMPLETED', 'FAILED', 'PENDING']
    })
    
    print("📊 DATOS DE ENTRADA (Simulando Bronze → Silver):")
    print(f"\ndf_existente (Silver Zone actual):")
    print(f"  Filas: {len(df_existente)}")
    print(f"  ISSUER_ID dtype: {df_existente['ISSUER_ID'].dtype}")
    print(f"  ISSUER_ID NaN count: {df_existente['ISSUER_ID'].isna().sum()}")
    print(f"  AMOUNT dtype: {df_existente['AMOUNT'].dtype}")
    
    print(f"\ndf_nuevos (Bronze Zone incoming):")
    print(f"  Filas: {len(df_nuevos)}")
    print(f"  ISSUER_ID dtype: {df_nuevos['ISSUER_ID'].dtype}")
    print(f"  ISSUER_ID NaN count: {df_nuevos['ISSUER_ID'].isna().sum()}")
    print(f"  AMOUNT dtype: {df_nuevos['AMOUNT'].dtype}")
    print(f"  ISSUER_ID values: {df_nuevos['ISSUER_ID'].tolist()}")
    
    # Verificar estado inicial
    def check_string_nan(df, col_name, label):
        """Verifica presencia de string 'nan' en una columna"""
        if df[col_name].dtype == 'object':
            string_nan_count = (df[col_name] == 'nan').sum()
            print(f"    {label} - String 'nan' count: {string_nan_count}")
            return string_nan_count
        return 0
    
    print(f"\n🔍 ESTADO INICIAL:")
    inicial_existente = check_string_nan(df_existente, 'ISSUER_ID', 'df_existente')
    inicial_nuevos = check_string_nan(df_nuevos, 'ISSUER_ID', 'df_nuevos')
    
    # Aplicar la función de alineación
    print(f"\n🔧 APLICANDO ALINEACIÓN DE TIPOS...")
    df_existente_aligned, df_nuevos_aligned = alinear_tipos_datos_puro(df_existente, df_nuevos)
    
    print(f"\n📈 DESPUÉS DE ALINEACIÓN:")
    print(f"  df_existente ISSUER_ID dtype: {df_existente_aligned['ISSUER_ID'].dtype}")
    print(f"  df_nuevos ISSUER_ID dtype: {df_nuevos_aligned['ISSUER_ID'].dtype}")
    print(f"  df_existente AMOUNT dtype: {df_existente_aligned['AMOUNT'].dtype}")
    print(f"  df_nuevos AMOUNT dtype: {df_nuevos_aligned['AMOUNT'].dtype}")
    
    # Verificar string 'nan' después de alineación
    print(f"\n🎯 VERIFICACIÓN POST-ALINEACIÓN:")
    post_existente = check_string_nan(df_existente_aligned, 'ISSUER_ID', 'df_existente_aligned')
    post_nuevos = check_string_nan(df_nuevos_aligned, 'ISSUER_ID', 'df_nuevos_aligned')
    
    print(f"  ISSUER_ID valores df_nuevos_aligned: {df_nuevos_aligned['ISSUER_ID'].tolist()}")
    
    # Simular el concat final del ETL
    print(f"\n🔗 SIMULANDO pd.concat() FINAL:")
    df_final = pd.concat([df_existente_aligned, df_nuevos_aligned], ignore_index=True)
    
    print(f"  Filas totales: {len(df_final)}")
    print(f"  ISSUER_ID dtype final: {df_final['ISSUER_ID'].dtype}")
    print(f"  ISSUER_ID NaN count final: {df_final['ISSUER_ID'].isna().sum()}")
    
    final_string_nan = check_string_nan(df_final, 'ISSUER_ID', 'df_final')
    
    print(f"\n{'='*60}")
    print(f"📊 RESUMEN FINAL:")
    print(f"  String 'nan' inicial: {inicial_existente + inicial_nuevos}")
    print(f"  String 'nan' post-alineación: {post_existente + post_nuevos}")
    print(f"  String 'nan' final: {final_string_nan}")
    print(f"  NaN reales preservados: {df_final['ISSUER_ID'].isna().sum()}")
    
    if final_string_nan == 0:
        print(f"\n✅ ÉXITO TOTAL!")
        print(f"   ✓ No hay conversiones NaN → 'nan'")
        print(f"   ✓ Integridad de datos preservada")
        print(f"   ✓ Bronze Zone y Silver Zone 'como dos gotas de agua' 💧💧")
        return True
    else:
        print(f"\n❌ PROBLEMA DETECTADO!")
        print(f"   ✗ Se encontraron {final_string_nan} conversiones NaN → 'nan'")
        return False

if __name__ == "__main__":
    test_escenario_realista()
