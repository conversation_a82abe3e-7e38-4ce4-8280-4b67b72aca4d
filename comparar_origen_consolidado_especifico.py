#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para comparar específicamente los archivos origen vs consolidado de MTX_WALLET_ORA
y detectar pérdida de datos en las columnas
"""

import os
import sys
import pandas as pd
import boto3
import logging
from io import BytesIO
from botocore.exceptions import ClientError
import warnings

# Suprimir warnings
warnings.filterwarnings('ignore')

# Configurar logging detallado
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def comparar_archivos_mtx_wallet():
    """Compara el archivo origen vs consolidado específico de MTX_WALLET_ORA"""
    
    print("\n" + "="*80)
    print("🔍 COMPARACIÓN DETALLADA: ORIGEN vs CONSOLIDADO MTX_WALLET_ORA")
    print("="*80)
    
    # Archivos específicos
    bucket_bronze = "prd-datalake-bronze-zone-637423440311"
    bucket_silver = "prd-datalake-silver-zone-637423440311"
    
    archivo_origen = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250530-164129_chunk_0.parquet"
    archivo_consolidado = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado.parquet"
    
    print(f"📁 Archivo Origen: s3://{bucket_bronze}/{archivo_origen}")
    print(f"📁 Archivo Consolidado: s3://{bucket_silver}/{archivo_consolidado}")
    
    # Cargar datos
    df_origen = cargar_archivo_s3(bucket_bronze, archivo_origen, "ORIGEN")
    df_consolidado = cargar_archivo_s3(bucket_silver, archivo_consolidado, "CONSOLIDADO")
    
    if df_origen is not None and df_consolidado is not None:
        print(f"\n🔍 ANÁLISIS COMPARATIVO:")
        analizar_diferencias(df_origen, df_consolidado)
    else:
        print(f"❌ No se pudieron cargar ambos archivos para comparar")

def cargar_archivo_s3(bucket, key, tipo):
    """Carga un archivo desde S3"""
    try:
        print(f"\n📥 Cargando archivo {tipo}...")
        s3_client = boto3.client('s3')
        
        # Verificar si existe
        try:
            s3_client.head_object(Bucket=bucket, Key=key)
            print(f"   ✅ Archivo {tipo} existe en S3")
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                print(f"   ❌ Archivo {tipo} NO encontrado en S3")
                return None
            else:
                print(f"   ❌ Error accediendo al archivo {tipo}: {str(e)}")
                return None
        
        # Leer archivo
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df = pd.read_parquet(buffer)
        
        print(f"   📊 {tipo} cargado: {len(df)} registros, {len(df.columns)} columnas")
        print(f"   📋 Columnas: {list(df.columns)}")
        
        return df
        
    except Exception as e:
        print(f"   ❌ Error cargando archivo {tipo}: {str(e)}")
        return None

def analizar_diferencias(df_origen, df_consolidado):
    """Analiza las diferencias entre origen y consolidado"""
    
    print(f"\n📊 RESUMEN GENERAL:")
    print(f"   Origen: {len(df_origen)} registros, {len(df_origen.columns)} columnas")
    print(f"   Consolidado: {len(df_consolidado)} registros, {len(df_consolidado.columns)} columnas")
    print(f"   Diferencia registros: {len(df_origen) - len(df_consolidado)}")
    print(f"   Diferencia columnas: {len(df_origen.columns) - len(df_consolidado.columns)}")
    
    # Comparar columnas
    print(f"\n📋 ANÁLISIS DE COLUMNAS:")
    columnas_origen = set(df_origen.columns)
    columnas_consolidado = set(df_consolidado.columns)
    
    columnas_perdidas = columnas_origen - columnas_consolidado
    columnas_nuevas = columnas_consolidado - columnas_origen
    columnas_comunes = columnas_origen & columnas_consolidado
    
    if columnas_perdidas:
        print(f"   ❌ Columnas PERDIDAS en consolidado: {sorted(columnas_perdidas)}")
    
    if columnas_nuevas:
        print(f"   ➕ Columnas NUEVAS en consolidado: {sorted(columnas_nuevas)}")
    
    print(f"   ✅ Columnas comunes: {len(columnas_comunes)}")
    
    # Analizar datos en columnas comunes
    if columnas_comunes:
        print(f"\n🔍 ANÁLISIS DE DATOS EN COLUMNAS COMUNES:")
        
        # Buscar clave primaria probable
        clave_primaria = None
        for col in columnas_comunes:
            if 'wallet' in col.lower() or 'id' in col.lower():
                if df_origen[col].nunique() == len(df_origen):
                    clave_primaria = col
                    break
        
        if clave_primaria:
            print(f"   🔑 Usando clave primaria detectada: {clave_primaria}")
            analizar_por_clave_primaria(df_origen, df_consolidado, clave_primaria, columnas_comunes)
        else:
            print(f"   ⚠️ No se detectó clave primaria única, analizando por muestreo")
            analizar_por_muestreo(df_origen, df_consolidado, columnas_comunes)

def analizar_por_clave_primaria(df_origen, df_consolidado, clave_primaria, columnas_comunes):
    """Analiza las diferencias usando una clave primaria"""
    
    # Crear índices para comparación
    origen_indexed = df_origen.set_index(clave_primaria)
    consolidado_indexed = df_consolidado.set_index(clave_primaria)
    
    # Verificar registros perdidos
    claves_origen = set(origen_indexed.index)
    claves_consolidado = set(consolidado_indexed.index)
    
    claves_perdidas = claves_origen - claves_consolidado
    claves_nuevas = claves_consolidado - claves_origen
    claves_comunes = claves_origen & claves_consolidado
    
    print(f"\n📈 ANÁLISIS POR CLAVE PRIMARIA ({clave_primaria}):")
    print(f"   Total claves origen: {len(claves_origen)}")
    print(f"   Total claves consolidado: {len(claves_consolidado)}")
    print(f"   Claves comunes: {len(claves_comunes)}")
    
    if claves_perdidas:
        print(f"   ❌ Registros PERDIDOS: {len(claves_perdidas)}")
        print(f"       Muestra de claves perdidas: {list(claves_perdidas)[:10]}")
    
    if claves_nuevas:
        print(f"   ➕ Registros NUEVOS: {len(claves_nuevas)}")
        print(f"       Muestra de claves nuevas: {list(claves_nuevas)[:10]}")
    
    # Analizar datos en registros comunes
    if claves_comunes:
        print(f"\n🔍 ANÁLISIS DE VALORES EN REGISTROS COMUNES:")
        
        for col in sorted(columnas_comunes):
            if col == clave_primaria:
                continue
                
            analizar_columna_especifica(origen_indexed, consolidado_indexed, col, list(claves_comunes)[:100])

def analizar_columna_especifica(origen_indexed, consolidado_indexed, columna, claves_muestra):
    """Analiza una columna específica entre origen y consolidado"""
    
    try:
        # Obtener valores para la muestra
        valores_origen = origen_indexed.loc[claves_muestra, columna]
        valores_consolidado = consolidado_indexed.loc[claves_muestra, columna]
        
        # Comparar valores
        diferentes = valores_origen != valores_consolidado
        nulos_origen = valores_origen.isna()
        nulos_consolidado = valores_consolidado.isna()
        
        # Detectar cambios de null/not-null
        null_a_valor = nulos_origen & ~nulos_consolidado
        valor_a_null = ~nulos_origen & nulos_consolidado
        
        # Detectar valores vacíos
        vacios_origen = valores_origen.astype(str).str.strip() == ''
        vacios_consolidado = valores_consolidado.astype(str).str.strip() == ''
        vacio_a_valor = vacios_origen & ~vacios_consolidado
        valor_a_vacio = ~vacios_origen & vacios_consolidado
        
        total_diferentes = diferentes.sum()
        total_null_cambios = null_a_valor.sum() + valor_a_null.sum()
        total_vacio_cambios = vacio_a_valor.sum() + valor_a_vacio.sum()
        
        if total_diferentes > 0 or total_null_cambios > 0 or total_vacio_cambios > 0:
            print(f"\n   🚨 COLUMNA {columna}:")
            print(f"       Valores diferentes: {total_diferentes}/{len(claves_muestra)}")
            
            if total_null_cambios > 0:
                print(f"       Cambios null: {null_a_valor.sum()} null→valor, {valor_a_null.sum()} valor→null")
            
            if total_vacio_cambios > 0:
                print(f"       Cambios vacío: {vacio_a_valor.sum()} vacío→valor, {valor_a_vacio.sum()} valor→vacío")
            
            # Mostrar ejemplos de cambios
            if total_diferentes > 0:
                indices_diferentes = diferentes[diferentes].index[:5]
                print(f"       Ejemplos de cambios:")
                for idx in indices_diferentes:
                    orig = valores_origen.loc[idx]
                    cons = valores_consolidado.loc[idx]
                    print(f"         {idx}: '{orig}' → '{cons}'")
        else:
            print(f"   ✅ COLUMNA {columna}: Sin diferencias detectadas")
            
    except Exception as e:
        print(f"   ❌ Error analizando columna {columna}: {str(e)}")

def analizar_por_muestreo(df_origen, df_consolidado, columnas_comunes):
    """Analiza las diferencias por muestreo cuando no hay clave primaria"""
    
    print(f"\n🔍 ANÁLISIS POR MUESTREO (primeras 100 filas):")
    
    # Tomar muestra
    muestra_size = min(100, len(df_origen), len(df_consolidado))
    muestra_origen = df_origen.head(muestra_size)
    muestra_consolidado = df_consolidado.head(muestra_size)
    
    for col in sorted(columnas_comunes):
        valores_origen = muestra_origen[col]
        valores_consolidado = muestra_consolidado[col]
        
        # Estadísticas básicas
        nulos_origen = valores_origen.isna().sum()
        nulos_consolidado = valores_consolidado.isna().sum()
        
        unicos_origen = valores_origen.nunique()
        unicos_consolidado = valores_consolidado.nunique()
        
        print(f"\n   📊 COLUMNA {col}:")
        print(f"       Origen: {nulos_origen} nulos, {unicos_origen} únicos")
        print(f"       Consolidado: {nulos_consolidado} nulos, {unicos_consolidado} únicos")
        
        # Muestra de valores
        if unicos_origen > 0:
            muestra_valores_origen = valores_origen.dropna().head(3).tolist()
            print(f"       Muestra origen: {muestra_valores_origen}")
        
        if unicos_consolidado > 0:
            muestra_valores_consolidado = valores_consolidado.dropna().head(3).tolist()
            print(f"       Muestra consolidado: {muestra_valores_consolidado}")

if __name__ == "__main__":
    comparar_archivos_mtx_wallet()
