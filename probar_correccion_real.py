#!/usr/bin/env python3
"""
Script para probar la corrección aplicada ejecutando consolidación de MTX_WALLET_ORA
y verificando que ya no se introduzcan NaN adicionales.
"""

import boto3
import pandas as pd
import numpy as np
import logging
import sys
import os

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def conectar_s3():
    """Conecta a S3"""
    try:
        s3 = boto3.client('s3')
        return s3
    except Exception as e:
        logging.error(f"Error conectando a S3: {e}")
        return None

def cargar_archivo_s3(bucket: str, key: str) -> pd.DataFrame:
    """Carga un archivo parquet desde S3"""
    try:
        s3 = conectar_s3()
        if s3 is None:
            return pd.DataFrame()
        
        obj = s3.get_object(Bucket=bucket, Key=key)
        df = pd.read_parquet(obj['Body'])
        logging.info(f"✅ Cargado: {key} - {len(df)} registros, {len(df.columns)} columnas")
        return df
    except Exception as e:
        logging.error(f"❌ Error cargando {key}: {e}")
        return pd.DataFrame()

def probar_consolidacion_corregida():
    """Prueba la consolidación con la función corregida"""
    
    logging.info("🚀 PROBANDO CONSOLIDACIÓN MTX_WALLET_ORA CON CORRECCIÓN")
    
    # Cargar archivos reales de Bronze Zone
    bucket_bronze = 'prd-datalake-bronze-zone-637423440311'
    archivo1 = 'PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040917_chunk_0.parquet'
    archivo2 = 'PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2025/06/02/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040552_chunk_0.parquet'
    
    logging.info("📥 Cargando archivos Bronze Zone...")
    df1 = cargar_archivo_s3(bucket_bronze, archivo1)
    df2 = cargar_archivo_s3(bucket_bronze, archivo2)
    
    if df1.empty or df2.empty:
        logging.error("❌ No se pudieron cargar los archivos necesarios")
        return False
    
    # Contar NaN originales
    nan_df1 = df1.isna().sum().sum()
    nan_df2 = df2.isna().sum().sum()
    total_nan_original = nan_df1 + nan_df2
    
    logging.info(f"📊 NaN originales - DF1: {nan_df1}, DF2: {nan_df2}, Total: {total_nan_original}")
    
    # Importar y usar la función corregida
    try:
        from app_raw_consolidado_puro import alinear_tipos_datos_puro
        logging.info("✅ Función corregida importada exitosamente")
    except Exception as e:
        logging.error(f"❌ Error importando función corregida: {e}")
        return False
    
    # Ejecutar función corregida
    logging.info("🔧 Ejecutando función alinear_tipos_datos_puro corregida...")
    df1_alineado, df2_alineado = alinear_tipos_datos_puro(df1, df2)
    
    # Contar NaN después de alineación
    nan_df1_alineado = df1_alineado.isna().sum().sum()
    nan_df2_alineado = df2_alineado.isna().sum().sum()
    total_nan_alineado = nan_df1_alineado + nan_df2_alineado
    
    logging.info(f"📊 NaN después alineación - DF1: {nan_df1_alineado}, DF2: {nan_df2_alineado}, Total: {total_nan_alineado}")
    
    # Simular concat final
    logging.info("🔗 Ejecutando consolidación final con pd.concat()...")
    df_consolidado = pd.concat([df1_alineado, df2_alineado], ignore_index=True)
    
    nan_consolidado = df_consolidado.isna().sum().sum()
    logging.info(f"📊 NaN en consolidado final: {nan_consolidado}")
    
    # Análisis de resultados
    diferencia_nan = nan_consolidado - total_nan_original
    
    logging.info("\n" + "="*80)
    logging.info("🎯 RESULTADO DE LA PRUEBA")
    logging.info(f"   NaN originales (Bronze): {total_nan_original}")
    logging.info(f"   NaN consolidado (Silver): {nan_consolidado}")
    logging.info(f"   Diferencia: {diferencia_nan:+d}")
    
    if diferencia_nan == 0:
        logging.info("✅ ÉXITO: La corrección funciona correctamente")
        logging.info("🎯 NO se introducen NaN adicionales durante la consolidación")
        return True
    elif diferencia_nan > 0:
        logging.error(f"❌ PROBLEMA: Se siguen introduciendo {diferencia_nan} NaN adicionales")
        
        # Análisis detallado por columnas
        logging.info("\n🔍 ANÁLISIS POR COLUMNAS:")
        for col in df_consolidado.columns:
            nan_orig1 = df1[col].isna().sum() if col in df1.columns else 0
            nan_orig2 = df2[col].isna().sum() if col in df2.columns else 0
            nan_total_orig = nan_orig1 + nan_orig2
            nan_consolidado_col = df_consolidado[col].isna().sum()
            
            if nan_consolidado_col > nan_total_orig:
                diferencia_col = nan_consolidado_col - nan_total_orig
                logging.error(f"   ❌ {col}: +{diferencia_col} NaN (Orig: {nan_total_orig}, Actual: {nan_consolidado_col})")
        
        return False
    else:
        logging.warning(f"⚠️ INESPERADO: Se redujeron {abs(diferencia_nan)} NaN (esto puede ser normal)")
        return True

def main():
    """Función principal"""
    logging.info("🧪 INICIANDO PRUEBA DE CORRECCIÓN DE INTEGRIDAD DE DATOS")
    
    exito = probar_consolidacion_corregida()
    
    if exito:
        logging.info("\n🏆 MISIÓN COMPLETADA: La corrección resuelve el problema de integridad de datos")
        logging.info("✅ La función alinear_tipos_datos_puro() ya NO introduce NaN adicionales")
        logging.info("✅ La consolidación de MTX_WALLET_ORA ahora preserva la integridad de datos")
    else:
        logging.error("\n❌ MISIÓN PENDIENTE: La corrección necesita ajustes adicionales")
    
    return exito

if __name__ == "__main__":
    exito = main()
    sys.exit(0 if exito else 1)
