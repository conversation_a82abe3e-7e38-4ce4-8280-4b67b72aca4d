#!/usr/bin/env python3
"""
Script para diagnosticar el problema específico de conversión de cadenas vacías '' a NaN
durante el proceso de consolidación cuando pandas trata de convertir tipos.
"""

import pandas as pd
import numpy as np
import logging
from typing import Tuple

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def probar_conversiones_problema():
    """Prueba específicamente el problema de conversión de '' a NaN"""
    
    logging.info("🔬 === PROBANDO CONVERSIONES ESPECÍFICAS ===")
    
    # Caso 1: Columna boolean con valores ''
    logging.info("\n1️⃣ COLUMNA BOOLEAN:")
    serie_bool_original = pd.Series([True, False, True])
    serie_bool_con_vacios = pd.Series([True, False, True, '', '', ''])
    
    logging.info(f"   Original (bool): {serie_bool_original.dtype}, NaN: {serie_bool_original.isna().sum()}")
    logging.info(f"   Con vacíos (object): {serie_bool_con_vacios.dtype}, NaN: {serie_bool_con_vacios.isna().sum()}")
    
    # Intentar concat
    df1 = pd.DataFrame({'PIN_REQUIRED': serie_bool_original})
    df2 = pd.DataFrame({'PIN_REQUIRED': serie_bool_con_vacios})
    
    df_concat = pd.concat([df1, df2], ignore_index=True)
    logging.info(f"   Después de concat: {df_concat['PIN_REQUIRED'].dtype}, NaN: {df_concat['PIN_REQUIRED'].isna().sum()}")
    
    # Verificar qué pasó con los valores ''
    valores_vacios = df_concat['PIN_REQUIRED'].iloc[-3:]
    logging.info(f"   Valores que eran '': {valores_vacios.tolist()}")
    
    # Caso 2: Columna datetime con valores ''
    logging.info("\n2️⃣ COLUMNA DATETIME:")
    serie_fecha_original = pd.Series(pd.to_datetime(['2025-01-01', '2025-01-02', '2025-01-03']))
    serie_fecha_con_vacios = pd.Series(['', '', ''])
    
    logging.info(f"   Original (datetime): {serie_fecha_original.dtype}, NaN: {serie_fecha_original.isna().sum()}")
    logging.info(f"   Con vacíos (object): {serie_fecha_con_vacios.dtype}, NaN: {serie_fecha_con_vacios.isna().sum()}")
    
    df3 = pd.DataFrame({'PIN_MODIFIED_ON': serie_fecha_original})
    df4 = pd.DataFrame({'PIN_MODIFIED_ON': serie_fecha_con_vacios})
    
    df_concat_fecha = pd.concat([df3, df4], ignore_index=True)
    logging.info(f"   Después de concat: {df_concat_fecha['PIN_MODIFIED_ON'].dtype}, NaN: {df_concat_fecha['PIN_MODIFIED_ON'].isna().sum()}")
    
    valores_vacios_fecha = df_concat_fecha['PIN_MODIFIED_ON'].iloc[-3:]
    logging.info(f"   Valores que eran '': {valores_vacios_fecha.tolist()}")
    
    # Caso 3: Columna numérica con valores ''
    logging.info("\n3️⃣ COLUMNA NUMÉRICA:")
    serie_num_original = pd.Series([1.0, 2.0, 3.0])
    serie_num_con_vacios = pd.Series(['', '', ''])
    
    logging.info(f"   Original (float): {serie_num_original.dtype}, NaN: {serie_num_original.isna().sum()}")
    logging.info(f"   Con vacíos (object): {serie_num_con_vacios.dtype}, NaN: {serie_num_con_vacios.isna().sum()}")
    
    df5 = pd.DataFrame({'BALANCE': serie_num_original})
    df6 = pd.DataFrame({'BALANCE': serie_num_con_vacios})
    
    df_concat_num = pd.concat([df5, df6], ignore_index=True)
    logging.info(f"   Después de concat: {df_concat_num['BALANCE'].dtype}, NaN: {df_concat_num['BALANCE'].isna().sum()}")
    
    valores_vacios_num = df_concat_num['BALANCE'].iloc[-3:]
    logging.info(f"   Valores que eran '': {valores_vacios_num.tolist()}")

def simular_caso_real_mtx_wallet():
    """Simula exactamente lo que pasa con MTX_WALLET_ORA"""
    
    logging.info("🎯 === SIMULANDO CASO REAL MTX_WALLET ===")
    
    # DataFrame existente (sin las columnas problemáticas)
    df_existente = pd.DataFrame({
        'WALLET_ID': [1, 2, 3],
        'USER_ID': ['U001', 'U002', 'U003'],
        'BALANCE': [100.50, 200.00, 150.75],
        'PIN_NUMBER': ['1234', np.nan, '5678'],
    })
    
    # DataFrame nuevos (con las columnas problemáticas)
    df_nuevos = pd.DataFrame({
        'WALLET_ID': [4, 5],
        'USER_ID': ['U004', 'U005'],
        'BALANCE': [75.25, 300.00],
        'PIN_NUMBER': ['9999', '1111'],
        'PIN_MODIFIED_ON': pd.to_datetime(['2025-01-01', '2025-01-02']),
        'PIN_STATUS': ['ENABLED', 'DISABLED'],
        'PIN_REQUIRED': [True, False],
        'VALID_FROM_DATE': pd.to_datetime(['2024-12-01', '2024-12-02']),
        'EXPIRY_DATE': pd.to_datetime(['2025-12-01', '2025-12-02']),
        'LAST_TRANSFER_TYPE': ['DEPOSIT', 'WITHDRAWAL'],
        'DESCRIPTION': ['Wallet 4', 'Wallet 5']
    })
    
    logging.info(f"📊 NaN inicial - Existente: {df_existente.isna().sum().sum()}, Nuevos: {df_nuevos.isna().sum().sum()}")
    
    # PASO 1: Simular la función problemática alinear_tipos_datos_puro
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    logging.info(f"➕ Agregando columnas faltantes: {sorted(columnas_solo_nuevos)}")
    
    df_existente_copia = df_existente.copy()
    for columna in columnas_solo_nuevos:
        logging.info(f"   Agregando '{columna}' con valor ''")
        df_existente_copia[columna] = ''  # PROBLEMA
    
    logging.info(f"📊 Después de agregar columnas - Existente: {df_existente_copia.isna().sum().sum()}")
    
    # PASO 2: Verificar tipos de datos antes del concat
    logging.info("\n📋 TIPOS DE DATOS ANTES DE CONCAT:")
    for col in df_existente_copia.columns:
        if col in df_nuevos.columns:
            tipo_existente = df_existente_copia[col].dtype
            tipo_nuevos = df_nuevos[col].dtype
            logging.info(f"   {col}: Existente={tipo_existente}, Nuevos={tipo_nuevos}")
            
            if tipo_existente != tipo_nuevos:
                logging.warning(f"     🚨 DISCREPANCIA DE TIPOS DETECTADA")
    
    # PASO 3: Ejecutar concat y ver qué pasa
    logging.info("\n🔗 EJECUTANDO pd.concat()...")
    df_consolidado = pd.concat([df_existente_copia, df_nuevos], ignore_index=True)
    
    logging.info(f"📊 Después de concat: {df_consolidado.isna().sum().sum()} NaN totales")
    
    # PASO 4: Análisis detallado de qué columnas ganaron NaN
    logging.info("\n🔍 ANÁLISIS DETALLADO POR COLUMNAS:")
    for col in df_consolidado.columns:
        nan_existente = df_existente_copia[col].isna().sum() if col in df_existente_copia.columns else 0
        nan_nuevos = df_nuevos[col].isna().sum() if col in df_nuevos.columns else 0
        nan_consolidado = df_consolidado[col].isna().sum()
        nan_esperado = nan_existente + nan_nuevos
        
        if nan_consolidado > nan_esperado:
            diferencia = nan_consolidado - nan_esperado
            logging.warning(f"   🚨 {col}: +{diferencia} NaN (Esperado: {nan_esperado}, Real: {nan_consolidado})")
            
            # Mostrar los valores que se convirtieron en NaN
            valores_problema = df_consolidado[col].iloc[:len(df_existente_copia)]
            logging.info(f"      Valores en filas del existente: {valores_problema.tolist()}")

def probar_solucion_correcta():
    """Prueba la solución correcta usando None en lugar de ''"""
    
    logging.info("✅ === PROBANDO SOLUCIÓN CORRECTA ===")
    
    # Mismo caso pero usando None en lugar de ''
    df_existente = pd.DataFrame({
        'WALLET_ID': [1, 2, 3],
        'PIN_NUMBER': ['1234', np.nan, '5678'],
    })
    
    df_nuevos = pd.DataFrame({
        'WALLET_ID': [4, 5],
        'PIN_NUMBER': ['9999', '1111'],
        'PIN_MODIFIED_ON': pd.to_datetime(['2025-01-01', '2025-01-02']),
        'PIN_REQUIRED': [True, False],
    })
    
    logging.info(f"📊 NaN inicial - Existente: {df_existente.isna().sum().sum()}, Nuevos: {df_nuevos.isna().sum().sum()}")
    
    # SOLUCIÓN: Usar None en lugar de ''
    df_existente_copia = df_existente.copy()
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    
    for columna in columnas_solo_nuevos:
        # Inferir el tipo correcto y usar el valor nulo apropiado
        tipo_columna = df_nuevos[columna].dtype
        if pd.api.types.is_bool_dtype(tipo_columna):
            df_existente_copia[columna] = None  # Se convierte a NaN para boolean
        elif pd.api.types.is_datetime64_any_dtype(tipo_columna):
            df_existente_copia[columna] = pd.NaT  # Para fechas
        elif pd.api.types.is_numeric_dtype(tipo_columna):
            df_existente_copia[columna] = np.nan  # Para numéricos
        else:
            df_existente_copia[columna] = None  # Para otros tipos
        
        logging.info(f"   Agregando '{columna}' (tipo: {tipo_columna}) con valor nulo apropiado")
    
    # Concat con la solución correcta
    df_consolidado_correcto = pd.concat([df_existente_copia, df_nuevos], ignore_index=True)
    
    logging.info(f"📊 Con solución correcta: {df_consolidado_correcto.isna().sum().sum()} NaN totales")
    
    # Comparar con solución problemática
    df_existente_problema = df_existente.copy()
    for columna in columnas_solo_nuevos:
        df_existente_problema[columna] = ''  # Solución problemática
    
    df_consolidado_problema = pd.concat([df_existente_problema, df_nuevos], ignore_index=True)
    logging.info(f"📊 Con solución problemática: {df_consolidado_problema.isna().sum().sum()} NaN totales")
    
    diferencia = df_consolidado_problema.isna().sum().sum() - df_consolidado_correcto.isna().sum().sum()
    logging.info(f"🎯 DIFERENCIA: +{diferencia} NaN evitados con la solución correcta")

def main():
    """Función principal"""
    logging.info("🚀 DIAGNÓSTICO AVANZADO: CONVERSIÓN DE CADENAS VACÍAS A NaN")
    
    # Probar conversiones específicas
    probar_conversiones_problema()
    
    logging.info("\n" + "="*80)
    # Simular caso real
    simular_caso_real_mtx_wallet()
    
    logging.info("\n" + "="*80)
    # Probar solución correcta
    probar_solucion_correcta()

if __name__ == "__main__":
    main()
