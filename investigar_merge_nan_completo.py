#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 INVESTIGACIÓN COMPLETA: Problema NaN en Merge MTX_WALLET_ORA
=============================================================

Investigación exhaustiva del problema donde valores NaN aparecen en el 
archivo consolidado (Silver Zone) que no existen en los archivos origen (Bronze Zone).

Fecha: 2025-06-02
"""

import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Agregar ruta del sistema
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def analizar_problema_nan_completo():
    """
    Investigación completa del problema de NaN en consolidación MTX_WALLET_ORA.
    """
    print("🔍 INVESTIGACIÓN COMPLETA: Problema NaN en MTX_WALLET_ORA")
    print("="*65)
    print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Importar las funciones del sistema principal
        from app_raw_consolidado_puro import (
            leer_archivo_resumen,
            agrupar_archivos_por_tabla,
            leer_parquet_desde_s3
        )
        
        print("\n📋 PASO 1: Cargar y analizar resumen de archivos...")
        
        # Cargar resumen para IMG_FLOW_42 (donde está MTX_WALLET_ORA)
        resumen_df = leer_archivo_resumen('IMG_FLOW_42')
        if resumen_df.empty:
            print("❌ No se pudo cargar el resumen")
            return
            
        print(f"✅ Resumen cargado: {len(resumen_df)} registros")
        print(f"📋 Columnas del resumen: {list(resumen_df.columns)}")
        
        # Filtrar para MTX_WALLET_ORA
        if 'tabla_nombre' in resumen_df.columns:
            mtx_wallet_files = resumen_df[resumen_df['tabla_nombre'] == 'MTX_WALLET_ORA'].copy()
        else:
            print("❌ No se encontró columna 'tabla_nombre' en el resumen")
            return
            
        print(f"📊 Archivos MTX_WALLET_ORA en resumen: {len(mtx_wallet_files)}")
        
        if len(mtx_wallet_files) == 0:
            print("❌ No se encontraron archivos MTX_WALLET_ORA en resumen")
            return
            
        print("\n📋 PASO 2: Agrupar archivos por tabla...")
        archivos_agrupados = agrupar_archivos_por_tabla(resumen_df)
        
        # Buscar MTX_WALLET_ORA
        tabla_key = 'PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA'
        if tabla_key not in archivos_agrupados:
            print("❌ MTX_WALLET_ORA no encontrado en archivos agrupados")
            print(f"📋 Claves disponibles: {list(archivos_agrupados.keys())}")
            return
            
        archivos_mtx = archivos_agrupados[tabla_key]
        print(f"✅ MTX_WALLET_ORA encontrado: {len(archivos_mtx)} archivos")
        
        print("\n📋 PASO 3: Cargar archivo consolidado existente...")
        
        # Leer archivo consolidado desde Silver Zone
        consolidado_key = f"{tabla_key}/consolidado_puro.parquet"
        bucket_silver = 'prd-datalake-silver-zone-637423440311'
        bucket_bronze = 'prd-datalake-bronze-zone-637423440311'
        
        df_consolidado = leer_parquet_desde_s3(bucket_silver, consolidado_key)
        
        if df_consolidado.empty:
            print("❌ No se pudo cargar el archivo consolidado")
            return
            
        print(f"✅ Archivo consolidado cargado")
        print(f"   📊 Registros: {len(df_consolidado)}")
        print(f"   📋 Columnas: {len(df_consolidado.columns)}")
        
        # Analizar NaN en archivo consolidado
        nan_consolidado = df_consolidado.isnull().sum()
        cols_con_nan_consolidado = nan_consolidado[nan_consolidado > 0]
        
        print(f"   🔍 Total NaN en consolidado: {nan_consolidado.sum()}")
        print(f"   🔍 Columnas con NaN: {len(cols_con_nan_consolidado)}")
        
        if len(cols_con_nan_consolidado) > 0:
            print("   📝 Top 10 columnas con más NaN en consolidado:")
            for col, count in cols_con_nan_consolidado.head(10).items():
                porcentaje = (count / len(df_consolidado)) * 100
                print(f"      - {col}: {count} NaN ({porcentaje:.1f}%)")
        
        print("\n📋 PASO 4: Cargar y analizar archivos individuales...")
        
        dataframes_individuales = []
        total_nan_individuales = 0
        
        # Cargar archivos individuales desde Bronze Zone
        for i, archivo_info in enumerate(archivos_mtx[:5]):  # Analizar máximo 5 archivos
            key = archivo_info['ruta_completa']
            
            print(f"\n🔍 Analizando archivo individual {i+1}: {archivo_info['nombre_archivo']}")
            
            try:
                df_individual = leer_parquet_desde_s3(bucket_bronze, key)
                if not df_individual.empty:
                    print(f"   📊 Registros: {len(df_individual)}")
                    print(f"   📋 Columnas: {len(df_individual.columns)}")
                    
                    # Analizar NaN en archivo individual
                    nan_individual = df_individual.isnull().sum()
                    cols_con_nan_individual = nan_individual[nan_individual > 0]
                    total_nan_archivo = nan_individual.sum()
                    total_nan_individuales += total_nan_archivo
                    
                    print(f"   🔍 Total NaN en archivo: {total_nan_archivo}")
                    print(f"   🔍 Columnas con NaN: {len(cols_con_nan_individual)}")
                    
                    if len(cols_con_nan_individual) > 0:
                        print("   📝 Columnas con NaN en archivo individual:")
                        for col, count in cols_con_nan_individual.head(5).items():
                            porcentaje = (count / len(df_individual)) * 100
                            print(f"      - {col}: {count} NaN ({porcentaje:.1f}%)")
                    
                    dataframes_individuales.append({
                        'archivo': archivo_info['nombre_archivo'],
                        'key': key,
                        'dataframe': df_individual,
                        'nan_counts': nan_individual
                    })
                    
            except Exception as e:
                print(f"   ❌ Error cargando archivo: {str(e)}")
        
        print(f"\n📊 RESUMEN DE ARCHIVOS INDIVIDUALES:")
        print(f"   📄 Archivos cargados: {len(dataframes_individuales)}")
        print(f"   🔍 Total NaN en todos los individuales: {total_nan_individuales}")
        
        print("\n📋 PASO 5: Comparación detallada consolidado vs individuales...")
        
        if len(dataframes_individuales) > 0:
            # Combinar todos los archivos individuales para comparar con consolidado
            print("\n🔬 Combinando archivos individuales para comparación...")
            
            df_individuales_combinados = pd.concat(
                [info['dataframe'] for info in dataframes_individuales], 
                ignore_index=True, 
                sort=False
            )
            
            print(f"   📊 Archivos combinados: {len(df_individuales_combinados)} registros")
            print(f"   📋 Columnas en combinados: {len(df_individuales_combinados.columns)}")
            
            # Contar NaN en archivos combinados
            nan_combinados = df_individuales_combinados.isnull().sum()
            total_nan_combinados = nan_combinados.sum()
            
            print(f"   🔍 Total NaN en combinados: {total_nan_combinados}")
            
            # ANÁLISIS CRÍTICO: Comparar NaN consolidado vs combinados
            print(f"\n🚨 ANÁLISIS CRÍTICO DEL PROBLEMA:")
            print(f"   📊 NaN en consolidado: {nan_consolidado.sum()}")
            print(f"   📊 NaN en combinados: {total_nan_combinados}")
            diferencia_nan = nan_consolidado.sum() - total_nan_combinados
            print(f"   📈 Diferencia: {diferencia_nan} NaN adicionales en consolidado")
            
            if diferencia_nan > 0:
                print(f"   ⚠️ PROBLEMA CONFIRMADO: El consolidado tiene {diferencia_nan} NaN más que los archivos origen!")
                
                # Identificar columnas problemáticas
                print(f"\n   🔍 Analizando columnas con NaN adicionales:")
                
                for col in df_consolidado.columns:
                    nan_cons = nan_consolidado.get(col, 0)
                    nan_comb = nan_combinados.get(col, 0) if col in df_individuales_combinados.columns else 0
                    
                    if nan_cons > nan_comb:
                        diferencia_col = nan_cons - nan_comb
                        print(f"      ⚠️ {col}: +{diferencia_col} NaN adicionales en consolidado")
                        print(f"         Consolidado: {nan_cons}, Combinados: {nan_comb}")
                        
                        # Analizar por qué esta columna tiene NaN adicionales
                        if col not in df_individuales_combinados.columns:
                            print(f"         🔍 CAUSA: Columna '{col}' NO existe en archivos individuales")
                        else:
                            print(f"         🔍 CAUSA: Posible problema en proceso de consolidación")
            
            # Comparar esquemas
            print(f"\n📋 COMPARACIÓN DE ESQUEMAS:")
            cols_consolidado = set(df_consolidado.columns)
            cols_combinados = set(df_individuales_combinados.columns)
            
            solo_consolidado = cols_consolidado - cols_combinados
            solo_combinados = cols_combinados - cols_consolidado
            comunes = cols_consolidado & cols_combinados
            
            print(f"   📊 Columnas comunes: {len(comunes)}")
            print(f"   📊 Solo en consolidado: {len(solo_consolidado)}")
            print(f"   📊 Solo en combinados: {len(solo_combinados)}")
            
            if solo_consolidado:
                print(f"   🔹 Columnas solo en consolidado:")
                for col in sorted(list(solo_consolidado)[:10]):
                    nan_count = nan_consolidado.get(col, 0)
                    print(f"      - {col} (NaN: {nan_count})")
            
            if solo_combinados:
                print(f"   🔹 Columnas solo en combinados:")
                for col in sorted(list(solo_combinados)[:10]):
                    print(f"      - {col}")
        
        print("\n📋 PASO 6: Búsqueda exhaustiva de archivos MTX_WALLET_ORA...")
        
        try:
            import boto3
            s3_client = boto3.client('s3')
            
            # Buscar todos los archivos MTX_WALLET_ORA en ambos buckets
            buckets_busqueda = [bucket_bronze, bucket_silver]
            archivos_encontrados = []
            
            for bucket in buckets_busqueda:
                print(f"\n🔍 Buscando en {bucket}...")
                
                prefijos_busqueda = [
                    'PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/',
                    'MTX_WALLET_ORA/',
                ]
                
                for prefijo in prefijos_busqueda:
                    try:
                        paginator = s3_client.get_paginator('list_objects_v2')
                        for page in paginator.paginate(Bucket=bucket, Prefix=prefijo):
                            if 'Contents' in page:
                                for obj in page['Contents']:
                                    if obj['Key'].endswith('.parquet') and 'MTX_WALLET_ORA' in obj['Key']:
                                        archivos_encontrados.append({
                                            'bucket': bucket,
                                            'key': obj['Key'],
                                            'size': obj['Size'],
                                            'last_modified': obj['LastModified']
                                        })
                    except Exception as e:
                        print(f"   ⚠️ Error buscando en {prefijo}: {str(e)}")
            
            print(f"\n✅ Total archivos MTX_WALLET_ORA encontrados: {len(archivos_encontrados)}")
            
            # Agrupar por bucket
            archivos_bronze = [a for a in archivos_encontrados if 'bronze' in a['bucket'].lower()]
            archivos_silver = [a for a in archivos_encontrados if 'silver' in a['bucket'].lower()]
            
            print(f"   📁 En Bronze Zone: {len(archivos_bronze)} archivos")
            print(f"   📁 En Silver Zone: {len(archivos_silver)} archivos")
            
            if len(archivos_bronze) > 1:
                print(f"\n📋 Archivos en Bronze Zone (datos origen):")
                for i, archivo in enumerate(archivos_bronze[:5], 1):
                    size_mb = archivo['size'] / (1024 * 1024)
                    print(f"   {i}. {archivo['key']} ({size_mb:.2f} MB)")
            
            if len(archivos_silver) > 0:
                print(f"\n📋 Archivos en Silver Zone (datos consolidados):")
                for i, archivo in enumerate(archivos_silver, 1):
                    size_mb = archivo['size'] / (1024 * 1024)
                    print(f"   {i}. {archivo['key']} ({size_mb:.2f} MB)")
                    
        except Exception as e:
            print(f"❌ Error en búsqueda exhaustiva: {str(e)}")
        
        print("\n📋 PASO 7: Conclusiones y recomendaciones...")
        
        print(f"\n🎯 CONCLUSIONES:")
        
        if diferencia_nan > 0:
            print(f"   ❌ PROBLEMA CONFIRMADO: Se detectaron {diferencia_nan} valores NaN adicionales en el consolidado")
            print(f"   🔍 Esto indica que el proceso de consolidación está introduciendo NaN incorrectamente")
            
            print(f"\n💡 POSIBLES CAUSAS:")
            print(f"   1. Diferencias de esquema entre archivos individuales")
            print(f"   2. Problemas en el proceso de concat/merge (parámetro sort=False vs True)")
            print(f"   3. Columnas que aparecen en algunos archivos pero no en otros")
            print(f"   4. Problemas de tipos de datos incompatibles")
            
            print(f"\n🛠️ RECOMENDACIONES:")
            print(f"   1. Revisar la función consolidar_archivos_tabla_incremental_puro()")
            print(f"   2. Verificar el parámetro sort en pd.concat()")
            print(f"   3. Implementar normalización de esquemas antes del merge")
            print(f"   4. Agregar validación de integridad post-consolidación")
            
        else:
            print(f"   ✅ No se detectaron NaN adicionales en esta muestra")
            print(f"   📋 El problema puede estar en archivos no analizados o en procesos específicos")
        
        print(f"\n✅ Investigación completada: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ Error durante investigación: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analizar_problema_nan_completo()
