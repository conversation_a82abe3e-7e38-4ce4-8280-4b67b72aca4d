#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import boto3
import pandas as pd
import argparse
import logging
from io import BytesIO
from botocore.exceptions import ClientError
import os
from typing import List, Dict, Tuple
import sys

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Constantes
S3_LANDING_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_RAW_BUCKET = "prd-datalake-silver-zone-637423440311"

def leer_archivo_resumen(seccion_img: str) -> pd.DataFrame:
    """
    Lee el archivo de resumen Parquet de S3.
    
    Args:
        seccion_img (str): Nombre de la sección (ej: IMG_FLOW_04)
        
    Returns:
        pd.DataFrame: DataFrame con el contenido del archivo de resumen
    """
    try:
        s3_client = boto3.client('s3')
        resumen_key = f"RESUMEN/{seccion_img}/RESUMEN_{seccion_img}.parquet"
        
        try:
            response = s3_client.get_object(Bucket=S3_LANDING_BUCKET, Key=resumen_key)
            buffer = BytesIO(response['Body'].read())
            df = pd.read_parquet(buffer)
            logging.info(f"Archivo de resumen leído exitosamente: {len(df)} registros encontrados")
            return df
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                logging.warning(f"No se encontró el archivo de resumen: {resumen_key}")
                return pd.DataFrame()  # Retornar DataFrame vacío en lugar de lanzar error
            else:
                logging.warning(f"Error accediendo al archivo: {str(e)}")
                return pd.DataFrame()  # Retornar DataFrame vacío para otros errores
            
    except Exception as e:
        logging.warning(f"Error leyendo archivo de resumen: {str(e)}")
        return pd.DataFrame()  # Retornar DataFrame vacío para cualquier otro error

def verificar_archivo_existe(bucket: str, key: str) -> bool:
    """
    Verifica si un archivo existe en S3.
    
    Args:
        bucket (str): Nombre del bucket
        key (str): Ruta del archivo
        
    Returns:
        bool: True si existe, False si no
    """
    try:
        s3_client = boto3.client('s3')
        s3_client.head_object(Bucket=bucket, Key=key)
        return True
    except ClientError as e:
        if e.response['Error']['Code'] == '404':
            return False
        else:
            raise

def copiar_archivo_s3(origen_bucket: str, origen_key: str, destino_bucket: str, destino_key: str) -> None:
    """
    Copia un archivo de un bucket S3 a otro.
    
    Args:
        origen_bucket (str): Bucket de origen
        origen_key (str): Ruta del archivo en origen
        destino_bucket (str): Bucket de destino
        destino_key (str): Ruta del archivo en destino
    """
    try:
        s3_client = boto3.client('s3')
        
        # Verificar si el archivo origen existe
        if not verificar_archivo_existe(origen_bucket, origen_key):
            logging.warning(f"Archivo origen no encontrado: {origen_bucket}/{origen_key}")
            return
            
        # Verificar si el archivo destino ya existe
        if verificar_archivo_existe(destino_bucket, destino_key):
            logging.info(f"Archivo ya existe en destino, omitiendo: {destino_bucket}/{destino_key}")
            return
            
        # Copiar archivo
        copy_source = {
            'Bucket': origen_bucket,
            'Key': origen_key
        }
        s3_client.copy(copy_source, destino_bucket, destino_key)
        logging.info(f"Archivo copiado exitosamente: {origen_key} -> {destino_key}")
        
    except Exception as e:
        logging.warning(f"Error copiando archivo {origen_key}: {str(e)}")
        raise

def listar_archivos_subcarpeta(bucket: str, prefix: str) -> List[str]:
    """
    Lista todos los archivos en una subcarpeta de S3.
    
    Args:
        bucket (str): Nombre del bucket
        prefix (str): Prefijo de la subcarpeta
        
    Returns:
        List[str]: Lista de rutas de archivos encontrados
    """
    try:
        s3_client = boto3.client('s3')
        archivos = []
        
        paginator = s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
            if 'Contents' in page:
                archivos.extend([obj['Key'] for obj in page['Contents']])
                
        return archivos
    except Exception as e:
        logging.warning(f"Error listando archivos en {bucket}/{prefix}: {str(e)}")
        raise

def limpiar_subcarpeta_destino(bucket: str, prefix: str) -> None:
    """
    Elimina todos los archivos existentes en una subcarpeta de S3.
    
    Args:
        bucket (str): Nombre del bucket
        prefix (str): Prefijo de la subcarpeta
    """
    try:
        # Listar archivos existentes
        archivos = listar_archivos_subcarpeta(bucket, prefix)
        
        if archivos:
            s3_client = boto3.client('s3')
            logging.info(f"Encontrados {len(archivos)} archivos en {bucket}/{prefix}")
            
            # Eliminar archivos en lotes de 1000 (límite de S3)
            for i in range(0, len(archivos), 1000):
                batch = archivos[i:i + 1000]
                objects_to_delete = [{'Key': key} for key in batch]
                
                s3_client.delete_objects(
                    Bucket=bucket,
                    Delete={'Objects': objects_to_delete}
                )
            
            logging.info(f"Eliminados {len(archivos)} archivos de {bucket}/{prefix}")
        else:
            logging.info(f"No se encontraron archivos para eliminar en {bucket}/{prefix}")
            
    except Exception as e:
        logging.warning(f"Error limpiando subcarpeta {bucket}/{prefix}: {str(e)}")
        raise

def procesar_archivos(df: pd.DataFrame) -> None:
    """
    Procesa los archivos listados en el DataFrame y los copia al bucket destino.
    Limpia solo la carpeta final antes de copiar nuevos archivos.
    
    Args:
        df (pd.DataFrame): DataFrame con la información de los archivos
    """
    try:
        total_archivos = len(df)
        archivos_procesados = 0
        errores = 0
        archivos_no_encontrados = 0
        subcarpetas_limpiadas = set()
        
        for _, row in df.iterrows():
            try:
                # Construir rutas origen y destino
                origen_key = f"{row['db_parent']}/{row['tabla_nombre']}/{row['año']}/{row['mes']}/{row['dia']}/{row['nombre_archivo']}"
                destino_key = origen_key  # Misma estructura en el bucket destino
                
                # Verificar si el archivo origen existe antes de procesar
                if not verificar_archivo_existe(S3_LANDING_BUCKET, origen_key):
                    logging.warning(f"Archivo no encontrado en origen, omitiendo: {origen_key}")
                    archivos_no_encontrados += 1
                    continue
                
                # Obtener la carpeta final donde está el archivo
                carpeta_final = f"{row['db_parent']}/{row['tabla_nombre']}/{row['año']}/{row['mes']}/{row['dia']}"
                
                # Limpiar carpeta final si no se ha limpiado antes
                if carpeta_final not in subcarpetas_limpiadas:
                    logging.info(f"Limpiando carpeta final: {S3_RAW_BUCKET}/{carpeta_final}")
                    limpiar_subcarpeta_destino(S3_RAW_BUCKET, carpeta_final)
                    subcarpetas_limpiadas.add(carpeta_final)
                
                # Copiar archivo
                copiar_archivo_s3(S3_LANDING_BUCKET, origen_key, S3_RAW_BUCKET, destino_key)
                archivos_procesados += 1
                
            except Exception as e:
                logging.warning(f"Error procesando archivo {row['nombre_archivo']}: {str(e)}")
                errores += 1
                continue
        
        # Resumen final
        logging.info(f"\n=== Resumen de procesamiento ===")
        logging.info(f"Total archivos: {total_archivos}")
        logging.info(f"Archivos procesados: {archivos_procesados}")
        logging.info(f"Archivos no encontrados: {archivos_no_encontrados}")
        logging.info(f"Carpetas finales limpiadas: {len(subcarpetas_limpiadas)}")
        logging.info(f"Errores: {errores}")
        
    except Exception as e:
        logging.warning(f"Error en el procesamiento de archivos: {str(e)}")
        raise

def main():
    """
    Función principal del script
    """
    try:
        # Configurar argumentos de línea de comandos
        parser = argparse.ArgumentParser(description='Procesa archivos basados en un resumen Parquet')
        parser.add_argument('seccion_img', help='Nombre de la sección (ej: IMG_FLOW_04)')
        args = parser.parse_args()
        
        # Mostrar configuración
        logging.info(f"\n=== Iniciando procesamiento ===")
        logging.info(f"Sección: {args.seccion_img}")
        logging.info(f"Bucket origen: {S3_LANDING_BUCKET}")
        logging.info(f"Bucket destino: {S3_RAW_BUCKET}")
        
        # Verificar si existe el archivo de resumen antes de continuar
        resumen_key = f"RESUMEN/{args.seccion_img}/RESUMEN_{args.seccion_img}.parquet"
        if not verificar_archivo_existe(S3_LANDING_BUCKET, resumen_key):
            logging.warning(f"No se encontró el archivo de resumen: {resumen_key}")
            logging.warning("No hay archivos para procesar.")
            sys.exit(0)  # Salir con código 0 (éxito) en lugar de 1 (error)
            
        # Leer archivo de resumen
        df_resumen = leer_archivo_resumen(args.seccion_img)
        
        # Verificar si hay datos en el resumen
        if len(df_resumen) == 0:
            logging.warning("El archivo de resumen está vacío, no hay archivos para procesar.")
            sys.exit(0)  # Salir con código 0 (éxito)
        
        # Procesar archivos
        procesar_archivos(df_resumen)
        
        logging.info("Proceso completado exitosamente")
        
    except Exception as e:
        logging.warning(f"Error en el procesamiento: {str(e)}")
        sys.exit(0)  # Salir con código 0 (éxito) en lugar de 1 (error)

if __name__ == "__main__":
    main()
