#!/usr/bin/env python3

print("=== ANÁLISIS PROFUNDO DE PÉRDIDA DE DATOS ===")
print("Investigando por qué se pierden 39 WALLET_NUMBER únicos...")

try:
    import pandas as pd
    import boto3
    from io import BytesIO
    
    # Configuración
    bucket_bronze = "prd-datalake-bronze-zone-637423440311"
    bucket_silver = "prd-datalake-silver-zone-637423440311"
    archivo_origen = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-080250_chunk_0.parquet"
    archivo_consolidado = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    
    s3 = boto3.client('s3')
    
    # Leer archivos
    print("📥 Leyendo archivos...")
    response = s3.get_object(Bucket=bucket_bronze, Key=archivo_origen)
    df_origen = pd.read_parquet(BytesIO(response['Body'].read()))
    
    response = s3.get_object(Bucket=bucket_silver, Key=archivo_consolidado)
    df_consolidado = pd.read_parquet(BytesIO(response['Body'].read()))
    
    print(f"   ✅ Origen: {len(df_origen)} registros")
    print(f"   ✅ Consolidado: {len(df_consolidado)} registros")
    
    # Identificar diferencias
    clave = "WALLET_NUMBER"
    origen_set = set(df_origen[clave])
    consolidado_set = set(df_consolidado[clave])
    
    perdidos = origen_set - consolidado_set
    ganados = consolidado_set - origen_set
    comunes = origen_set & consolidado_set
    
    print(f"\n📊 ANÁLISIS DE CONJUNTOS:")
    print(f"   📋 WALLET_NUMBER en origen: {len(origen_set)}")
    print(f"   📋 WALLET_NUMBER en consolidado: {len(consolidado_set)}")
    print(f"   📋 WALLET_NUMBER comunes: {len(comunes)}")
    print(f"   📋 WALLET_NUMBER perdidos: {len(perdidos)}")
    print(f"   📋 WALLET_NUMBER nuevos en consolidado: {len(ganados)}")
    
    # Análisis de campos de fecha en origen
    print(f"\n📅 ANÁLISIS DE CAMPOS DE FECHA EN ORIGEN:")
    campos_fecha = [col for col in df_origen.columns if any(palabra in col.upper() for palabra in ['DATE', 'MODIFIED', 'CREATED', 'PARTITION'])]
    print(f"   📋 Campos de fecha: {campos_fecha}")
    
    for campo in campos_fecha:
        valores_unicos = df_origen[campo].nunique()
        print(f"       {campo}: {valores_unicos} valores únicos")
        if valores_unicos <= 10:
            print(f"         Valores: {sorted(df_origen[campo].unique())}")
    
    # Análisis de registros perdidos vs conservados
    print(f"\n🔍 ANÁLISIS DE REGISTROS PERDIDOS:")
    
    if len(perdidos) > 0:
        print(f"   📋 Primeros 10 WALLET_NUMBER perdidos:")
        perdidos_lista = sorted(list(perdidos))[:10]
        for wallet in perdidos_lista:
            registro = df_origen[df_origen[clave] == wallet].iloc[0]
            print(f"       {wallet}:")
            print(f"         CREATED_ON: {registro.get('CREATED_ON', 'N/A')}")
            print(f"         MODIFIED_ON: {registro.get('MODIFIED_ON', 'N/A')}")
            print(f"         DATA_LAKE_PARTITION_DATE: {registro.get('DATA_LAKE_PARTITION_DATE', 'N/A')}")
            if 'VALID_FROM_DATE' in df_origen.columns:
                print(f"         VALID_FROM_DATE: {registro.get('VALID_FROM_DATE', 'N/A')}")
    
    print(f"\n   📋 Primeros 5 WALLET_NUMBER conservados:")
    comunes_lista = sorted(list(comunes))[:5]
    for wallet in comunes_lista:
        registro = df_origen[df_origen[clave] == wallet].iloc[0]
        print(f"       {wallet}:")
        print(f"         CREATED_ON: {registro.get('CREATED_ON', 'N/A')}")
        print(f"         MODIFIED_ON: {registro.get('MODIFIED_ON', 'N/A')}")
        print(f"         DATA_LAKE_PARTITION_DATE: {registro.get('DATA_LAKE_PARTITION_DATE', 'N/A')}")
        if 'VALID_FROM_DATE' in df_origen.columns:
            print(f"         VALID_FROM_DATE: {registro.get('VALID_FROM_DATE', 'N/A')}")
    
    # Verificar si hay diferencias en algún campo que pueda explicar el filtrado
    print(f"\n🧪 BÚSQUEDA DE PATRONES EN CAMPOS:")
    
    # Analizar si los perdidos tienen características específicas
    df_perdidos = df_origen[df_origen[clave].isin(perdidos)]
    df_conservados = df_origen[df_origen[clave].isin(comunes)]
    
    print(f"   📊 Estadísticas de registros perdidos vs conservados:")
    
    for campo in ['CREATED_ON', 'MODIFIED_ON', 'DATA_LAKE_PARTITION_DATE', 'VALID_FROM_DATE', 'EXPIRY_DATE']:
        if campo in df_origen.columns:
            perdidos_unique = df_perdidos[campo].nunique()
            conservados_unique = df_conservados[campo].nunique()
            print(f"       {campo}:")
            print(f"         Perdidos - valores únicos: {perdidos_unique}")
            print(f"         Conservados - valores únicos: {conservados_unique}")
            
            if perdidos_unique <= 5:
                print(f"         Perdidos - valores: {sorted(df_perdidos[campo].unique())}")
            if conservados_unique <= 5:
                print(f"         Conservados - valores: {sorted(df_conservados[campo].unique())}")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n=== FIN ANÁLISIS PROFUNDO ===")
