#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CORRECCIÓN CRÍTICA: Evitar conversión NaN → 'nan' string

PROBLEMA: La función alinear_tipos_datos_puro() está convirtiendo valores NaN reales 
a strings 'nan' cuando usa .astype(str) para alinear tipos.

SOLUCIÓN: Preservar NaN como NaN incluso después de conversión a object/string
"""

import pandas as pd
import numpy as np
import logging

def alinear_tipos_datos_puro_corregido(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame):
    """
    VERSIÓN CORREGIDA: Alinea tipos SIN convertir NaN reales a strings 'nan'
    """
    if df_existente.empty or df_nuevos.empty:
        return df_existente, df_nuevos
    
    columnas_comunes = set(df_existente.columns) & set(df_nuevos.columns)
    
    if not columnas_comunes:
        logging.warning("No hay columnas comunes entre DataFrames existente y nuevo")
        return df_existente, df_nuevos
    
    df_existente_copia = df_existente.copy()
    df_nuevos_copia = df_nuevos.copy()
    
    for columna in columnas_comunes:
        tipo_existente = df_existente_copia[columna].dtype
        tipo_nuevo = df_nuevos_copia[columna].dtype
        
        if tipo_existente != tipo_nuevo:
            logging.debug(f"🔄 Alineando {columna}: {tipo_existente} -> {tipo_nuevo}")
            
            try:
                # CORRECCIÓN CRÍTICA: Preservar NaN durante conversión a string
                
                # 1. Guardar máscaras de NaN ANTES de conversión
                mask_existente_nan = df_existente_copia[columna].isna()
                mask_nuevos_nan = df_nuevos_copia[columna].isna()
                
                # 2. Convertir a string
                df_existente_copia[columna] = df_existente_copia[columna].astype(str)
                df_nuevos_copia[columna] = df_nuevos_copia[columna].astype(str)
                
                # 3. RESTAURAR NaN donde había NaN originalmente
                # (esto evita que 'nan' strings permanezcan)
                df_existente_copia.loc[mask_existente_nan, columna] = np.nan
                df_nuevos_copia.loc[mask_nuevos_nan, columna] = np.nan
                
                logging.info(f"✅ Columna {columna}: NaN preservados durante alineación de tipos")
                
            except Exception as e:
                logging.warning(f"Error alineando tipos para columna {columna}: {str(e)}")
    
    # Resto de la función igual (agregar columnas faltantes)
    columnas_solo_existente = set(df_existente.columns) - set(df_nuevos.columns)
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    
    for columna in columnas_solo_nuevos:
        tipo_columna = df_nuevos_copia[columna].dtype
        
        if pd.api.types.is_bool_dtype(tipo_columna):
            df_existente_copia[columna] = None
        elif pd.api.types.is_datetime64_any_dtype(tipo_columna):
            df_existente_copia[columna] = pd.NaT
        elif pd.api.types.is_numeric_dtype(tipo_columna):
            df_existente_copia[columna] = np.nan
        else:
            df_existente_copia[columna] = None
            
        logging.info(f"➕ Agregada columna faltante al existente: {columna} (tipo: {tipo_columna})")
    
    for columna in columnas_solo_existente:
        tipo_columna = df_existente_copia[columna].dtype
        
        if pd.api.types.is_bool_dtype(tipo_columna):
            df_nuevos_copia[columna] = None
        elif pd.api.types.is_datetime64_any_dtype(tipo_columna):
            df_nuevos_copia[columna] = pd.NaT
        elif pd.api.types.is_numeric_dtype(tipo_columna):
            df_nuevos_copia[columna] = np.nan
        else:
            df_nuevos_copia[columna] = None
            
        logging.info(f"➕ Agregada columna faltante al nuevo: {columna} (tipo: {tipo_columna})")
    
    return df_existente_copia, df_nuevos_copia

def test_correccion():
    """Test para verificar que la corrección funciona"""
    print("🧪 TESTING CORRECCIÓN NaN → 'nan' string")
    print("=" * 50)
    
    # Crear DataFrames de prueba con NaN reales
    df1 = pd.DataFrame({
        'ISSUER_ID': [100.0, np.nan, 200.0],  # float64 con NaN
        'NAME': ['A', 'B', 'C']               # object
    })
    
    df2 = pd.DataFrame({
        'ISSUER_ID': ['300', '400', '500'],   # object (string)  
        'NAME': ['D', 'E', 'F']               # object
    })
    
    print("ANTES de alineación:")
    print(f"df1 ISSUER_ID: {df1['ISSUER_ID'].tolist()} (tipo: {df1['ISSUER_ID'].dtype})")
    print(f"df2 ISSUER_ID: {df2['ISSUER_ID'].tolist()} (tipo: {df2['ISSUER_ID'].dtype})")
    
    # Aplicar corrección
    df1_alineado, df2_alineado = alinear_tipos_datos_puro_corregido(df1, df2)
    
    print("\nDESPUÉS de alineación corregida:")
    print(f"df1 ISSUER_ID: {df1_alineado['ISSUER_ID'].tolist()} (tipo: {df1_alineado['ISSUER_ID'].dtype})")
    print(f"df2 ISSUER_ID: {df2_alineado['ISSUER_ID'].tolist()} (tipo: {df2_alineado['ISSUER_ID'].dtype})")
    
    # Verificar que no hay strings 'nan'
    strings_nan_df1 = (df1_alineado['ISSUER_ID'].astype(str).str.lower() == 'nan').sum()
    strings_nan_df2 = (df2_alineado['ISSUER_ID'].astype(str).str.lower() == 'nan').sum()
    
    print(f"\n🔍 VERIFICACIÓN:")
    print(f"   Strings 'nan' en df1: {strings_nan_df1}")
    print(f"   Strings 'nan' en df2: {strings_nan_df2}")
    print(f"   NaN reales en df1: {df1_alineado['ISSUER_ID'].isna().sum()}")
    print(f"   NaN reales en df2: {df2_alineado['ISSUER_ID'].isna().sum()}")
    
    if strings_nan_df1 == 0 and strings_nan_df2 == 0:
        print("✅ CORRECCIÓN EXITOSA: No hay strings 'nan' artificiales")
        return True
    else:
        print("❌ CORRECCIÓN FALLÓ: Aún hay strings 'nan'")
        return False

if __name__ == "__main__":
    test_correccion()
