#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import sys
import os

# Agregar el directorio actual al path para poder importar
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

def test_problema_especifico():
    """
    Prueba específica para diagnosticar por qué WALLET, OPT, OPERATOR se convierten en cadena vacía.
    """
    
    print("=== DIAGNÓSTICO ESPECÍFICO DEL PROBLEMA ===\n")
    
    # Importar la función desde el módulo local
    from app_raw_consolidado import normalizar_tipos_datos
    
    # Recrear exactamente los datos problemáticos observados
    test_data = {
        'PAYMENT_METHOD_TYPE_ID': ['WALLET', 'WALLET', 'WALLET', 'WALLET'],
        'USER_GRADE': ['OPT', 'OPT', 'OPT', 'OPT'], 
        'USER_TYPE': ['OPERATOR', 'OPERATOR', 'OPERATOR', 'OPERATOR']
    }
    
    df_original = pd.DataFrame(test_data)
    
    print("DataFrame ORIGINAL:")
    print(df_original)
    print()
    
    print("Tipos de datos ORIGINALES:")
    print(df_original.dtypes)
    print()
    
    print("Valores únicos ORIGINALES:")
    for col in df_original.columns:
        print(f"{col}: {df_original[col].unique()}")
    print()
    
    # Aplicar normalización paso a paso para ver dónde ocurre el problema
    df_test = df_original.copy()
    
    print("APLICANDO NORMALIZACIÓN...")
    df_normalizado = normalizar_tipos_datos(df_test)
    
    print("\nDataFrame DESPUÉS de normalización:")
    print(df_normalizado)
    print()
    
    print("Tipos de datos DESPUÉS:")
    print(df_normalizado.dtypes)
    print()
    
    print("Valores únicos DESPUÉS:")
    for col in df_normalizado.columns:
        print(f"{col}: {df_normalizado[col].unique()}")
    print()
    
    print("=== ANÁLISIS DE CAMBIOS ===")
    for col in df_original.columns:
        original_values = df_original[col].tolist()
        final_values = df_normalizado[col].tolist()
        
        if original_values != final_values:
            print(f"\n🚨 PROBLEMA en columna {col}:")
            for i, (orig, final) in enumerate(zip(original_values, final_values)):
                if orig != final:
                    print(f"   Fila {i}: '{orig}' → '{final}'")
        else:
            print(f"\n✅ Columna {col}: Sin cambios")

if __name__ == "__main__":
    test_problema_especifico()
