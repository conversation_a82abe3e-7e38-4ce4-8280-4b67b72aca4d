#!/usr/bin/env python3
"""
==========================================
 Test de conexión a Oracle
 Autor: <PERSON><PERSON><PERSON><PERSON>enas Galarza
 Descripción: Prueba la conexión a Oracle.
 Fecha: 2024-06-01
==========================================
"""
import os
import pytest
from pathlib import Path
from src.etl.db_manager import DBManager, get_project_root
from src.utils.aws_secrets import get_secret
from configparser import ConfigParser

def test_config_file_exists():
    """Verifica que el archivo de configuración existe y es accesible en S3"""
    # Ya no verificamos el archivo local porque ahora se lee desde S3
    db = DBManager()
    config = db.config
    assert config is not None, "No se pudo cargar la configuración desde S3"
    
    # Verificar si existe la configuración de Oracle
    if 'oracle' in config:
        # Verifica las claves requeridas
        required_keys = ['host', 'port', 'user', 'password']
        for key in required_keys:
            assert key in config['oracle'], f"Falta la clave '{key}' en la configuración de Oracle"
        
        # Verificar que existe service_name o sid
        assert 'service_name' in config['oracle'] or 'sid' in config['oracle'], "Falta 'service_name' o 'sid' en la configuración de Oracle"
    else:
        pytest.skip("No hay configuración de Oracle disponible")

@pytest.mark.db
def test_oracle_connection():
    """Prueba la conexión a Oracle con permisos limitados"""
    try:
        db = DBManager()
        
        # Verificar si existe la configuración de Oracle
        if 'oracle' not in db.config:
            pytest.skip("No hay configuración de Oracle disponible")
            
        conn = db.get_oracle_connection()
        assert conn is not None, "La conexión a Oracle es None"
        
        # Prueba una consulta simple que no requiere permisos especiales
        with conn.get_cursor() as cursor:
            cursor.execute("SELECT 1 as test FROM DUAL")
            result = cursor.fetchone()
            assert result[0] == 1, "La consulta de prueba a Oracle falló"
        
        db.close_connections()
    except ImportError:
        pytest.skip("El módulo oracledb no está instalado")
    except Exception as e:
        pytest.fail(f"Error al probar la conexión Oracle: {str(e)}")

if __name__ == "__main__":
    pytest.main([__file__, '-v'])
