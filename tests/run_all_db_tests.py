#!/usr/bin/env python3
"""
Script para ejecutar todos los tests de conexión de base de datos
"""

import subprocess
import sys
from pathlib import Path

def run_test(test_file):
    """Ejecuta un archivo de test específico"""
    print(f"\n{'='*60}")
    print(f"🔄 Ejecutando: {test_file}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            f'tests/{test_file}', '-v'
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"✅ {test_file} - EXITOSO")
        else:
            print(f"❌ {test_file} - FALLÓ")
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error ejecutando {test_file}: {str(e)}")
        return False

def main():
    """Función principal"""
    print("🚀 Iniciando tests de conexión de bases de datos...")
    
    # Lista de archivos de test
    test_files = [
        'test_mysql_connection.py',
        'test_postgres_connection.py', 
        'test_oracle_connection.py'
    ]
    
    results = {}
    
    # Ejecutar cada test
    for test_file in test_files:
        results[test_file] = run_test(test_file)
    
    # Mostrar resumen
    print(f"\n{'='*60}")
    print("📊 RESUMEN DE RESULTADOS")
    print(f"{'='*60}")
    
    passed = 0
    failed = 0
    
    for test_file, success in results.items():
        status = "✅ EXITOSO" if success else "❌ FALLÓ"
        print(f"{test_file:<30} {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 Total: {len(test_files)} tests")
    print(f"✅ Exitosos: {passed}")
    print(f"❌ Fallidos: {failed}")
    
    if failed == 0:
        print("\n🎉 ¡Todos los tests de conexión pasaron exitosamente!")
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) fallaron. Revisa la configuración.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
