import os
import pytest
from pathlib import Path
from src.etl.db_manager import DBManager, get_project_root
from src.utils.aws_secrets import get_secret
from configparser import Config<PERSON>arser

def test_config_file_mysql():
    """Verifica que el archivo de configuración existe y contiene la configuración de MySQL"""
    db = DBManager()
    config = db.config
    assert config is not None, "No se pudo cargar la configuración desde S3"
    assert 'mysql' in config, "Falta la configuración de MySQL"
    
    # Verifica las claves requeridas para MySQL
    required_keys = ['host', 'port', 'user', 'password']
    for key in required_keys:
        assert key in config['mysql'], f"Falta la clave '{key}' en la configuración de MySQL"

def test_get_mysql_secrets():
    """Prueba la obtención de secretos de MySQL desde AWS Secrets Manager"""
    db = DBManager()
    config = db.config
    assert config is not None, "No se pudo cargar la configuración"
    
    # Verifica que tenemos las credenciales necesarias para MySQL
    assert 'mysql' in config, "Falta la configuración de MySQL"
    required_keys = ['host', 'port', 'user', 'password']
    for key in required_keys:
        assert key in config['mysql'], f"Falta la clave '{key}' en la configuración de MySQL"

@pytest.mark.db
def test_mysql_connection():
    """Prueba la conexión a MySQL con permisos limitados"""
    try:
        db = DBManager()
        conn = db.get_mysql_connection()
        assert conn is not None, "La conexión a MySQL es None"
        assert conn.connection.is_connected(), "La conexión a MySQL no está activa"
        
        # Prueba una consulta simple que no requiere permisos especiales ni base de datos específica
        with conn.get_cursor() as cursor:
            cursor.execute("SELECT 1 as test")  # Esta consulta no requiere una base de datos específica
            result = cursor.fetchone()
            assert result['test'] == 1, "La consulta de prueba a MySQL falló"
        
        db.close_connections()
    except Exception as e:
        pytest.fail(f"Error al probar la conexión MySQL: {str(e)}")

@pytest.mark.db
def test_mysql_connection_performance():
    """Prueba el rendimiento de la conexión a MySQL"""
    import time
    try:
        start_time = time.time()
        db = DBManager()
        conn = db.get_mysql_connection()
        assert conn is not None, "La conexión a MySQL es None"
        
        # Prueba una consulta simple y mide el tiempo
        with conn.get_cursor() as cursor:
            query_start = time.time()
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            query_time = time.time() - query_start
            
        conn_time = time.time() - start_time
        
        # Registrar tiempos para análisis
        print(f"\nTiempo de conexión MySQL: {conn_time:.2f} segundos")
        print(f"Tiempo de consulta MySQL: {query_time:.2f} segundos")
        
        # Verificar que los tiempos sean razonables
        assert conn_time < 5, f"La conexión a MySQL tomó demasiado tiempo: {conn_time:.2f} segundos"
        assert query_time < 1, f"La consulta a MySQL tomó demasiado tiempo: {query_time:.2f} segundos"
        
        db.close_connections()
    except Exception as e:
        pytest.fail(f"Error al probar el rendimiento de MySQL: {str(e)}")

if __name__ == "__main__":
    pytest.main([__file__, '-v'])
