import os
import pytest
from pathlib import Path
from src.etl.db_manager import DBManager, get_project_root
from src.utils.aws_secrets import get_secret
from configparser import ConfigParser

def test_config_file_exists():
    """Verifica que el archivo de configuración existe y es accesible en S3"""
    # Ya no verificamos el archivo local porque ahora se lee desde S3
    db = DBManager()
    config = db.config
    assert config is not None, "No se pudo cargar la configuración desde S3"
    assert 'mysql' in config, "Falta la configuración de MySQL"
    assert 'postgres' in config, "Falta la configuración de PostgreSQL"
    
    # Verifica las claves requeridas
    for db_type in ['mysql', 'postgres']:
        required_keys = ['host', 'port', 'user', 'password']
        for key in required_keys:
            assert key in config[db_type], f"Falta la clave '{key}' en la configuración de {db_type}"

def test_get_secrets():
    """Prueba la obtención de secretos desde AWS Secrets Manager"""
    db = DBManager()
    config = db.config
    assert config is not None, "No se pudo cargar la configuración"
    
    # Verifica que tenemos las credenciales necesarias
    for db_type in ['mysql', 'postgres']:
        assert db_type in config, f"Falta la configuración de {db_type}"
        required_keys = ['host', 'port', 'user', 'password']
        for key in required_keys:
            assert key in config[db_type], f"Falta la clave '{key}' en la configuración de {db_type}"

@pytest.mark.db
def test_mysql_connection():
    """Prueba la conexión a MySQL con permisos limitados"""
    try:
        db = DBManager()
        conn = db.get_mysql_connection()
        assert conn is not None, "La conexión a MySQL es None"
        assert conn.connection.is_connected(), "La conexión a MySQL no está activa"
        
        # Prueba una consulta simple que no requiere permisos especiales ni base de datos específica
        with conn.get_cursor() as cursor:
            cursor.execute("SELECT 1 as test")  # Esta consulta no requiere una base de datos específica
            result = cursor.fetchone()
            assert result['test'] == 1, "La consulta de prueba a MySQL falló"
        
        db.close_connections()
    except Exception as e:
        pytest.fail(f"Error al probar la conexión MySQL: {str(e)}")

@pytest.mark.db
def test_postgres_connection():
    """Prueba la conexión a PostgreSQL con permisos limitados"""
    try:
        db = DBManager()
        conn = db.get_postgres_connection()
        assert conn is not None, "La conexión a PostgreSQL es None"
        assert not conn.connection.closed, "La conexión a PostgreSQL está cerrada"
        
        # Prueba una consulta simple que no requiere permisos especiales
        with conn.get_cursor() as cursor:
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            assert result['test'] == 1, "La consulta de prueba a PostgreSQL falló"
        
        db.close_connections()
    except Exception as e:
        pytest.fail(f"Error al probar la conexión PostgreSQL: {str(e)}")

if __name__ == "__main__":
    pytest.main([__file__, '-v'])
