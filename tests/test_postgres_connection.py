import os
import pytest
from pathlib import Path
from src.etl.db_manager import DBManager, get_project_root
from src.utils.aws_secrets import get_secret
from configparser import ConfigParser

def test_config_file_postgres():
    """Verifica que el archivo de configuraciu00f3n existe y contiene la configuraciu00f3n de PostgreSQL"""
    db = DBManager()
    config = db.config
    assert config is not None, "No se pudo cargar la configuraciu00f3n desde S3"
    assert 'postgres' in config, "Falta la configuraciu00f3n de PostgreSQL"
    
    # Verifica las claves requeridas para PostgreSQL
    required_keys = ['host', 'port', 'user', 'password']
    for key in required_keys:
        assert key in config['postgres'], f"Falta la clave '{key}' en la configuraciu00f3n de PostgreSQL"

def test_get_postgres_secrets():
    """Prueba la obtenciu00f3n de secretos de PostgreSQL desde AWS Secrets Manager"""
    db = DBManager()
    config = db.config
    assert config is not None, "No se pudo cargar la configuraciu00f3n"
    
    # Verifica que tenemos las credenciales necesarias para PostgreSQL
    assert 'postgres' in config, "Falta la configuraciu00f3n de PostgreSQL"
    required_keys = ['host', 'port', 'user', 'password']
    for key in required_keys:
        assert key in config['postgres'], f"Falta la clave '{key}' en la configuraciu00f3n de PostgreSQL"

@pytest.mark.db
def test_postgres_connection():
    """Prueba la conexiu00f3n a PostgreSQL con permisos limitados"""
    try:
        db = DBManager()
        conn = db.get_postgres_connection()
        assert conn is not None, "La conexiu00f3n a PostgreSQL es None"
        assert not conn.connection.closed, "La conexiu00f3n a PostgreSQL estu00e1 cerrada"
        
        # Prueba una consulta simple que no requiere permisos especiales
        with conn.get_cursor() as cursor:
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            assert result['test'] == 1, "La consulta de prueba a PostgreSQL fallu00f3"
        
        db.close_connections()
    except Exception as e:
        pytest.fail(f"Error al probar la conexiu00f3n PostgreSQL: {str(e)}")

@pytest.mark.db
def test_postgres_connection_performance():
    """Prueba el rendimiento de la conexiu00f3n a PostgreSQL"""
    import time
    try:
        start_time = time.time()
        db = DBManager()
        conn = db.get_postgres_connection()
        assert conn is not None, "La conexiu00f3n a PostgreSQL es None"
        
        # Prueba una consulta simple y mide el tiempo
        with conn.get_cursor() as cursor:
            query_start = time.time()
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            query_time = time.time() - query_start
            
        conn_time = time.time() - start_time
        
        # Registrar tiempos para anu00e1lisis
        print(f"\nTiempo de conexiu00f3n PostgreSQL: {conn_time:.2f} segundos")
        print(f"Tiempo de consulta PostgreSQL: {query_time:.2f} segundos")
        
        # Verificar que los tiempos sean razonables
        assert conn_time < 5, f"La conexiu00f3n a PostgreSQL tomu00f3 demasiado tiempo: {conn_time:.2f} segundos"
        assert query_time < 1, f"La consulta a PostgreSQL tomu00f3 demasiado tiempo: {query_time:.2f} segundos"
        
        db.close_connections()
    except Exception as e:
        pytest.fail(f"Error al probar el rendimiento de PostgreSQL: {str(e)}")

@pytest.mark.db
def test_postgres_fast_mode_params():
    """Verifica que los paru00e1metros optimizados para el modo ru00e1pido de PostgreSQL estu00e1n correctamente configurados"""
    from src.etl.fast_mode_manager import FastModeManager
    
    try:
        # Crear una instancia del FastModeManager
        fast_manager = FastModeManager()
        
        # Simular query_info para PostgreSQL
        query_info = {
            'name': 'test_table',
            'db_config': {
                'type': 'postgres',
                'host': 'localhost',
                'port': '5432',
                'database': 'test_db',
                'user': 'test_user',
                'password': 'test_password'
            }
        }
        
        # Extraer los paru00e1metros JDBC que se usarían
        # Nota: No podemos llamar directamente a _conectar_bd porque es un mu00e9todo privado
        # y no queremos modificar la clase solo para testing
        
        # Verificar que los paru00e1metros cruciales estu00e1n presentes en el cu00f3digo
        import inspect
        source = inspect.getsource(FastModeManager)
        
        # Verificar paru00e1metros cruciales para PostgreSQL
        crucial_params = [
            "useCursorFetch", "true",
            "defaultFetchSize", "10000",
            "prepareThreshold", "1",
            "tcpKeepAlive", "true",
            "reWriteBatchedInserts", "true",
            "binaryTransfer", "true"
        ]
        
        for param in crucial_params:
            assert param in source, f"Paru00e1metro crucial '{param}' no encontrado en FastModeManager"
        
        # Verificar que se usa fetchmany con tamau00f1o de chunk correcto
        assert "fetchmany(" in source, "No se encontru00f3 uso de fetchmany() para procesar en chunks"
        assert "100000" in source, "No se encontru00f3 el tamau00f1o de chunk recomendado (100000)"
        
    except Exception as e:
        pytest.fail(f"Error al verificar paru00e1metros de modo ru00e1pido para PostgreSQL: {str(e)}")

if __name__ == "__main__":
    pytest.main([__file__, '-v'])
