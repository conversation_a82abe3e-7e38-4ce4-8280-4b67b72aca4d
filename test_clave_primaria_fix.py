#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar que la corrección de claves primarias funcione correctamente.
Simula el problema original y verifica que ya no ocurra.
"""

import pandas as pd
import numpy as np
import logging
from tabla_config_simple import detectar_clave_primaria_tabla, validar_clave_primaria
from app_raw_consolidado import normalizar_tipos_datos

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def crear_dataframe_test_con_problemas():
    """
    Crea un DataFrame de prueba que simula el problema original donde
    los valores de WALLET_NUMBER se convertían en strings vacías.
    """
    data = {
        'WALLET_NUMBER': [
            'WAL001', 'WAL002', None, 'WAL004', 'WAL005',
            'WAL006', np.nan, 'WAL008', 'None', 'WAL010',
            'WAL011', 'null', 'WAL013', 'nan', 'WAL015',
            'WAL016', 'WAL017', 'WAL018', 'NaN', 'WAL020'
        ],
        'USER_ID': [
            'USR001', 'USR002', 'USR003', 'USR004', 'USR005',
            'USR006', 'USR007', 'USR008', 'USR009', 'USR010',
            'USR011', 'USR012', 'USR013', 'USR014', 'USR015',
            'USR016', 'USR017', 'USR018', 'USR019', 'USR020'
        ],
        'WALLET_TYPE': [
            'DIGITAL', 'PHYSICAL', 'DIGITAL', 'DIGITAL', 'PHYSICAL',
            'DIGITAL', 'PHYSICAL', 'DIGITAL', 'DIGITAL', 'PHYSICAL',
            'DIGITAL', 'PHYSICAL', 'DIGITAL', 'DIGITAL', 'PHYSICAL',
            'DIGITAL', 'PHYSICAL', 'DIGITAL', 'DIGITAL', 'PHYSICAL'
        ],
        'BALANCE': [
            100.50, 250.75, 0.0, 500.25, 1000.0,
            750.50, 300.25, 450.75, 200.0, 350.50,
            600.25, 800.75, 150.0, 950.25, 1200.0,
            400.50, 650.25, 550.75, 900.0, 1150.50
        ],
        'CREATED_DATE': [
            '2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05',
            '2024-01-06', '2024-01-07', '2024-01-08', '2024-01-09', '2024-01-10',
            '2024-01-11', '2024-01-12', '2024-01-13', '2024-01-14', '2024-01-15',
            '2024-01-16', '2024-01-17', '2024-01-18', '2024-01-19', '2024-01-20'
        ]
    }
    
    return pd.DataFrame(data)

def simular_normalizacion_antigua(df):
    """
    Simula la normalización problemática antigua que corrompía las claves primarias.
    """
    df_problematico = df.copy()
    
    # Simular la conversión problemática
    for col in df_problematico.columns:
        if df_problematico[col].dtype == 'object':
            # Esta es la línea problemática que corrompía los datos
            df_problematico[col] = df_problematico[col].fillna('').replace(['None', 'nan', 'NaN', 'null'], '')
    
    return df_problematico

def main():
    """
    Función principal que ejecuta las pruebas.
    """
    print("🧪 INICIANDO PRUEBAS DE CORRECCIÓN DE CLAVES PRIMARIAS")
    print("=" * 60)
    
    # PASO 1: Crear DataFrame de prueba
    df_original = crear_dataframe_test_con_problemas()
    print(f"\n📊 DataFrame original creado: {len(df_original)} registros")
    print(f"Columnas: {list(df_original.columns)}")
    
    # Mostrar valores problemáticos en WALLET_NUMBER
    wallet_problematicos = df_original['WALLET_NUMBER'].isna() | df_original['WALLET_NUMBER'].isin(['None', 'nan', 'NaN', 'null'])
    print(f"🚨 Valores problemáticos en WALLET_NUMBER: {wallet_problematicos.sum()}")
    print(f"Valores problemáticos: {df_original[wallet_problematicos]['WALLET_NUMBER'].tolist()}")
    
    # PASO 2: Detectar clave primaria
    print(f"\n🔍 Detectando clave primaria para MTX_WALLET_ORA...")
    claves_detectadas = detectar_clave_primaria_tabla('MTX_WALLET_ORA', df_original)
    print(f"Claves primarias detectadas: {claves_detectadas}")
    
    # PASO 3: Simular normalización problemática antigua
    print(f"\n❌ SIMULANDO NORMALIZACIÓN ANTIGUA (PROBLEMÁTICA)...")
    df_antiguo = simular_normalizacion_antigua(df_original)
    
    # Verificar corrupción
    wallet_vacios_antiguo = (df_antiguo['WALLET_NUMBER'] == '').sum()
    print(f"🚨 PROBLEMA REPRODUCIDO: {wallet_vacios_antiguo} valores de WALLET_NUMBER convertidos a string vacío")
    
    # Validar clave primaria antigua (debería fallar)
    es_valida_antigua = validar_clave_primaria(df_antiguo, ['WALLET_NUMBER'])
    print(f"✅ Validación clave primaria antigua: {es_valida_antigua} (debería ser False)")
    
    # PASO 4: Aplicar normalización NUEVA (corregida)
    print(f"\n✅ APLICANDO NORMALIZACIÓN NUEVA (CORREGIDA)...")
    df_nuevo = normalizar_tipos_datos(df_original, 'MTX_WALLET_ORA')
    
    # Verificar preservación
    wallet_vacios_nuevo = (df_nuevo['WALLET_NUMBER'] == '').sum()
    wallet_nulos_nuevo = df_nuevo['WALLET_NUMBER'].isna().sum()
    print(f"✅ PROBLEMA CORREGIDO: {wallet_vacios_nuevo} valores vacíos, {wallet_nulos_nuevo} valores nulos preservados")
    
    # Validar clave primaria nueva (debería pasar)
    es_valida_nueva = validar_clave_primaria(df_nuevo, ['WALLET_NUMBER'])
    print(f"✅ Validación clave primaria nueva: {es_valida_nueva} (debería ser True)")
    
    # PASO 5: Comparación detallada
    print(f"\n📊 COMPARACIÓN DETALLADA:")
    print(f"{'Métrica':<30} {'Antigua':<10} {'Nueva':<10} {'Mejora'}")
    print("-" * 60)
    
    # Contar valores válidos (no vacíos y no nulos)
    validos_antiguo = ((df_antiguo['WALLET_NUMBER'] != '') & (~df_antiguo['WALLET_NUMBER'].isna())).sum()
    validos_nuevo = ((df_nuevo['WALLET_NUMBER'] != '') & (~df_nuevo['WALLET_NUMBER'].isna())).sum()
    
    print(f"{'Valores válidos':<30} {validos_antiguo:<10} {validos_nuevo:<10} {'+' + str(validos_nuevo - validos_antiguo) if validos_nuevo > validos_antiguo else str(validos_nuevo - validos_antiguo)}")
    print(f"{'Valores vacíos':<30} {wallet_vacios_antiguo:<10} {wallet_vacios_nuevo:<10} {'+' + str(wallet_vacios_nuevo - wallet_vacios_antiguo) if wallet_vacios_nuevo > wallet_vacios_antiguo else str(wallet_vacios_nuevo - wallet_vacios_antiguo)}")
    print(f"{'Validación PK':<30} {es_valida_antigua:<10} {es_valida_nueva:<10} {'✅' if es_valida_nueva and not es_valida_antigua else '❌'}")
    
    # PASO 6: Mostrar muestra de datos
    print(f"\n📋 MUESTRA DE DATOS (primeros 10 registros):")
    print("WALLET_NUMBER - Antigua vs Nueva:")
    comparacion = pd.DataFrame({
        'Original': df_original['WALLET_NUMBER'].head(10),
        'Antigua': df_antiguo['WALLET_NUMBER'].head(10),
        'Nueva': df_nuevo['WALLET_NUMBER'].head(10)
    })
    print(comparacion.to_string(index=False))
    
    # PASO 7: Resultado final
    print(f"\n🎯 RESULTADO FINAL:")
    if es_valida_nueva and not es_valida_antigua and validos_nuevo > validos_antiguo:
        print("✅ ¡CORRECCIÓN EXITOSA! La nueva normalización preserva las claves primarias.")
        print("✅ Los valores problemáticos se mantienen como NaN en lugar de convertirse a strings vacías.")
        print("✅ La validación de clave primaria ahora pasa correctamente.")
    else:
        print("❌ La corrección no funcionó como esperado. Revisar la implementación.")
    
    print("\n" + "=" * 60)
    print("🧪 PRUEBAS COMPLETADAS")

if __name__ == "__main__":
    main()
