{
    "Comment": "An example of a workflow which sends an SNS notification when a Batch job succeeds or fails.",
    "StartAt": "TESTS_CONNECTIONS_JOB",
    "TimeoutSeconds": 3600,
    "QueryLanguage": "JSONata",
    "States": {
      "TESTS_CONNECTIONS_JOB": {
        "Type": "Task",
        "Resource": "arn:aws:states:::batch:submitJob.sync",
        "Next": "FLOW_PARALELO_IMG",
        "Arguments": {
          "JobName": "TESTS_CONNECTIONS_JOB",
          "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
          "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
          "ContainerOverrides": {
            "Command": [
              "python",
              "-m",
              "pytest",
              "tests/test_db_connections.py",
              "-v"
            ]
          }
        }
      },
      "FLOW_PARALELO_IMG": {
        "Type": "Parallel",
        "Branches": [
          {
            "StartAt": "IMG_FLOW_01",
            "States": {
              "IMG_FLOW_01": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_01",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_01"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_01"
              },
              "IMG_RAW_01": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_01",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:40",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_01"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          },
          {
            "StartAt": "IMG_FLOW_02",
            "States": {
              "IMG_FLOW_02": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_02",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_02"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_02"
              },
              "IMG_RAW_02": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_02",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:40",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_02"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          },
          {
            "StartAt": "IMG_FLOW_03",
            "States": {
              "IMG_FLOW_03": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_03",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_03"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_03"
              },
              "IMG_RAW_03": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_03",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:41",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_03"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          },
          {
            "StartAt": "IMG_FLOW_04",
            "States": {
              "IMG_FLOW_04": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_04",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_04"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_04"
              },
              "IMG_RAW_04": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_04",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:41",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_04"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          },
          {
            "StartAt": "IMG_FLOW_05",
            "States": {
              "IMG_FLOW_05": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_05",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_05"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_05"
              },
              "IMG_RAW_05": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_05",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:41",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_05"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          },
          {
            "StartAt": "IMG_FLOW_06",
            "States": {
              "IMG_FLOW_06": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_06",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_06"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_06"
              },
              "IMG_RAW_06": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_06",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:41",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_06"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          },
          {
            "StartAt": "IMG_FLOW_07",
            "States": {
              "IMG_FLOW_07": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_07",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_07"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_07"
              },
              "IMG_RAW_07": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_07",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:41",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_07"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          },
          {
            "StartAt": "IMG_FLOW_08",
            "States": {
              "IMG_FLOW_08": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_08",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_08"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_08"
              },
              "IMG_RAW_08": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_08",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:41",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_08"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          },
          {
            "StartAt": "IMG_FLOW_09",
            "States": {
              "IMG_FLOW_09": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_09",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_09"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_09"
              },
              "IMG_RAW_09": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_09",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:41",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_09"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          },
          {
            "StartAt": "IMG_FLOW_10",
            "States": {
              "IMG_FLOW_10": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_FLOW_10",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:43",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_landing.py",
                      "IMG_FLOW_10"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "Next": "IMG_RAW_10"
              },
              "IMG_RAW_10": {
                "Type": "Task",
                "Resource": "arn:aws:states:::batch:submitJob.sync",
                "Arguments": {
                  "JobName": "IMG_RAW_10",
                  "JobQueue": "arn:aws:batch:us-east-1:992382450203:job-queue/pdp-analytics-batch-ingest-queue",
                  "JobDefinition": "arn:aws:batch:us-east-1:992382450203:job-definition/pdp-analytics-batch-job-ingest-mob:41",
                  "ContainerOverrides": {
                    "Command": [
                      "python",
                      "app_raw.py",
                      "IMG_FLOW_10"
                    ]
                  }
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "States.ALL"
                    ],
                    "IntervalSeconds": 30,
                    "MaxAttempts": 2,
                    "BackoffRate": 1.5
                  }
                ],
                "End": true
              }
            }
          }
        ],
        "End": true
      }
    }
  }
  }