#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛠️ CORRECCIÓN ESPECÍFICA: Problema NaN en Consolidación
======================================================

Data Engineer: Corrección del problema de valores NaN agregados durante consolidación
Implementa las mejoras identificadas en el diagnóstico

Solución:
1. Verificación previa antes de reemplazar infinitos
2. Tracking de cambios durante conversión de fechas
3. Preservación estricta de valores originales

Fecha: 2025-06-02
"""

import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Agregar ruta del sistema
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def test_consolidacion_corregida():
    """
    Prueba la consolidación con las correcciones aplicadas para evitar NaN.
    """
    print("🛠️ PRUEBA DE CONSOLIDACIÓN CORREGIDA")
    print("="*50)
    print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Importar las funciones corregidas
        from app_raw_consolidado_puro import (
            leer_archivo_resumen,
            agrupar_archivos_por_tabla,
            consolidar_archivos_tabla_incremental_puro,
            leer_parquet_desde_s3
        )
        
        print("\n📋 PASO 1: Cargar resumen...")
        seccion_img = "IMG_FLOW_42"
        df_resumen = leer_archivo_resumen(seccion_img)
        
        if df_resumen.empty:
            print("❌ No se pudo cargar el resumen")
            return False
        
        print(f"✅ Resumen cargado: {len(df_resumen)} registros")
        
        print("\n📋 PASO 2: Identificar archivos MTX_WALLET_ORA...")
        grupos_archivos = agrupar_archivos_por_tabla(df_resumen)
        tabla_objetivo = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA"
        
        if tabla_objetivo not in grupos_archivos:
            print(f"❌ Tabla {tabla_objetivo} no encontrada")
            return False
        
        archivos_mtx = grupos_archivos[tabla_objetivo]
        print(f"✅ Encontrados {len(archivos_mtx)} archivos para consolidación")
        
        # Leer archivo origen para establecer baseline
        print("\n📋 PASO 3: Analizar archivo origen...")
        archivo_origen = archivos_mtx[0]['ruta_completa']
        bucket_origen = "prd-datalake-bronze-zone-637423440311"
        
        df_origen = leer_parquet_desde_s3(bucket_origen, archivo_origen)
        if df_origen.empty:
            print("❌ No se pudo leer archivo origen")
            return False
        
        print(f"✅ Archivo origen analizado:")
        print(f"   📊 Registros: {len(df_origen)}")
        print(f"   📊 Columnas: {len(df_origen.columns)}")
        
        # Contar NaN en origen
        nan_count_origen = {}
        for col in df_origen.columns:
            nan_count = df_origen[col].isnull().sum()
            if nan_count > 0:
                nan_count_origen[col] = nan_count
        
        print(f"   📊 Columnas con NaN en origen: {len(nan_count_origen)}")
        if nan_count_origen:
            for col, count in nan_count_origen.items():
                pct = (count / len(df_origen)) * 100
                print(f"      {col}: {count} NaN ({pct:.1f}%)")
        
        print("\n📋 PASO 4: Ejecutar consolidación corregida...")
        df_consolidado = consolidar_archivos_tabla_incremental_puro(tabla_objetivo, archivos_mtx)
        
        if df_consolidado.empty:
            print("❌ Consolidación resultó en DataFrame vacío")
            return False
        
        print(f"✅ Consolidación exitosa:")
        print(f"   📊 Registros consolidados: {len(df_consolidado)}")
        print(f"   📊 Columnas: {len(df_consolidado.columns)}")
        
        # Contar NaN en consolidado
        print("\n📋 PASO 5: Verificar NaN en consolidado...")
        nan_count_consolidado = {}
        for col in df_consolidado.columns:
            nan_count = df_consolidado[col].isnull().sum()
            if nan_count > 0:
                nan_count_consolidado[col] = nan_count
        
        print(f"   📊 Columnas con NaN en consolidado: {len(nan_count_consolidado)}")
        
        # Análisis de cambios en columnas comunes
        columnas_comunes = set(df_origen.columns).intersection(set(df_consolidado.columns))
        columnas_problema = []
        
        for col in columnas_comunes:
            nan_origen = df_origen[col].isnull().sum()
            nan_consolidado = df_consolidado[col].isnull().sum()
            
            if nan_consolidado > nan_origen:
                diferencia = nan_consolidado - nan_origen
                columnas_problema.append({
                    'columna': col,
                    'nan_agregados': diferencia,
                    'origen': nan_origen,
                    'consolidado': nan_consolidado
                })
        
        print(f"\n📋 PASO 6: Resultado del diagnóstico...")
        if columnas_problema:
            print(f"❌ PROBLEMA PERSISTE: {len(columnas_problema)} columnas con NaN agregados")
            for prob in columnas_problema:
                print(f"   {prob['columna']}: {prob['origen']} → {prob['consolidado']} (+{prob['nan_agregados']})")
            
            print(f"\n💡 Análisis adicional requerido:")
            print(f"   - Verificar si hay múltiples archivos origen con estructuras diferentes")
            print(f"   - Revisar lógica de merge/concatenación")
            print(f"   - Validar tipos de datos en archivos fuente")
            
            return False
        else:
            print(f"✅ ÉXITO: No se agregaron valores NaN durante la consolidación")
            print(f"   📊 Estructura preservada correctamente")
            print(f"   📊 Valores faltantes mantienen su estado original")
            return True
            
    except Exception as e:
        print(f"❌ Error durante la prueba: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Función principal."""
    print("🎯 CORRECCIÓN ESPECÍFICA: Problema valores NaN")
    print("="*60)
    
    success = test_consolidacion_corregida()
    
    print(f"\n{'='*60}")
    if success:
        print("🎉 CORRECCIÓN EXITOSA: Problema NaN resuelto")
        print("✅ La consolidación ahora preserva la estructura original sin agregar NaN")
    else:
        print("⚠️ CORRECCIÓN PARCIAL: Se requiere análisis adicional")
        print("🔍 Revisar logs anteriores para más detalles")
    
    print(f"⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
