#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para debuggear la lectura de configuración de llaves primarias
"""

import os
import sys
import pandas as pd

# Agregar el directorio actual al path para las importaciones
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from tabla_config_simple import (
    CONFIG_FILE, 
    TABLA_KEYS_CONFIG, 
    cargar_configuracion_simple,
    detectar_clave_primaria_tabla
)

def debug_config_lectura():
    """Debug completo de la lectura de configuración"""
    print("=" * 60)
    print("🔍 DEBUG: Lectura de configuración de llaves primarias")
    print("=" * 60)
    
    # 1. Verificar archivo de configuración
    print(f"📁 Archivo de configuración: {CONFIG_FILE}")
    print(f"📁 Existe: {os.path.exists(CONFIG_FILE)}")
    
    if os.path.exists(CONFIG_FILE):
        print(f"📁 Tamaño: {os.path.getsize(CONFIG_FILE)} bytes")
        
        # Leer contenido raw
        print("\n📄 Contenido RAW del archivo:")
        print("-" * 40)
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            contenido = f.read()
            print(contenido)
        print("-" * 40)
    
    # 2. Forzar recarga de configuración
    print("\n🔄 Recargando configuración...")
    cargar_configuracion_simple()
    
    # 3. Mostrar configuración cargada
    print(f"\n📋 Configuración cargada en memoria:")
    print(f"   Total tablas: {len(TABLA_KEYS_CONFIG)}")
    
    for tabla, claves in TABLA_KEYS_CONFIG.items():
        print(f"   {tabla} → {claves}")
    
    # 4. Probar detección específica para tabla MTX_WALLET_ORA
    print(f"\n🧪 Prueba específica: MTX_WALLET_ORA")
    
    # Crear DataFrame de prueba
    df_test = pd.DataFrame({
        'WALLET_NUMBER': ['W001', 'W002', 'W003'],
        'USER_ID': [1, 2, 3],
        'OTHER_COLUMN': ['A', 'B', 'C']
    })
    
    print(f"📊 DataFrame de prueba:")
    print(f"   Columnas: {list(df_test.columns)}")
    
    # Detectar clave primaria
    claves_detectadas = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_test)
    print(f"🔑 Claves detectadas: {claves_detectadas}")
    
    # 5. Probar con diferentes variaciones del nombre
    print(f"\n🧪 Pruebas con variaciones de nombre:")
    
    variaciones = [
        "MTX_WALLET_ORA",
        "mtx_wallet_ora", 
        "Mtx_Wallet_Ora",
        "MTX_WALLET",
        "WALLET_ORA"
    ]
    
    for variacion in variaciones:
        claves = detectar_clave_primaria_tabla(variacion, df_test)
        print(f"   {variacion} → {claves}")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    debug_config_lectura()
