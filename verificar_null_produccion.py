#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import boto3
import pandas as pd
from io import BytesIO
import warnings

warnings.filterwarnings('ignore')

def verificar_manejo_null_en_consolidado():
    """
    Verifica que los datos consolidados no tengan valores <null> sino cadenas vacías.
    """
    print("🔍 VERIFICANDO MANEJO DE NULL EN DATOS CONSOLIDADOS")
    print("=" * 60)
    
    try:
        # Conectar a S3
        s3_client = boto3.client('s3')
        bucket = "prd-datalake-silver-zone-637423440311"
        
        # Leer datos consolidados de IMG_FLOW_42
        prefix = "PDP_PRD010_MAINDBBUS/IMG_FLOW_42/"
        print(f"📂 Listando archivos en: {bucket}/{prefix}")
        
        response = s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix)
        
        if 'Contents' not in response:
            print("❌ No se encontraron archivos consolidados")
            return False
        
        # Tomar el primer archivo consolidado
        archivo = response['Contents'][0]['Key']
        print(f"📄 Analizando archivo: {archivo}")
        
        # Leer el archivo Parquet
        response = s3_client.get_object(Bucket=bucket, Key=archivo)
        buffer = BytesIO(response['Body'].read())
        df = pd.read_parquet(buffer)
        
        print(f"📊 Datos leídos: {len(df)} filas, {len(df.columns)} columnas")
        print()
        
        # Mostrar muestra de datos
        print("📋 MUESTRA DE DATOS CONSOLIDADOS:")
        print("-" * 40)
        print(df.head(10))
        print()
        
        # Verificar que NO hay valores <null> o similares
        print("🔍 VERIFICACIÓN DE VALORES NULL:")
        print("-" * 40)
        
        valores_null_encontrados = 0
        for col in df.columns:
            # Buscar diferentes representaciones de NULL
            null_values = df[col].isin([
                'null', 'NULL', '<null>', 'None', 'NONE', '<None>',
                'nan', 'NaN', 'nil', 'NIL', 'undefined', 'N/A', 'n/a'
            ]).sum()
            
            # Contar cadenas vacías (que es lo correcto)
            empty_strings = (df[col] == '').sum()
            
            print(f"   {col}:")
            print(f"      Valores NULL: {null_values}")
            print(f"      Cadenas vacías: {empty_strings}")
            
            if null_values > 0:
                print(f"      ❌ PROBLEMA: Encontrados {null_values} valores NULL")
                valores_encontrados = df[col][df[col].isin([
                    'null', 'NULL', '<null>', 'None', 'NONE', '<None>',
                    'nan', 'NaN', 'nil', 'NIL', 'undefined', 'N/A', 'n/a'
                ])].unique()
                print(f"      Valores específicos: {valores_encontrados}")
                valores_null_encontrados += null_values
            else:
                print(f"      ✅ CORRECTO: Sin valores NULL, solo cadenas vacías")
            print()
        
        # Mostrar valores únicos de columnas clave
        print("📈 VALORES ÚNICOS EN COLUMNAS CLAVE:")
        print("-" * 40)
        
        columnas_clave = ['USER_GRADE', 'DESCRIPTION', 'WALLET_REF_ID', 'ACCOUNT_GROUP_ID', 'ISSUER_ID']
        for col in columnas_clave:
            if col in df.columns:
                valores_unicos = df[col].unique()[:10]  # Mostrar solo primeros 10
                print(f"   {col}: {valores_unicos}")
        print()
        
        # Verificar que valores categóricos se mantuvieron
        print("📋 VERIFICACIÓN DE VALORES CATEGÓRICOS:")
        print("-" * 40)
        if 'USER_GRADE' in df.columns:
            opt_count = (df['USER_GRADE'] == 'OPT').sum()
            wallet_count = (df['USER_GRADE'] == 'WALLET').sum()
            empty_count = (df['USER_GRADE'] == '').sum()
            print(f"   USER_GRADE - OPT: {opt_count}")
            print(f"   USER_GRADE - WALLET: {wallet_count}")
            print(f"   USER_GRADE - Cadenas vacías: {empty_count}")
        
        print()
        print("🎯 RESUMEN DE LA VERIFICACIÓN:")
        print("=" * 60)
        
        if valores_null_encontrados == 0:
            print("✅ ÉXITO TOTAL: No se encontraron valores <null> en los datos consolidados")
            print("✅ Todos los valores NULL fueron convertidos correctamente a cadenas vacías")
            print("✅ Los valores categóricos se preservaron perfectamente")
            print("✅ El sistema está funcionando correctamente en producción")
            return True
        else:
            print(f"❌ PROBLEMA: Se encontraron {valores_null_encontrados} valores NULL")
            print("❌ Necesita revisión adicional")
            return False
        
    except Exception as e:
        print(f"❌ Error verificando datos: {str(e)}")
        return False

if __name__ == "__main__":
    exito = verificar_manejo_null_en_consolidado()
    
    if exito:
        print("\n🚀 SISTEMA FUNCIONANDO PERFECTAMENTE EN PRODUCCIÓN")
    else:
        print("\n⚠️  NECESITA REVISIÓN")
