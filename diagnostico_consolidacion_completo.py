#!/usr/bin/env python3
"""
Script para diagnosticar el problema completo de NaN durante el proceso de consolidación,
incluyendo la operación pd.concat() que parece ser donde se introducen los NaN.
"""

import pandas as pd
import numpy as np
import logging
from typing import Tuple

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def crear_datos_realistas():
    """Crea datos que simulan exactamente la situación real de MTX_WALLET_ORA"""
    
    # DataFrame existente (primer archivo Bronze)
    df_existente = pd.DataFrame({
        'WALLET_ID': [1, 2, 3, 4, 5],
        'USER_ID': ['U001', 'U002', 'U003', 'U004', 'U005'],
        'BALANCE': [100.50, 200.00, 150.75, 75.25, 300.00],
        'STATUS': ['ACTIVE', 'INACTIVE', 'ACTIVE', 'PENDING', 'ACTIVE'],
        'PIN_NUMBER': ['1234', np.nan, '5678', '9999', '1111'],
        'LAST_UPDATE': pd.to_datetime(['2025-01-01', '2025-01-02', np.nan, '2025-01-03', '2025-01-04']),
        'CREATED_DATE': pd.to_datetime(['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'])
    })
    
    # DataFrame nuevos (segundo archivo Bronze) - con COLUMNAS ADICIONALES
    df_nuevos = pd.DataFrame({
        'WALLET_ID': [6, 7, 8],
        'USER_ID': ['U006', 'U007', 'U008'],
        'BALANCE': [125.75, 180.50, 90.25],
        'STATUS': ['ACTIVE', 'INACTIVE', 'PENDING'],
        'PIN_NUMBER': ['2222', '3333', np.nan],
        'LAST_UPDATE': pd.to_datetime(['2025-01-05', '2025-01-06', '2025-01-07']),
        'CREATED_DATE': pd.to_datetime(['2024-01-06', '2024-01-07', '2024-01-08']),
        # COLUMNAS NUEVAS (estas son las que causan el problema)
        'PIN_MODIFIED_ON': pd.to_datetime(['2025-01-01', '2025-01-02', '2025-01-03']),
        'PIN_STATUS': ['ENABLED', 'DISABLED', 'ENABLED'],
        'PIN_REQUIRED': [True, False, True],
        'VALID_FROM_DATE': pd.to_datetime(['2024-12-01', '2024-12-02', '2024-12-03']),
        'EXPIRY_DATE': pd.to_datetime(['2025-12-01', '2025-12-02', '2025-12-03']),
        'LAST_TRANSFER_TYPE': ['DEPOSIT', 'WITHDRAWAL', 'TRANSFER'],
        'DESCRIPTION': ['Wallet 6', 'Wallet 7', 'Wallet 8']
    })
    
    return df_existente, df_nuevos

def simular_alinear_tipos_datos_original(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Simulación exacta de la función problemática"""
    
    logging.info("🔴 === ALINEAR_TIPOS_DATOS_PURO (ORIGINAL) ===")
    
    if df_existente.empty or df_nuevos.empty:
        return df_existente, df_nuevos
    
    columnas_comunes = set(df_existente.columns) & set(df_nuevos.columns)
    df_existente_copia = df_existente.copy()
    df_nuevos_copia = df_nuevos.copy()
    
    # Alinear tipos de columnas comunes
    for columna in columnas_comunes:
        tipo_existente = df_existente_copia[columna].dtype
        tipo_nuevo = df_nuevos_copia[columna].dtype
        
        if tipo_existente != tipo_nuevo:
            logging.info(f"🔄 Alineando tipos para {columna}: {tipo_existente} -> {tipo_nuevo} -> string")
            try:
                df_existente_copia[columna] = df_existente_copia[columna].astype(str)
                df_nuevos_copia[columna] = df_nuevos_copia[columna].astype(str)
            except Exception as e:
                logging.warning(f"⚠️ Error alineando tipos para {columna}: {e}")
    
    # PROBLEMA: Agregar columnas faltantes con valores por defecto
    columnas_solo_existente = set(df_existente.columns) - set(df_nuevos.columns)
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    
    logging.info(f"📝 Columnas solo en nuevos: {sorted(columnas_solo_nuevos)}")
    
    for columna in columnas_solo_nuevos:
        logging.info(f"➕ Agregando columna '{columna}' al existente con valor ''")
        df_existente_copia[columna] = ''  # AQUÍ ESTÁ EL PROBLEMA
        
    for columna in columnas_solo_existente:
        logging.info(f"➕ Agregando columna '{columna}' al nuevo con valor ''")
        df_nuevos_copia[columna] = ''
    
    return df_existente_copia, df_nuevos_copia

def simular_proceso_consolidacion_completo(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame) -> pd.DataFrame:
    """Simula el proceso completo de consolidación incluyendo pd.concat()"""
    
    logging.info("🔄 === PROCESO COMPLETO DE CONSOLIDACIÓN ===")
    
    # Paso 1: Alinear tipos de datos (función problemática)
    df_existente_alineado, df_nuevos_alineado = simular_alinear_tipos_datos_original(df_existente, df_nuevos)
    
    # Contar NaN después de alineación
    nan_existente_post_alineacion = df_existente_alineado.isna().sum().sum()
    nan_nuevos_post_alineacion = df_nuevos_alineado.isna().sum().sum()
    logging.info(f"📊 NaN después de alineación - Existente: {nan_existente_post_alineacion}, Nuevos: {nan_nuevos_post_alineacion}")
    
    # Paso 2: Agregar claves de registro (simulado)
    df_existente_alineado['_clave_registro'] = 'clave_' + df_existente_alineado['WALLET_ID'].astype(str)
    df_nuevos_alineado['_clave_registro'] = 'clave_' + df_nuevos_alineado['WALLET_ID'].astype(str)
    
    # Paso 3: Identificar tipos de registros
    claves_existentes = set(df_existente_alineado['_clave_registro'])
    claves_nuevas = set(df_nuevos_alineado['_clave_registro'])
    
    # Como no hay overlapping, todos los registros son para insertar
    df_mantener = df_existente_alineado
    df_insertar = df_nuevos_alineado
    
    logging.info(f"📦 Registros a mantener: {len(df_mantener)}")
    logging.info(f"➕ Registros a insertar: {len(df_insertar)}")
    
    # Paso 4: pd.concat() - AQUÍ ES DONDE PUEDE OCURRIR EL PROBLEMA
    logging.info("🔗 Ejecutando pd.concat()...")
    
    # Mostrar tipos de datos antes del concat
    logging.info("📋 TIPOS DE DATOS ANTES DE pd.concat():")
    for col in df_mantener.columns:
        tipo_mantener = df_mantener[col].dtype
        tipo_insertar = df_insertar[col].dtype if col in df_insertar.columns else "N/A"
        if tipo_mantener != tipo_insertar and tipo_insertar != "N/A":
            logging.warning(f"   ⚠️ {col}: Mantener={tipo_mantener}, Insertar={tipo_insertar}")
    
    # Contar NaN ANTES del concat
    nan_mantener_antes = df_mantener.isna().sum().sum()
    nan_insertar_antes = df_insertar.isna().sum().sum()
    logging.info(f"📊 NaN ANTES concat - Mantener: {nan_mantener_antes}, Insertar: {nan_insertar_antes}")
    
    # ESTE ES EL PASO CRÍTICO
    dataframes_finales = [df_mantener, df_insertar]
    
    # Vamos a probar diferentes parámetros de pd.concat()
    logging.info("🧪 Probando pd.concat() con diferentes parámetros...")
    
    # Versión 1: Con sort=True (comportamiento por defecto en versiones más recientes)
    try:
        df_final_sort_true = pd.concat(dataframes_finales, ignore_index=True, sort=True)
        nan_final_sort_true = df_final_sort_true.isna().sum().sum()
        logging.info(f"   pd.concat(sort=True): {nan_final_sort_true} NaN")
    except Exception as e:
        logging.error(f"   Error con sort=True: {e}")
        df_final_sort_true = None
    
    # Versión 2: Con sort=False
    try:
        df_final_sort_false = pd.concat(dataframes_finales, ignore_index=True, sort=False)
        nan_final_sort_false = df_final_sort_false.isna().sum().sum()
        logging.info(f"   pd.concat(sort=False): {nan_final_sort_false} NaN")
    except Exception as e:
        logging.error(f"   Error con sort=False: {e}")
        df_final_sort_false = None
    
    # Versión 3: Sin especificar sort (comportamiento por defecto)
    try:
        df_final_default = pd.concat(dataframes_finales, ignore_index=True)
        nan_final_default = df_final_default.isna().sum().sum()
        logging.info(f"   pd.concat(default): {nan_final_default} NaN")
    except Exception as e:
        logging.error(f"   Error con default: {e}")
        df_final_default = None
    
    # Análisis detallado de las diferencias
    if df_final_sort_true is not None and df_final_sort_false is not None:
        logging.info("🔍 ANÁLISIS DETALLADO DE DIFERENCIAS:")
        
        # Análisis por columnas
        for col in df_final_sort_true.columns:
            if col != '_clave_registro':
                nan_sort_true = df_final_sort_true[col].isna().sum()
                nan_sort_false = df_final_sort_false[col].isna().sum()
                
                if nan_sort_true != nan_sort_false:
                    logging.warning(f"   🚨 {col}: sort=True({nan_sort_true}) vs sort=False({nan_sort_false})")
    
    # Remover columna auxiliar y retornar
    if df_final_sort_false is not None:
        df_final_sort_false.drop('_clave_registro', axis=1, inplace=True)
        return df_final_sort_false
    elif df_final_default is not None:
        df_final_default.drop('_clave_registro', axis=1, inplace=True)
        return df_final_default
    else:
        return pd.DataFrame()

def main():
    """Función principal"""
    logging.info("🚀 DIAGNÓSTICO COMPLETO DEL PROCESO DE CONSOLIDACIÓN")
    
    # Crear datos realistas
    df_existente, df_nuevos = crear_datos_realistas()
    
    logging.info("\n" + "="*80)
    logging.info("📋 DATOS INICIALES")
    logging.info(f"DF Existente: {df_existente.shape}")
    logging.info(f"   Columnas: {list(df_existente.columns)}")
    logging.info(f"   NaN originales: {df_existente.isna().sum().sum()}")
    
    logging.info(f"DF Nuevos: {df_nuevos.shape}")
    logging.info(f"   Columnas: {list(df_nuevos.columns)}")
    logging.info(f"   NaN originales: {df_nuevos.isna().sum().sum()}")
    
    # PROCESO COMPLETO DE CONSOLIDACIÓN
    logging.info("\n" + "="*80)
    df_consolidado = simular_proceso_consolidacion_completo(df_existente, df_nuevos)
    
    # ANÁLISIS FINAL
    logging.info("\n" + "="*80)
    logging.info("🎯 RESUMEN FINAL")
    logging.info(f"DataFrame original 1 NaN: {df_existente.isna().sum().sum()}")
    logging.info(f"DataFrame original 2 NaN: {df_nuevos.isna().sum().sum()}")
    logging.info(f"DataFrame consolidado NaN: {df_consolidado.isna().sum().sum()}")
    
    diferencia_nan = df_consolidado.isna().sum().sum() - (df_existente.isna().sum().sum() + df_nuevos.isna().sum().sum())
    logging.info(f"🚨 DIFERENCIA NaN: +{diferencia_nan}")
    
    # Análisis por columnas para identificar exactamente qué columnas tienen NaN adicionales
    logging.info("\n📊 ANÁLISIS POR COLUMNAS DE NaN ADICIONALES:")
    for col in df_consolidado.columns:
        nan_consolidado = df_consolidado[col].isna().sum()
        nan_original_1 = df_existente[col].isna().sum() if col in df_existente.columns else 0
        nan_original_2 = df_nuevos[col].isna().sum() if col in df_nuevos.columns else 0
        nan_total_original = nan_original_1 + nan_original_2
        
        if nan_consolidado > nan_total_original:
            diferencia = nan_consolidado - nan_total_original
            logging.warning(f"   🚨 {col}: +{diferencia} NaN adicionales (Original: {nan_total_original}, Consolidado: {nan_consolidado})")

if __name__ == "__main__":
    main()
