#!/usr/bin/env python3
"""
Script para probar las optimizaciones de Oracle en modo rápido
"""

import sys
import os
import time
import logging
import pathlib

# Configurar path como en app_landing.py
project_root = pathlib.Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))

from src.etl.fast_mode_manager import FastModeManager

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('oracle_optimization_test.log')
    ]
)

def test_oracle_optimizations():
    """
    Prueba las optimizaciones de Oracle en modo rápido
    """
    print("🚀 INICIANDO PRUEBA DE OPTIMIZACIONES ORACLE")
    print("=" * 60)
    
    # Configuración de prueba (usando la misma configuración que funcionó antes)
    query_info = {
        'name': 'ACCOUNT_BALANCE_SUMMARY',
        'db_parent': 'ORACLE_PROD',
        'db_type': 'oracle_prod',
        'db_config': {
            'host': 'localhost',
            'port': 1521,
            'database': 'XEPDB1',
            'user': 'FLOW_ETL',
            'password': 'flow_etl_2024'
        },
        'query': '''
        SELECT 
            ACCOUNT_ID,
            BALANCE_DATE,
            CURRENT_BALANCE,
            AVAILABLE_BALANCE,
            PENDING_BALANCE,
            CURRENCY_CODE,
            ACCOUNT_TYPE,
            BRANCH_CODE,
            CUSTOMER_ID,
            LAST_TRANSACTION_DATE,
            ATTR_1_VALUE,
            ATTR_2_VALUE,
            ATTR_3_VALUE,
            REMARKS,
            BANK_ID,
            CREATED_DATE,
            UPDATED_DATE
        FROM ACCOUNT_BALANCE_SUMMARY
        ''',
        'rango_columna': 'BALANCE_DATE',
        'rango_inicio': '2024-01-01',
        'rango_fin': '2024-01-02'
    }
    
    try:
        # Crear instancia del FastModeManager
        fast_manager = FastModeManager()
        
        print(f"📊 Configuración de prueba:")
        print(f"   - Tabla: {query_info['name']}")
        print(f"   - Base de datos: {query_info['db_type']}")
        print(f"   - Rango de fechas: {query_info['rango_inicio']} a {query_info['rango_fin']}")
        print()
        
        # Medir tiempo total
        start_time = time.time()
        
        print("🔧 OPTIMIZACIONES IMPLEMENTADAS:")
        print("   ✅ defaultRowPrefetch: 50,000 (optimizado)")
        print("   ✅ useCursorFetch: true (crítico)")
        print("   ✅ defaultFetchSize: 50,000 (crítico)")
        print("   ✅ useServerPrepStmts: true (crítico)")
        print("   ✅ cachePrepStmts: true (crítico)")
        print("   ✅ useCompression: true (crítico)")
        print("   ✅ cursor.arraysize: 50,000 (optimizado)")
        print("   ✅ chunk_size: 50,000 (optimizado)")
        print("   ✅ Parámetros de sesión Oracle optimizados")
        print("   ✅ JVM memoria optimizada: inicial=512MB, máxima=4GB")
        print("   ✅ Manejo correcto de fechas en particionamiento")
        print()
        
        # Ejecutar en modo rápido
        print("⚡ EJECUTANDO MODO RÁPIDO OPTIMIZADO...")
        resultados = fast_manager.ejecutar_modo_rapido(query_info)
        
        # Calcular tiempo total
        total_time = time.time() - start_time
        
        # Mostrar resultados
        print()
        print("📈 RESULTADOS DE LA OPTIMIZACIÓN:")
        print("=" * 50)
        print(f"⏱️  Tiempo total: {total_time:.2f} segundos")
        print(f"📊 Registros procesados: {resultados.get('registros_totales', 0):,}")
        print(f"📁 Archivos generados: {len(resultados.get('archivos_procesados', []))}")
        
        if total_time > 0 and resultados.get('registros_totales', 0) > 0:
            velocidad = resultados['registros_totales'] / total_time
            print(f"🚀 Velocidad: {velocidad:,.0f} registros/segundo")
        
        print()
        print("🎯 COMPARACIÓN CON VERSIÓN ANTERIOR:")
        print("   - Tiempo anterior: ~200 segundos (3:20)")
        print(f"   - Tiempo actual: {total_time:.2f} segundos")
        if total_time > 0:
            mejora = ((200 - total_time) / 200) * 100
            print(f"   - Mejora: {mejora:.1f}% más rápido")
        
        print()
        if total_time < 120:  # Menos de 2 minutos
            print("🎉 ¡EXCELENTE! Las optimizaciones funcionaron perfectamente")
        elif total_time < 180:  # Menos de 3 minutos
            print("✅ ¡BUENO! Hay mejora significativa en el rendimiento")
        else:
            print("⚠️  Rendimiento similar, pueden necesitarse más optimizaciones")
            
        return True
        
    except Exception as e:
        print(f"❌ ERROR durante la prueba: {str(e)}")
        logging.error(f"Error en test_oracle_optimizations: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    print("🔍 PRUEBA DE OPTIMIZACIONES ORACLE - MODO RÁPIDO")
    print("Verificando mejoras de rendimiento implementadas...")
    print()
    
    success = test_oracle_optimizations()
    
    if success:
        print("\n✅ Prueba completada exitosamente")
    else:
        print("\n❌ Prueba falló - revisar logs para más detalles")
        sys.exit(1)
