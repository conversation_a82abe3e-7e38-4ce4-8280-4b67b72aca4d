#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para ejecutar la consolidación de MTX_WALLET_ORA sin interacción del usuario
"""

import boto3
import pandas as pd
import logging
from io import BytesIO
from botocore.exceptions import ClientError
import sys
from typing import List
import hashlib

# Importar configuración simple de tablas
from tabla_config_simple import detectar_clave_primaria_tabla, validar_clave_primaria

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Constantes
S3_RAW_BUCKET = "prd-datalake-silver-zone-637423440311"
TABLA_PATH = "PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/2025/05/29"
TABLA_NOMBRE = "USER_MODIFICATION_HISTORY_ORA"
CONSOLIDADO_PATH = "PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA"

def verificar_archivo_existe(bucket: str, key: str) -> bool:
    """Verifica si un archivo existe en S3."""
    try:
        s3_client = boto3.client('s3')
        s3_client.head_object(Bucket=bucket, Key=key)
        return True
    except ClientError as e:
        if e.response['Error']['Code'] == '404':
            return False
        else:
            raise

def leer_parquet_desde_s3(bucket: str, key: str) -> pd.DataFrame:
    """Lee un archivo parquet desde S3."""
    try:
        s3_client = boto3.client('s3')
        obj = s3_client.get_object(Bucket=bucket, Key=key)
        return pd.read_parquet(BytesIO(obj['Body'].read()))
    except Exception as e:
        logging.error(f"Error leyendo {key}: {str(e)}")
        return pd.DataFrame()

def escribir_parquet_a_s3(df: pd.DataFrame, bucket: str, key: str) -> None:
    """Escribe un DataFrame como parquet a S3."""
    buffer = BytesIO()
    df.to_parquet(buffer, index=False)
    buffer.seek(0)
    
    s3_client = boto3.client('s3')
    s3_client.put_object(Bucket=bucket, Key=key, Body=buffer.getvalue())

def listar_archivos_tabla(bucket: str, prefix: str) -> List[str]:
    """Lista archivos .parquet en S3 para una tabla."""
    s3_client = boto3.client('s3')
    archivos = []
    
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
            if 'Contents' in page:
                for obj in page['Contents']:
                    if obj['Key'].endswith('.parquet'):
                        archivos.append(obj['Key'])
    except Exception as e:
        logging.error(f"Error listando archivos: {str(e)}")
    
    return archivos

def generar_clave_registro(row: pd.Series, claves_primarias: List[str]) -> str:
    """Genera una clave única para el registro basada en las columnas de clave primaria."""
    valores_clave = [str(row[col]) for col in claves_primarias]
    valores_string = '|'.join(valores_clave)
    return hashlib.md5(valores_string.encode('utf-8')).hexdigest()

def main():
    """Función principal"""
    try:
        print(f"🚀 Iniciando consolidación automática de USER_MODIFICATION_HISTORY_ORA")
        print(f"📂 Tabla objetivo: {TABLA_NOMBRE}")
        print(f"📍 Ruta de origen: s3://{S3_RAW_BUCKET}/{TABLA_PATH}")
        print(f"📍 Ruta consolidado: s3://{S3_RAW_BUCKET}/{CONSOLIDADO_PATH}/consolidado.parquet")
        
        # 1. Listar archivos particionados
        archivos_particionados = listar_archivos_tabla(S3_RAW_BUCKET, TABLA_PATH)
        
        if not archivos_particionados:
            logging.warning(f"⚠️ No se encontraron archivos particionados en {TABLA_PATH}")
            return
        
        print(f"📁 Archivos encontrados: {len(archivos_particionados)}")
        for archivo in archivos_particionados:
            print(f"   - {archivo}")
        
        # 2. Verificar si ya existe un consolidado
        consolidado_key = f"{CONSOLIDADO_PATH}/consolidado.parquet"
        if verificar_archivo_existe(S3_RAW_BUCKET, consolidado_key):
            print(f"⚠️ Ya existe un archivo consolidado: {consolidado_key}")
            print(f"🔄 Sobrescribiendo con nuevos datos...")
        
        # 3. Leer todos los archivos particionados
        print(f"📖 Leyendo {len(archivos_particionados)} archivos particionados...")
        dataframes = []
        total_registros = 0
        
        for archivo in archivos_particionados:
            print(f"   Leyendo: {archivo}")
            df = leer_parquet_desde_s3(S3_RAW_BUCKET, archivo)
            if not df.empty:
                dataframes.append(df)
                total_registros += len(df)
                print(f"      ✅ {len(df)} registros leídos")
            else:
                print(f"      ⚠️ Archivo vacío")
        
        if not dataframes:
            print(f"⚠️ No hay datos para consolidar")
            return
        
        # 4. Combinar todos los DataFrames
        print(f"🔄 Combinando {len(dataframes)} DataFrames...")
        df_combinado = pd.concat(dataframes, ignore_index=True)
        print(f"📊 Total registros combinados: {len(df_combinado)}")
        
        # 5. Detectar clave primaria
        print(f"🔑 Detectando clave primaria para {TABLA_NOMBRE}...")
        claves_primarias = detectar_clave_primaria_tabla(TABLA_NOMBRE, df_combinado)
        
        if claves_primarias:
            print(f"✅ Clave primaria detectada: {claves_primarias}")
            
            # 6. Eliminar duplicados
            print(f"🔄 Eliminando duplicados usando clave primaria...")
            
            df_combinado['_clave_registro'] = df_combinado.apply(
                lambda row: generar_clave_registro(row, claves_primarias), axis=1
            )
            
            registros_antes = len(df_combinado)
            df_combinado = df_combinado.drop_duplicates(subset=['_clave_registro'], keep='last')
            df_combinado.drop('_clave_registro', axis=1, inplace=True)
            
            registros_despues = len(df_combinado)
            duplicados_eliminados = registros_antes - registros_despues
            
            print(f"📈 Registros antes: {registros_antes}")
            print(f"📉 Registros después: {registros_despues}")
            print(f"🗑️ Duplicados eliminados: {duplicados_eliminados}")
        else:
            print(f"⚠️ No se pudo detectar clave primaria, conservando todos los registros")
        
        # 7. Escribir archivo consolidado
        print(f"💾 Escribiendo archivo consolidado...")
        escribir_parquet_a_s3(df_combinado, S3_RAW_BUCKET, consolidado_key)
        
        # 8. Estadísticas finales
        print(f"\n✅ Consolidación completada exitosamente:")
        print(f"   📂 Tabla: {TABLA_NOMBRE}")
        print(f"   📁 Archivos procesados: {len(archivos_particionados)}")
        print(f"   📊 Registros finales: {len(df_combinado)}")
        print(f"   💾 Archivo consolidado: s3://{S3_RAW_BUCKET}/{consolidado_key}")
        
        # Obtener tamaño del archivo final
        try:
            s3_client = boto3.client('s3')
            obj_info = s3_client.head_object(Bucket=S3_RAW_BUCKET, Key=consolidado_key)
            size_mb = obj_info['ContentLength'] / (1024 * 1024)
            print(f"   📏 Tamaño final: {size_mb:.2f} MB")
        except Exception as e:
            print(f"   ⚠️ No se pudo obtener el tamaño del archivo: {str(e)}")
            
        print(f"\n🎉 Proceso completado exitosamente")
        
    except Exception as e:
        print(f"❌ Error en el proceso: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
