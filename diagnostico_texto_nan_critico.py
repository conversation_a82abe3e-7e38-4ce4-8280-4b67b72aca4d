#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico Crítico: Valores 'nan' en Texto
===========================================

PROBLEMA IDENTIFICADO:
- Origen: Valores NaN reales (numpy.nan)
- Destino: Strings "nan" (texto en minúsculas)

Este script identifica exactamente dónde y cómo se está produciendo 
la conversión incorrecta de NaN a string "nan".

Autor: Sistema ETL - Ingeniero de Datos
Fecha: 2025-06-02
"""

import pandas as pd
import numpy as np
import boto3
import logging
from io import BytesIO
from typing import Dict, List, Any, Tuple

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Configuración de buckets
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"

def leer_parquet_s3(bucket: str, key: str) -> pd.DataFrame:
    """Lee archivo Parquet desde S3."""
    try:
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df = pd.read_parquet(buffer)
        return df
    except Exception as e:
        logging.error(f"Error leyendo {bucket}/{key}: {str(e)}")
        return pd.DataFrame()

def analizar_tipos_valores_nan(df: pd.DataFrame, etiqueta: str) -> Dict[str, Any]:
    """
    Analiza tipos de datos y valores NaN de forma detallada.
    
    Returns:
        Dict con análisis completo de tipos y valores NaN
    """
    resultado = {
        'etiqueta': etiqueta,
        'total_registros': len(df),
        'total_columnas': len(df.columns),
        'analisis_por_columna': {},
        'strings_nan_encontrados': {},
        'tipos_datos': {}
    }
    
    for col in df.columns:
        serie = df[col]
        
        # Análisis de tipos
        dtype_info = {
            'dtype': str(serie.dtype),
            'dtype_category': pd.api.types.infer_dtype(serie, skipna=True),
            'total_valores': len(serie),
            'valores_nulos_pandas': serie.isnull().sum(),
            'valores_nulos_numpy': serie.isna().sum()
        }
        
        # CRÍTICO: Buscar strings "nan" específicamente
        strings_nan_count = 0
        if serie.dtype == 'object':  # Solo en columnas de texto
            try:
                # Contar valores que son exactamente el string "nan"
                strings_nan_count = (serie.astype(str).str.lower() == 'nan').sum()
                
                # Obtener algunos ejemplos
                ejemplos_nan = serie[serie.astype(str).str.lower() == 'nan'].head(3).tolist()
                dtype_info['strings_nan_ejemplos'] = ejemplos_nan
                
            except Exception as e:
                logging.warning(f"Error analizando strings 'nan' en {col}: {str(e)}")
        
        dtype_info['strings_nan_count'] = strings_nan_count
        
        # Valores únicos (muestra)
        try:
            valores_unicos = serie.value_counts(dropna=False).head(10)
            dtype_info['valores_mas_frecuentes'] = valores_unicos.to_dict()
        except:
            dtype_info['valores_mas_frecuentes'] = "Error obteniendo valores únicos"
        
        resultado['analisis_por_columna'][col] = dtype_info
        resultado['tipos_datos'][col] = str(serie.dtype)
        
        if strings_nan_count > 0:
            resultado['strings_nan_encontrados'][col] = strings_nan_count
    
    return resultado

def comparar_origen_destino_detallado():
    """
    Compara origen vs destino enfocándose en el problema de strings 'nan'.
    """
    print("🔍 DIAGNÓSTICO CRÍTICO: Conversión NaN → 'nan' (string)")
    print("=" * 60)
    
    # 1. Cargar archivos origen (Bronze)
    origen_files = [
        "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040917_chunk_0.parquet",
        "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2025/06/02/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040552_chunk_0.parquet"
    ]
    
    print("\n📁 CARGANDO ARCHIVOS ORIGEN (Bronze Zone)...")
    df_origen_combined = pd.DataFrame()
    
    for file_path in origen_files:
        print(f"  • Cargando: {file_path}")
        df_file = leer_parquet_s3(S3_BRONZE_BUCKET, file_path)
        if not df_file.empty:
            df_origen_combined = pd.concat([df_origen_combined, df_file], ignore_index=True)
            print(f"    ✅ Cargado: {len(df_file)} registros")
        else:
            print(f"    ❌ Error cargando archivo")
    
    print(f"\n📊 ORIGEN TOTAL: {len(df_origen_combined)} registros")
    
    # 2. Cargar archivo destino (Silver)
    destino_file = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    print(f"\n📁 CARGANDO ARCHIVO DESTINO (Silver Zone): {destino_file}")
    df_destino = leer_parquet_s3(S3_SILVER_BUCKET, destino_file)
    print(f"📊 DESTINO TOTAL: {len(df_destino)} registros")
    
    if df_origen_combined.empty or df_destino.empty:
        print("❌ Error: No se pudieron cargar los archivos")
        return
    
    # 3. Análisis detallado de tipos y valores NaN
    print(f"\n🔬 ANÁLISIS DETALLADO DE TIPOS Y VALORES NaN")
    print("=" * 50)
    
    analisis_origen = analizar_tipos_valores_nan(df_origen_combined, "ORIGEN (Bronze)")
    analisis_destino = analizar_tipos_valores_nan(df_destino, "DESTINO (Silver)")
    
    # 4. Comparación crítica: Strings "nan"
    print(f"\n🚨 PROBLEMA CRÍTICO: STRINGS 'nan' INTRODUCIDOS")
    print("=" * 50)
    
    strings_nan_origen = analisis_origen['strings_nan_encontrados']
    strings_nan_destino = analisis_destino['strings_nan_encontrados']
    
    print(f"📊 Strings 'nan' en ORIGEN: {sum(strings_nan_origen.values())} total")
    print(f"📊 Strings 'nan' en DESTINO: {sum(strings_nan_destino.values())} total")
    
    if strings_nan_destino and not strings_nan_origen:
        print(f"\n🚨 PROBLEMA CONFIRMADO:")
        print(f"   • ORIGEN: No tiene strings 'nan'")
        print(f"   • DESTINO: Tiene {sum(strings_nan_destino.values())} strings 'nan'")
        print(f"   • CONCLUSIÓN: El proceso ETL está convirtiendo NaN → 'nan'")
        
        print(f"\n📋 COLUMNAS AFECTADAS:")
        for col, count in strings_nan_destino.items():
            print(f"   • {col}: {count} valores convertidos a 'nan'")
    
    # 5. Análisis de cambios de tipos
    print(f"\n📊 CAMBIOS DE TIPOS DE DATOS")
    print("=" * 40)
    
    for col in df_origen_combined.columns:
        tipo_origen = analisis_origen['tipos_datos'].get(col, 'NO_ENCONTRADO')
        tipo_destino = analisis_destino['tipos_datos'].get(col, 'NO_ENCONTRADO')
        
        if tipo_origen != tipo_destino:
            print(f"🔄 {col}:")
            print(f"   • ORIGEN: {tipo_origen}")
            print(f"   • DESTINO: {tipo_destino}")
            print(f"   • CAMBIO: {'✅ VÁLIDO' if 'object' not in tipo_destino else '❌ PROBLEMÁTICO'}")
    
    # 6. Muestra específica de valores problemáticos
    print(f"\n🔍 EJEMPLOS DE VALORES PROBLEMÁTICOS")
    print("=" * 40)
    
    for col in strings_nan_destino.keys():
        print(f"\n📝 Columna: {col}")
        
        # Valores en origen para esta columna
        valores_origen = df_origen_combined[col].head(10)
        print(f"   ORIGEN (primeros 10 valores):")
        for i, val in enumerate(valores_origen):
            print(f"     [{i}] {repr(val)} (tipo: {type(val).__name__})")
        
        # Valores en destino para esta columna
        valores_destino = df_destino[col].head(10)
        print(f"   DESTINO (primeros 10 valores):")
        for i, val in enumerate(valores_destino):
            print(f"     [{i}] {repr(val)} (tipo: {type(val).__name__})")
            if isinstance(val, str) and val.lower() == 'nan':
                print(f"         🚨 PROBLEMA: String 'nan' detectado!")
    
    # 7. Resumen ejecutivo del problema
    print(f"\n📋 RESUMEN EJECUTIVO")
    print("=" * 30)
    print(f"🎯 PROBLEMA: Conversión incorrecta NaN → string 'nan'")
    print(f"📊 IMPACTO: {sum(strings_nan_destino.values())} valores afectados")
    print(f"🔧 CAUSA PROBABLE: Conversión de tipos durante consolidación")
    print(f"⚡ ACCIÓN REQUERIDA: Revisar función alinear_tipos_datos_puro()")
    
    return {
        'analisis_origen': analisis_origen,
        'analisis_destino': analisis_destino,
        'strings_nan_origen': strings_nan_origen,
        'strings_nan_destino': strings_nan_destino
    }

if __name__ == "__main__":
    try:
        resultado = comparar_origen_destino_detallado()
        print(f"\n✅ Diagnóstico completado")
        
    except Exception as e:
        logging.error(f"Error en diagnóstico: {str(e)}")
        print(f"❌ Error ejecutando diagnóstico: {str(e)}")
