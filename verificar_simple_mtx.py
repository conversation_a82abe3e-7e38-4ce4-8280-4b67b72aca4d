#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script simple para verificar MTX_WALLET_ORA
"""

import pandas as pd
import logging

# Configurar logging básico
logging.basicConfig(level=logging.INFO)

def main():
    print("🔍 Verificando MTX_WALLET_ORA...")
    
    s3_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado.parquet"
    
    try:
        print("📁 Leyendo archivo...")
        df = pd.read_parquet(s3_path)
        
        print(f"✅ Leído: {len(df):,} filas, {len(df.columns)} columnas")
        
        # Verificar WALLET_NUMBER
        wallet_unique = df['WALLET_NUMBER'].nunique()
        wallet_total = len(df)
        wallet_nulls = df['WALLET_NUMBER'].isnull().sum()
        
        print(f"📋 WALLET_NUMBER:")
        print(f"   Únicos: {wallet_unique:,}")
        print(f"   Total: {wallet_total:,}")
        print(f"   Nulos: {wallet_nulls:,}")
        print(f"   Duplicados: {wallet_total - wallet_unique:,}")
        
        # Verificar RNUM si existe
        if 'RNUM' in df.columns:
            rnum_unique = df['RNUM'].nunique()
            rnum_nulls = df['RNUM'].isnull().sum()
            print(f"📋 RNUM:")
            print(f"   Únicos: {rnum_unique:,}")
            print(f"   Nulos: {rnum_nulls:,}")
            print(f"   Es clave válida: {rnum_unique == wallet_total and rnum_nulls == 0}")
        
        print("✅ Verificación completada")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
