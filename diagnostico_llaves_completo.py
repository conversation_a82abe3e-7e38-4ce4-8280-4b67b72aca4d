#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para diagnosticar problemas específicos con llaves primarias en tiempo real
"""

import os
import sys
import pandas as pd
import logging
import warnings

# Suprimir warnings de pandas
warnings.filterwarnings('ignore')

# Configurar logging detallado
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Agregar el directorio actual al path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from tabla_config_simple import (
    detectar_clave_primaria_tabla,
    validar_clave_primaria,
    TABLA_KEYS_CONFIG
)

def diagnosticar_problema_llaves():
    """Diagnóstico completo del problema de llaves primarias"""
    print("\n" + "="*80)
    print("🔧 DIAGNÓSTICO COMPLETO: Problemas con Llaves Primarias")
    print("="*80)
    
    # 1. Estado de la configuración
    print(f"\n📋 ESTADO DE LA CONFIGURACIÓN:")
    print(f"   Total tablas configuradas: {len(TABLA_KEYS_CONFIG)}")
    
    for tabla, claves in TABLA_KEYS_CONFIG.items():
        print(f"   ✅ {tabla} → {claves}")
    
    # 2. Probar todas las tablas configuradas con datos simulados
    print(f"\n🧪 PRUEBAS DE TODAS LAS TABLAS CONFIGURADAS:")
    
    resultados_pruebas = {}
    
    for tabla_nombre, claves_configuradas in TABLA_KEYS_CONFIG.items():
        print(f"\n   🔍 Probando: {tabla_nombre}")
        
        # Crear DataFrame simulado para esta tabla
        df_simulado = crear_dataframe_simulado(tabla_nombre, claves_configuradas)
        
        if df_simulado.empty:
            print(f"   ❌ No se pudo crear DataFrame simulado")
            resultados_pruebas[tabla_nombre] = "ERROR_SIMULACION"
            continue
        
        # Detectar clave primaria
        claves_detectadas = detectar_clave_primaria_tabla(tabla_nombre, df_simulado)
        
        # Validar clave primaria
        es_valida = False
        if claves_detectadas:
            es_valida = validar_clave_primaria(df_simulado, claves_detectadas)
        
        # Resultado
        if claves_detectadas == claves_configuradas and es_valida:
            print(f"   ✅ ÉXITO: Detectó {claves_detectadas} y validó correctamente")
            resultados_pruebas[tabla_nombre] = "EXITO"
        elif claves_detectadas == claves_configuradas and not es_valida:
            print(f"   ⚠️ DETECTÓ BIEN, PERO VALIDACIÓN FALLÓ: {claves_detectadas}")
            resultados_pruebas[tabla_nombre] = "VALIDACION_FALLO"
        elif claves_detectadas != claves_configuradas:
            print(f"   ❌ DETECCIÓN INCORRECTA: Esperado {claves_configuradas}, Obtuvo {claves_detectadas}")
            resultados_pruebas[tabla_nombre] = "DETECCION_INCORRECTA"
        else:
            print(f"   ❌ ERROR GENERAL")
            resultados_pruebas[tabla_nombre] = "ERROR_GENERAL"
    
    # 3. Resumen de resultados
    print(f"\n📊 RESUMEN DE RESULTADOS:")
    exitos = sum(1 for r in resultados_pruebas.values() if r == "EXITO")
    total = len(resultados_pruebas)
    print(f"   Éxitos: {exitos}/{total} ({(exitos/total)*100:.1f}%)")
    
    for tabla, resultado in resultados_pruebas.items():
        emoji = {"EXITO": "✅", "VALIDACION_FALLO": "⚠️", "DETECCION_INCORRECTA": "❌", "ERROR_GENERAL": "❌", "ERROR_SIMULACION": "💥"}
        print(f"   {emoji.get(resultado, '❓')} {tabla}: {resultado}")
    
    # 4. Consejos específicos basados en los resultados
    print(f"\n💡 CONSEJOS PARA RESOLVER PROBLEMAS:")
    
    problemas_validacion = [t for t, r in resultados_pruebas.items() if r == "VALIDACION_FALLO"]
    problemas_deteccion = [t for t, r in resultados_pruebas.items() if r == "DETECCION_INCORRECTA"]
    
    if problemas_validacion:
        print(f"   🔍 PROBLEMAS DE VALIDACIÓN ({len(problemas_validacion)} tablas):")
        print(f"       - Verificar que los datos reales no tengan valores nulos/vacíos en claves primarias")
        print(f"       - Verificar que no haya duplicados en las claves primarias")
        print(f"       - Tablas afectadas: {', '.join(problemas_validacion)}")
    
    if problemas_deteccion:
        print(f"   🔍 PROBLEMAS DE DETECCIÓN ({len(problemas_deteccion)} tablas):")
        print(f"       - Verificar que las columnas configuradas existan en los datos reales")
        print(f"       - Verificar que los nombres de columnas coincidan exactamente (case-sensitive)")
        print(f"       - Tablas afectadas: {', '.join(problemas_deteccion)}")
    
    if not problemas_validacion and not problemas_deteccion:
        print(f"   ✅ ¡Todas las configuraciones están funcionando correctamente!")
        print(f"   💡 Si tienes problemas específicos, ejecuta el proceso real y revisa los logs")
    
    print("\n" + "="*80)

def crear_dataframe_simulado(tabla_nombre, claves_configuradas):
    """Crea un DataFrame simulado para probar una tabla específica"""
    try:
        # Datos base comunes
        datos_base = {
            'USER_ID': [1001, 1002, 1003],
            'STATUS': ['ACTIVE', 'ACTIVE', 'INACTIVE'],
            'CREATION_DATE': ['2024-01-01', '2024-01-02', '2024-01-03']
        }
        
        # Agregar las columnas específicas de la clave primaria
        for clave in claves_configuradas:
            if clave not in datos_base:
                # Generar valores únicos para la clave
                if 'ID' in clave:
                    datos_base[clave] = [f"{clave}_{i}" for i in range(1, 4)]
                elif 'NUMBER' in clave:
                    datos_base[clave] = [f"NUM_{i:03d}" for i in range(1, 4)]
                elif 'CODE' in clave:
                    datos_base[clave] = [f"CODE_{i}" for i in range(1, 4)]
                else:
                    datos_base[clave] = [f"VAL_{i}" for i in range(1, 4)]
        
        # Crear DataFrame
        df = pd.DataFrame(datos_base)
        return df
        
    except Exception as e:
        logging.error(f"Error creando DataFrame simulado para {tabla_nombre}: {str(e)}")
        return pd.DataFrame()

if __name__ == "__main__":
    diagnosticar_problema_llaves()
