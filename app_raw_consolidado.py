#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import boto3
import pandas as pd
import argparse
import logging
import warnings
from io import BytesIO
from botocore.exceptions import ClientError
import os
from typing import List, Dict, Tuple, Optional
import sys
from collections import defaultdict
import hashlib
import numpy as np
from datetime import datetime, date
import warnings

# Suprimir warnings de pandas
warnings.filterwarnings('ignore')

def normalizar_tipos_datos(df: pd.DataFrame) -> pd.DataFrame:
    """
    Normaliza los tipos de datos del DataFrame para ser compatibles con Parquet.
    
    Args:
        df (pd.DataFrame): DataFrame original
        
    Returns:
        pd.DataFrame: DataFrame con tipos de datos normalizados
    """
    if df.empty:
        return df
    
    df_normalizado = df.copy()
    
    for col in df_normalizado.columns:
        try:
            # Verificar tipo de datos de la columna
            dtype = df_normalizado[col].dtype
            
            # Convertir columnas object a string de manera segura
            if dtype == 'object':
                # Verificar si la columna contiene timestamps o fechas en formato string
                sample_values = df_normalizado[col].dropna().head(100)
                
                if not sample_values.empty:
                    # Lista de valores que claramente NO son timestamps, aunque contengan caracteres que podrían confundir
                    valores_no_timestamp = {
                        'WALLET', 'OPT', 'OPERATOR', 'ADMIN', 'USER', 'GUEST', 'CLIENT', 'CUSTOMER',
                        'ACTIVE', 'INACTIVE', 'PENDING', 'APPROVED', 'REJECTED', 'CANCELLED',
                        'YES', 'NO', 'TRUE', 'FALSE', 'ON', 'OFF', 'ENABLED', 'DISABLED',
                        'HIGH', 'MEDIUM', 'LOW', 'CRITICAL', 'WARNING', 'INFO', 'ERROR',
                        'BRONZE', 'SILVER', 'GOLD', 'PLATINUM', 'VIP', 'PREMIUM', 'BASIC'
                    }
                    
                    # Detectar timestamps en formato string de manera más inteligente
                    is_timestamp = False
                    timestamp_count = 0
                    
                    # Primero verificar si hay valores que claramente NO son timestamps
                    valores_no_timestamp_encontrados = 0
                    for val in sample_values:
                        if isinstance(val, str):
                            val_upper = str(val).strip().upper()
                            if val_upper in valores_no_timestamp:
                                valores_no_timestamp_encontrados += 1
                    
                    # Si más del 30% son valores claramente no-timestamp, no convertir
                    if valores_no_timestamp_encontrados > len(sample_values) * 0.3:
                        is_timestamp = False
                        logging.info(f"Columna {col}: No convertir a timestamp - contiene valores como WALLET/OPT/OPERATOR")
                    else:
                        # Proceder con detección normal de timestamps
                        for val in sample_values:
                            if isinstance(val, str):
                                # Criterios más estrictos para detectar timestamps
                                val_clean = str(val).strip()
                                
                                # Patrones que SÍ indican timestamp:
                                # - Contiene 'T' Y longitud apropiada (formato ISO)
                                # - Formato completo YYYY-MM-DD HH:MM:SS
                                # - Solo fecha YYYY-MM-DD pero debe ser fecha real
                                
                                if (
                                    # ISO format con T
                                    ('T' in val_clean and len(val_clean) >= 19) or
                                    # Formato completo con fecha + hora
                                    (len(val_clean) >= 16 and 
                                     (
                                        # Formato YYYY-MM-DD HH:MM:SS
                                        (val_clean.count('-') == 2 and val_clean.count(':') >= 1 and 
                                         val_clean[:4].isdigit() and val_clean[5:7].isdigit()) or
                                        # Formato DD/MM/YYYY HH:MM:SS  
                                        (val_clean.count('/') == 2 and val_clean.count(':') >= 1 and
                                         val_clean[6:10].isdigit())
                                     )
                                    )
                                ):
                                    timestamp_count += 1
                        
                        # Solo considerar timestamp si al menos 70% de los valores lo parecen
                        if len(sample_values) > 0 and (timestamp_count / len(sample_values)) >= 0.7:
                            is_timestamp = True
                    
                    if is_timestamp:
                        # Convertir timestamps string a datetime, luego a string ISO
                        try:
                            df_normalizado[col] = pd.to_datetime(
                                df_normalizado[col], 
                                errors='coerce',
                                infer_datetime_format=True
                            ).dt.strftime('%Y-%m-%d %H:%M:%S')
                            logging.info(f"Columna {col} convertida de timestamp string a formato ISO string")
                        except Exception as e:
                            # Si falla, convertir a string simple
                            df_normalizado[col] = df_normalizado[col].astype(str)
                            logging.warning(f"Error convirtiendo timestamp en columna {col}, convertida a string: {str(e)}")
                    else:
                        # Convertir object a string de manera segura
                        df_normalizado[col] = df_normalizado[col].astype(str)
            
            # Manejar tipos de timestamp problemáticos
            elif dtype.name.startswith('datetime') or 'timestamp' in str(dtype).lower():
                try:
                    # Convertir timestamps a string ISO para evitar problemas de serialización
                    df_normalizado[col] = pd.to_datetime(df_normalizado[col], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')
                    logging.info(f"Columna {col} convertida de timestamp a string ISO")
                except Exception as e:
                    # Si falla, convertir a string
                    df_normalizado[col] = df_normalizado[col].astype(str)
                    logging.warning(f"Error convirtiendo timestamp en columna {col}: {str(e)}")
            
            # Manejar tipos de fecha problemáticos
            elif dtype.name.startswith('date') or 'date' in str(dtype).lower():
                try:
                    # Convertir fechas a string ISO
                    df_normalizado[col] = pd.to_datetime(df_normalizado[col], errors='coerce').dt.strftime('%Y-%m-%d')
                    logging.info(f"Columna {col} convertida de fecha a string ISO")
                except Exception as e:
                    # Si falla, convertir a string
                    df_normalizado[col] = df_normalizado[col].astype(str)
                    logging.warning(f"Error convirtiendo fecha en columna {col}: {str(e)}")
            
            # Manejar tipos numéricos con valores especiales
            elif np.issubdtype(dtype, np.floating):
                # Reemplazar infinitos y NaN problemáticos
                df_normalizado[col] = df_normalizado[col].replace([np.inf, -np.inf], np.nan)
                # Convertir a float64 estándar
                df_normalizado[col] = df_normalizado[col].astype('float64')
            
            elif np.issubdtype(dtype, np.integer):
                # Manejar enteros con valores problemáticos
                try:
                    # Convertir a int64 estándar si es posible
                    df_normalizado[col] = pd.to_numeric(df_normalizado[col], errors='coerce').astype('int64')
                except Exception as e:
                    # Si falla, convertir a string
                    df_normalizado[col] = df_normalizado[col].astype(str)
                    logging.debug(f"Columna {col} convertida a string: {str(e)}")
            
            # Manejar tipos categóricos
            elif hasattr(df_normalizado[col], 'cat'):
                df_normalizado[col] = df_normalizado[col].astype(str)
                
        except Exception as e:
            # Si todo falla, convertir a string como último recurso
            logging.warning(f"Error normalizando columna {col}, convirtiendo a string: {str(e)}")
            df_normalizado[col] = df_normalizado[col].astype(str)
    
    # Reemplazar valores problemáticos generales de manera más agresiva
    for col in df_normalizado.columns:
        if df_normalizado[col].dtype == 'object':
            # Primero llenar valores NaN/null reales con cadena vacía
            df_normalizado[col] = df_normalizado[col].fillna('')
            
            # Lista extensa de representaciones de NULL que deben convertirse a cadena vacía
            null_representations = [
                'nan', 'NaN', 'null', 'NULL', '<null>', 'Null',
                'none', 'None', 'NONE', '<None>', '<none>', '<NULL>',
                'nil', 'NIL', 'Nil', '<nil>', '<NIL>', '<Nil>',
                'undefined', 'UNDEFINED', 'Undefined',
                'na', 'NA', 'N/A', 'n/a'
            ]
            
            # Reemplazar todas las representaciones de NULL
            df_normalizado[col] = df_normalizado[col].replace(null_representations, '')
            
            # Manejo especial para 'None' string - ser más inteligente
            mask_none = (df_normalizado[col] == 'None')
            if mask_none.any():
                none_count = (df_normalizado[col] == 'None').sum()
                total_count = len(df_normalizado[col])
                
                # Obtener valores únicos sin contar 'None' y cadenas vacías
                unique_non_null = df_normalizado[col][(df_normalizado[col] != 'None') & 
                                                     (df_normalizado[col] != '')].unique()
                
                # Si solo hay 1-2 valores únicos reales además de 'None', 
                # probablemente 'None' representa NULL
                if len(unique_non_null) <= 2:
                    df_normalizado[col] = df_normalizado[col].replace('None', '')
                    logging.info(f"Columna {col}: Reemplazados {none_count} valores 'None' por cadena vacía (pocos valores únicos)")
                
                # Si 'None' representa más del 30% de los datos, probablemente es NULL
                elif (none_count / total_count) > 0.3:
                    df_normalizado[col] = df_normalizado[col].replace('None', '')
                    logging.info(f"Columna {col}: Reemplazados {none_count} valores 'None' por cadena vacía (>30% de datos)")
                
                # Si la columna parece ser categórica pero tiene muchos 'None', tratar como NULL
                elif col.upper() in ['DESCRIPTION', 'COMMENT', 'NOTES', 'REMARKS'] and (none_count / total_count) > 0.1:
                    df_normalizado[col] = df_normalizado[col].replace('None', '')
                    logging.info(f"Columna {col}: Reemplazados {none_count} valores 'None' por cadena vacía (campo de descripción)")
                
                else:
                    logging.info(f"Columna {col}: Mantenidos {none_count} valores 'None' (parecen datos válidos)")
            
            # Limpiar espacios en blanco extra y cadenas que solo tienen espacios
            df_normalizado[col] = df_normalizado[col].astype(str).str.strip()
            df_normalizado[col] = df_normalizado[col].replace(['', ' ', '  ', '   '], '')
            
            logging.debug(f"Columna {col}: Normalización selectiva completada")
        
        # También manejar columnas numéricas que pueden tener NULLs
        else:
            # Para columnas numéricas, manejar NaN de manera consistente
            if df_normalizado[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                # Para enteros con NaN, convertir a string para mantener consistencia
                if df_normalizado[col].isna().any() and 'int' in str(df_normalizado[col].dtype):
                    df_normalizado[col] = df_normalizado[col].astype(str).replace('nan', '')
                    logging.debug(f"Columna numérica {col}: Convertida a string para manejar NaN consistentemente")
                else:
                    # Mantener como numérica pero limpiar NaN
                    df_normalizado[col] = df_normalizado[col].fillna(0 if 'int' in str(df_normalizado[col].dtype) else np.nan)
                    logging.debug(f"Columna numérica {col}: NaN manejados apropiadamente")
    
    logging.info(f"Normalización de tipos completada. Columnas procesadas: {len(df_normalizado.columns)}")
    return df_normalizado

# Importar configuración simple de tablas
from tabla_config_simple import detectar_clave_primaria_tabla, validar_clave_primaria

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Constantes
S3_LANDING_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_RAW_BUCKET = "prd-datalake-silver-zone-637423440311"

def leer_archivo_resumen(seccion_img: str) -> pd.DataFrame:
    """
    Lee el archivo de resumen Parquet de S3.
    
    Args:
        seccion_img (str): Nombre de la sección (ej: IMG_FLOW_04)
        
    Returns:
        pd.DataFrame: DataFrame con el contenido del archivo de resumen
    """
    try:
        s3_client = boto3.client('s3')
        resumen_key = f"RESUMEN/{seccion_img}/RESUMEN_{seccion_img}.parquet"
        
        try:
            response = s3_client.get_object(Bucket=S3_LANDING_BUCKET, Key=resumen_key)
            buffer = BytesIO(response['Body'].read())
            df = pd.read_parquet(buffer)
            logging.info(f"Archivo de resumen leído exitosamente: {len(df)} registros encontrados")
            return df
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                logging.warning(f"No se encontró el archivo de resumen: {resumen_key}")
                return pd.DataFrame()  # Retornar DataFrame vacío en lugar de lanzar error
            else:
                logging.warning(f"Error accediendo al archivo: {str(e)}")
                return pd.DataFrame()  # Retornar DataFrame vacío para otros errores
            
    except Exception as e:
        logging.warning(f"Error leyendo archivo de resumen: {str(e)}")
        return pd.DataFrame()  # Retornar DataFrame vacío para cualquier otro error

def verificar_archivo_existe(bucket: str, key: str) -> bool:
    """
    Verifica si un archivo existe en S3.
    
    Args:
        bucket (str): Nombre del bucket
        key (str): Ruta del archivo
        
    Returns:
        bool: True si existe, False si no
    """
    try:
        s3_client = boto3.client('s3')
        s3_client.head_object(Bucket=bucket, Key=key)
        return True
    except ClientError as e:
        if e.response['Error']['Code'] == '404':
            return False
        else:
            raise

def leer_parquet_desde_s3(bucket: str, key: str) -> pd.DataFrame:
    """
    Lee un archivo Parquet desde S3 y lo retorna como DataFrame.
    
    Args:
        bucket (str): Nombre del bucket
        key (str): Ruta del archivo
        
    Returns:
        pd.DataFrame: DataFrame con el contenido del archivo
    """
    try:
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df = pd.read_parquet(buffer)
        return df
    except Exception as e:
        logging.warning(f"Error leyendo Parquet desde S3 {bucket}/{key}: {str(e)}")
        return pd.DataFrame()

def escribir_parquet_a_s3(df: pd.DataFrame, bucket: str, key: str) -> None:
    """
    Escribe un DataFrame como archivo Parquet en S3.
    
    Args:
        df (pd.DataFrame): DataFrame a escribir
        bucket (str): Nombre del bucket
        key (str): Ruta donde escribir el archivo
    """
    try:
        # Normalizar tipos de datos antes de escribir
        logging.info(f"Normalizando tipos de datos antes de escribir {bucket}/{key}")
        df_normalizado = normalizar_tipos_datos(df)
        
        s3_client = boto3.client('s3')
        buffer = BytesIO()
        df_normalizado.to_parquet(buffer, index=False, engine='pyarrow')
        buffer.seek(0)
        
        s3_client.put_object(
            Bucket=bucket,
            Key=key,
            Body=buffer.getvalue(),
            ContentType='application/octet-stream'
        )
        logging.info(f"Archivo Parquet consolidado escrito exitosamente: {bucket}/{key} ({len(df_normalizado)} registros)")
        
    except Exception as e:
        logging.error(f"Error escribiendo Parquet a S3 {bucket}/{key}: {str(e)}")
        # Intentar obtener más detalles del error
        if "timestamp" in str(e).lower() or "datetime" in str(e).lower():
            logging.error("Error relacionado con timestamp/datetime detectado. Verifique los tipos de datos de fecha/hora.")
        raise

def listar_archivos_subcarpeta(bucket: str, prefix: str) -> List[str]:
    """
    Lista todos los archivos en una subcarpeta de S3.
    
    Args:
        bucket (str): Nombre del bucket
        prefix (str): Prefijo de la subcarpeta
        
    Returns:
        List[str]: Lista de rutas de archivos encontrados
    """
    try:
        s3_client = boto3.client('s3')
        archivos = []
        
        paginator = s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
            if 'Contents' in page:
                archivos.extend([obj['Key'] for obj in page['Contents']])
                
        return archivos
    except Exception as e:
        logging.warning(f"Error listando archivos en {bucket}/{prefix}: {str(e)}")
        raise

def limpiar_subcarpeta_destino(bucket: str, prefix: str) -> None:
    """
    Elimina todos los archivos existentes en una subcarpeta de S3.
    
    Args:
        bucket (str): Nombre del bucket
        prefix (str): Prefijo de la subcarpeta
    """
    try:
        # Listar archivos existentes
        archivos = listar_archivos_subcarpeta(bucket, prefix)
        
        if archivos:
            s3_client = boto3.client('s3')
            logging.info(f"Encontrados {len(archivos)} archivos en {bucket}/{prefix}")
            
            # Eliminar archivos en lotes de 1000 (límite de S3)
            for i in range(0, len(archivos), 1000):
                batch = archivos[i:i + 1000]
                objects_to_delete = [{'Key': key} for key in batch]
                
                s3_client.delete_objects(
                    Bucket=bucket,
                    Delete={'Objects': objects_to_delete}
                )
            
            logging.info(f"Eliminados {len(archivos)} archivos de {bucket}/{prefix}")
        else:
            logging.info(f"No se encontraron archivos para eliminar en {bucket}/{prefix}")
            
    except Exception as e:
        logging.warning(f"Error limpiando subcarpeta {bucket}/{prefix}: {str(e)}")
        raise

def agrupar_archivos_por_tabla(df: pd.DataFrame) -> Dict[str, List[Dict]]:
    """
    Agrupa los archivos por db_parent y tabla_nombre.
    
    Args:
        df (pd.DataFrame): DataFrame con la información de los archivos
        
    Returns:
        Dict[str, List[Dict]]: Diccionario agrupado por tabla
    """
    grupos = defaultdict(list)
    
    for _, row in df.iterrows():
        # Clave de agrupación: db_parent/tabla_nombre
        clave_tabla = f"{row['db_parent']}/{row['tabla_nombre']}"
        
        # Información del archivo
        archivo_info = {
            'nombre_archivo': row['nombre_archivo'],
            'año': row['año'],
            'mes': row['mes'],
            'dia': row['dia'],
            'ruta_completa': f"{row['db_parent']}/{row['tabla_nombre']}/{row['año']}/{row['mes']}/{row['dia']}/{row['nombre_archivo']}"
        }
        
        grupos[clave_tabla].append(archivo_info)
    
    logging.info(f"Archivos agrupados en {len(grupos)} tablas diferentes")
    for tabla, archivos in grupos.items():
        logging.info(f"  - {tabla}: {len(archivos)} archivos")
    
    return dict(grupos)

def generar_clave_registro(row: pd.Series, tabla_path: str = "", claves_primarias: Optional[List[str]] = None) -> str:
    """
    Genera una clave única para un registro basada en la clave primaria del negocio.
    Detecta automáticamente la clave primaria más apropiada para cada tabla.
    
    Args:
        row (pd.Series): Fila del DataFrame
        tabla_path (str): Ruta de la tabla para contexto
        claves_primarias (Optional[List[str]]): Claves primarias ya detectadas (opcional)
        
    Returns:
        str: Clave única del registro
    """
    # Si ya se proporcionaron las claves primarias, usarlas
    if claves_primarias:
        try:
            if len(claves_primarias) == 1:
                clave_valor = str(row[claves_primarias[0]]).strip()
                if clave_valor and clave_valor.lower() not in ['nan', 'none', '']:
                    return clave_valor
            else:
                # Clave compuesta
                valores_clave = []
                for col in claves_primarias:
                    if col in row.index:
                        valor = str(row[col]).strip()
                        if valor and valor.lower() not in ['nan', 'none', '']:
                            valores_clave.append(valor)
                
                if len(valores_clave) == len(claves_primarias):
                    return '|'.join(valores_clave)
        except Exception as e:
            logging.warning(f"Error usando claves primarias {claves_primarias}: {str(e)}")
    
    # Detectar automáticamente la clave primaria basada en la tabla
    tabla_nombre = tabla_path.split('/')[-1] if tabla_path else "unknown"
    
    # Crear DataFrame temporal para detectar clave primaria
    df_temp = pd.DataFrame([row])
    claves_detectadas = detectar_clave_primaria_tabla(tabla_nombre, df_temp)
    
    if claves_detectadas:
        try:
            if len(claves_detectadas) == 1:
                clave_valor = str(row[claves_detectadas[0]]).strip()
                if clave_valor and clave_valor.lower() not in ['nan', 'none', '']:
                    logging.info(f"Usando clave primaria detectada para {tabla_nombre}: {claves_detectadas[0]} = {clave_valor}")
                    return clave_valor
            else:
                # Clave compuesta detectada
                valores_clave = []
                for col in claves_detectadas:
                    if col in row.index:
                        valor = str(row[col]).strip()
                        if valor and valor.lower() not in ['nan', 'none', '']:
                            valores_clave.append(valor)
                
                if len(valores_clave) == len(claves_detectadas):
                    clave_compuesta = '|'.join(valores_clave)
                    logging.info(f"Usando clave primaria compuesta detectada para {tabla_nombre}: {claves_detectadas} = {clave_compuesta}")
                    return clave_compuesta
        except Exception as e:
            logging.warning(f"Error usando claves detectadas {claves_detectadas}: {str(e)}")
    
    # Fallback: usar hash completo (comportamiento anterior)
    logging.warning(f"No se pudo detectar clave primaria válida para {tabla_nombre}, usando hash completo como clave")
    
    # Columnas de metadatos a excluir de la comparación de duplicados
    columnas_metadatos = [
        'DATA_LAKE_PARTITION_DATE',
        'RNUM', 
        'data_lake_last_modified'
    ]
    
    # Filtrar columnas excluyendo metadatos
    columnas_datos = [col for col in sorted(row.index) if col not in columnas_metadatos]
    
    # Crear string con los valores de las columnas de datos únicamente
    valores_string = '|'.join(str(row[col]) for col in columnas_datos)
    
    # Generar hash MD5 para evitar problemas con caracteres especiales
    return hashlib.md5(valores_string.encode('utf-8')).hexdigest()

def leer_consolidado_existente(tabla_path: str) -> pd.DataFrame:
    """
    Lee el archivo consolidado existente si existe.
    
    Args:
        tabla_path (str): Ruta de la tabla (db_parent/tabla_nombre)
        
    Returns:
        pd.DataFrame: DataFrame consolidado existente o vacío si no existe
    """
    archivo_consolidado_key = f"{tabla_path}/consolidado.parquet"
    
    if verificar_archivo_existe(S3_RAW_BUCKET, archivo_consolidado_key):
        logging.info(f"Encontrado archivo consolidado existente: {archivo_consolidado_key}")
        df_existente = leer_parquet_desde_s3(S3_RAW_BUCKET, archivo_consolidado_key)
        if not df_existente.empty:
            # Normalizar tipos de datos del archivo existente
            df_existente = normalizar_tipos_datos(df_existente)
            logging.info(f"Archivo consolidado leído y normalizado: {len(df_existente)} registros existentes")
            return df_existente
        else:
            logging.warning(f"Archivo consolidado existe pero está vacío")
            return pd.DataFrame()
    else:
        logging.info(f"No existe archivo consolidado previo, será primera consolidación")
        return pd.DataFrame()

def alinear_tipos_datos(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Alinea los tipos de datos entre el DataFrame existente y nuevo para evitar errores en el merge.
    
    Args:
        df_existente (pd.DataFrame): DataFrame consolidado existente
        df_nuevos (pd.DataFrame): DataFrame con nuevos datos
        
    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: DataFrames con tipos alineados
    """
    if df_existente.empty or df_nuevos.empty:
        return df_existente, df_nuevos
    
    # Obtener columnas comunes
    columnas_comunes = set(df_existente.columns) & set(df_nuevos.columns)
    
    if not columnas_comunes:
        logging.warning("No hay columnas comunes entre DataFrames existente y nuevo")
        return df_existente, df_nuevos
    
    df_existente_copia = df_existente.copy()
    df_nuevos_copia = df_nuevos.copy()
    
    for columna in columnas_comunes:
        tipo_existente = df_existente_copia[columna].dtype
        tipo_nuevo = df_nuevos_copia[columna].dtype
        
        # Si los tipos son diferentes, convertir ambos a string como tipo común
        if tipo_existente != tipo_nuevo:
            logging.info(f"Alineando tipos para columna {columna}: {tipo_existente} -> {tipo_nuevo} -> string")
            try:
                df_existente_copia[columna] = df_existente_copia[columna].astype(str)
                df_nuevos_copia[columna] = df_nuevos_copia[columna].astype(str)
            except Exception as e:
                logging.warning(f"Error alineando tipos para columna {columna}: {str(e)}")
    
    # Agregar columnas faltantes con valores por defecto
    columnas_solo_existente = set(df_existente.columns) - set(df_nuevos.columns)
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    
    for columna in columnas_solo_nuevos:
        df_existente_copia[columna] = ''  # Valor por defecto
        logging.info(f"Agregada columna faltante al existente: {columna}")
    
    for columna in columnas_solo_existente:
        df_nuevos_copia[columna] = ''  # Valor por defecto
        logging.info(f"Agregada columna faltante al nuevo: {columna}")
    
    return df_existente_copia, df_nuevos_copia

def merge_incremental(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame, tabla_path: str) -> pd.DataFrame:
    """
    Realiza un merge incremental entre datos existentes y nuevos.
    
    Args:
        df_existente (pd.DataFrame): Datos existentes en el consolidado
        df_nuevos (pd.DataFrame): Nuevos datos a procesar
        tabla_path (str): Ruta de la tabla para logging
        
    Returns:
        pd.DataFrame: DataFrame consolidado final
    """
    if df_existente.empty:
        logging.info(f"Primera consolidación para {tabla_path}: {len(df_nuevos)} registros nuevos")
        return df_nuevos
    
    if df_nuevos.empty:
        logging.info(f"No hay datos nuevos para {tabla_path}, manteniendo {len(df_existente)} registros existentes")
        return df_existente
    
    logging.info(f"Iniciando merge incremental para {tabla_path}")
    
    # Alinear tipos de datos antes del merge
    logging.info("Alineando tipos de datos entre DataFrames existente y nuevo")
    df_existente_alineado, df_nuevos_alineado = alinear_tipos_datos(df_existente, df_nuevos)
    
    # Detectar clave primaria para esta tabla
    tabla_nombre = tabla_path.split('/')[-1]
    claves_primarias = detectar_clave_primaria_tabla(tabla_nombre, df_nuevos_alineado)
    
    # Validar clave primaria detectada
    if claves_primarias and validar_clave_primaria(df_nuevos_alineado, claves_primarias):
        logging.info(f"Usando clave primaria detectada para {tabla_path}: {claves_primarias}")
    else:
        claves_primarias = None
        logging.warning(f"No se pudo detectar clave primaria válida para {tabla_path}, usando hash completo")
    
    # Generar claves únicas para ambos DataFrames alineados
    df_existente_copia = df_existente_alineado.copy()
    df_nuevos_copia = df_nuevos_alineado.copy()
    
    df_existente_copia['_clave_registro'] = df_existente_copia.apply(
        lambda row: generar_clave_registro(row, tabla_path, claves_primarias), axis=1
    )
    df_nuevos_copia['_clave_registro'] = df_nuevos_copia.apply(
        lambda row: generar_clave_registro(row, tabla_path, claves_primarias), axis=1
    )
    
    # Alinear tipos de datos entre existente y nuevos
    df_existente_copia, df_nuevos_copia = alinear_tipos_datos(df_existente_copia, df_nuevos_copia)
    
    # Identificar tipos de registros
    claves_existentes = set(df_existente_copia['_clave_registro'])
    claves_nuevas = set(df_nuevos_copia['_clave_registro'])
    
    # Registros que se mantienen (están en existentes pero NO en nuevos)
    claves_mantener = claves_existentes - claves_nuevas
    df_mantener = df_existente_copia[df_existente_copia['_clave_registro'].isin(claves_mantener)]
    
    # Registros completamente nuevos (están en nuevos pero NO en existentes) - INSERTAR
    claves_insertar = claves_nuevas - claves_existentes
    df_insertar = df_nuevos_copia[df_nuevos_copia['_clave_registro'].isin(claves_insertar)]
    
    # Registros que existen en ambos (misma clave) - ACTUALIZAR con versión más reciente
    claves_actualizar = claves_existentes & claves_nuevas
    df_actualizar = df_nuevos_copia[df_nuevos_copia['_clave_registro'].isin(claves_actualizar)]
    
    # Estadísticas del merge
    logging.info(f"=== Estadísticas de merge para {tabla_path} ===")
    logging.info(f"Clave primaria utilizada: {claves_primarias if claves_primarias else 'Hash completo'}")
    logging.info(f"Registros existentes: {len(df_existente_copia)}")
    logging.info(f"Registros nuevos procesados: {len(df_nuevos_copia)}")
    logging.info(f"  - Registros mantenidos (sin cambios): {len(df_mantener)}")
    logging.info(f"  - Registros nuevos (insertar): {len(df_insertar)}")
    logging.info(f"  - Registros actualizados (reemplazar): {len(df_actualizar)}")
    
    # Combinar: mantener + insertar + actualizar (NO duplicados)
    dataframes_finales = []
    
    if not df_mantener.empty:
        dataframes_finales.append(df_mantener)
    if not df_insertar.empty:
        dataframes_finales.append(df_insertar)
    if not df_actualizar.empty:
        dataframes_finales.append(df_actualizar)
    
    if dataframes_finales:
        df_final = pd.concat(dataframes_finales, ignore_index=True)
        # Eliminar la columna auxiliar de clave
        df_final.drop('_clave_registro', axis=1, inplace=True)
        
        logging.info(f"Merge completado: {len(df_final)} registros totales en el consolidado final")
        return df_final
    else:
        logging.warning(f"No hay datos para consolidar en {tabla_path}")
        return pd.DataFrame()

def consolidar_archivos_tabla_incremental(tabla_path: str, archivos_info: List[Dict]) -> pd.DataFrame:
    """
    Consolida archivos de una tabla usando lógica incremental.
    
    Args:
        tabla_path (str): Ruta de la tabla (db_parent/tabla_nombre)
        archivos_info (List[Dict]): Lista con información de los archivos
        
    Returns:
        pd.DataFrame: DataFrame consolidado final
    """
    # 1. Leer datos existentes (si existen)
    df_existente = leer_consolidado_existente(tabla_path)
    
    # 2. Leer y consolidar nuevos datos
    df_nuevos = consolidar_archivos_nuevos(tabla_path, archivos_info)
    
    # 3. Hacer merge incremental
    df_final = merge_incremental(df_existente, df_nuevos, tabla_path)
    
    return df_final

def consolidar_archivos_nuevos(tabla_path: str, archivos_info: List[Dict]) -> pd.DataFrame:
    """
    Consolida solo los archivos nuevos de una tabla.
    
    Args:
        tabla_path (str): Ruta de la tabla (db_parent/tabla_nombre)
        archivos_info (List[Dict]): Lista con información de los archivos
        
    Returns:
        pd.DataFrame: DataFrame con los nuevos datos consolidados
    """
    dataframes = []
    archivos_leidos = 0
    archivos_fallidos = 0
    
    logging.info(f"Leyendo archivos nuevos para tabla: {tabla_path}")
    
    for archivo_info in archivos_info:
        try:
            ruta_completa = archivo_info['ruta_completa']
            
            # Verificar si el archivo existe
            if not verificar_archivo_existe(S3_LANDING_BUCKET, ruta_completa):
                logging.warning(f"Archivo no encontrado, omitiendo: {ruta_completa}")
                archivos_fallidos += 1
                continue
            
            # Leer el archivo Parquet
            df = leer_parquet_desde_s3(S3_LANDING_BUCKET, ruta_completa)
            
            if not df.empty:
                # Normalizar tipos de datos del archivo leído
                df = normalizar_tipos_datos(df)
                
                # NO agregar columnas de metadatos - mantener datos originales únicamente
                dataframes.append(df)
                archivos_leidos += 1
                logging.info(f"Archivo leído y normalizado: {ruta_completa} ({len(df)} registros)")
            else:
                logging.warning(f"Archivo vacío, omitiendo: {ruta_completa}")
                archivos_fallidos += 1
                
        except Exception as e:
            logging.warning(f"Error procesando archivo {archivo_info['ruta_completa']}: {str(e)}")
            archivos_fallidos += 1
            continue
    
    # Consolidar todos los DataFrames nuevos
    if dataframes:
        df_consolidado = pd.concat(dataframes, ignore_index=True)
        logging.info(f"Nuevos datos consolidados para {tabla_path}: {len(df_consolidado)} registros")
        logging.info(f"  - Archivos leídos: {archivos_leidos}")
        logging.info(f"  - Archivos fallidos: {archivos_fallidos}")
        return df_consolidado
    else:
        logging.warning(f"No se pudieron leer archivos nuevos para la tabla {tabla_path}")
        return pd.DataFrame()

def procesar_archivos_consolidados_incremental(df: pd.DataFrame) -> None:
    """
    Procesa los archivos del resumen usando lógica incremental por tabla.
    
    Args:
        df (pd.DataFrame): DataFrame con la información de los archivos
    """
    try:
        # Agrupar archivos por tabla
        grupos_tabla = agrupar_archivos_por_tabla(df)
        
        tablas_procesadas = 0
        tablas_fallidas = 0
        total_registros_procesados = 0
        total_registros_nuevos = 0
        total_registros_actualizados = 0
        
        for tabla_path, archivos_info in grupos_tabla.items():
            try:
                logging.info(f"\n=== Procesando tabla con lógica incremental: {tabla_path} ===")
                
                # Verificar si existe consolidado previo
                archivo_consolidado_key = f"{tabla_path}/consolidado.parquet"
                existe_previo = verificar_archivo_existe(S3_RAW_BUCKET, archivo_consolidado_key)
                
                if existe_previo:
                    logging.info(f"Modo: Actualización incremental")
                else:
                    logging.info(f"Modo: Primera consolidación completa")
                
                # Procesar con lógica incremental
                df_consolidado = consolidar_archivos_tabla_incremental(tabla_path, archivos_info)
                
                if not df_consolidado.empty:
                    # Escribir archivo consolidado actualizado
                    escribir_parquet_a_s3(df_consolidado, S3_RAW_BUCKET, archivo_consolidado_key)
                    
                    tablas_procesadas += 1
                    total_registros_procesados += len(df_consolidado)
                    logging.info(f"Tabla procesada exitosamente: {tabla_path}")
                else:
                    logging.warning(f"No se generó archivo consolidado para tabla: {tabla_path}")
                    tablas_fallidas += 1
                    
            except Exception as e:
                logging.error(f"Error procesando tabla {tabla_path}: {str(e)}")
                tablas_fallidas += 1
                continue
        
        # Resumen final
        logging.info(f"\n=== Resumen de consolidación incremental ===")
        logging.info(f"Total tablas: {len(grupos_tabla)}")
        logging.info(f"Tablas procesadas exitosamente: {tablas_procesadas}")
        logging.info(f"Tablas fallidas: {tablas_fallidas}")
        logging.info(f"Total registros en consolidados finales: {total_registros_procesados}")
        
    except Exception as e:
        logging.error(f"Error en el procesamiento de consolidación incremental: {str(e)}")
        raise

def main():
    """
    Función principal del script
    """
    print("🚀 Iniciando aplicación de consolidación...")
    logging.info("🚀 Iniciando aplicación de consolidación...")
    
    try:
        print("📋 Parseando argumentos...")
        # Configurar argumentos de línea de comandos
        parser = argparse.ArgumentParser(description='Consolida archivos Parquet basados en un resumen')
        parser.add_argument('seccion_img', help='Nombre de la sección (ej: IMG_FLOW_04)')
        args = parser.parse_args()
        
        print(f"✅ Argumentos parseados: {args.seccion_img}")
        
        # Mostrar configuración
        print(f"\n=== Iniciando consolidación de archivos ===")
        print(f"Sección: {args.seccion_img}")
        print(f"Bucket origen: {S3_LANDING_BUCKET}")
        print(f"Bucket destino: {S3_RAW_BUCKET}")
        print(f"Modo: Consolidación incremental automática por tabla")
        
        logging.info(f"\n=== Iniciando consolidación de archivos ===")
        logging.info(f"Sección: {args.seccion_img}")
        logging.info(f"Bucket origen: {S3_LANDING_BUCKET}")
        logging.info(f"Bucket destino: {S3_RAW_BUCKET}")
        logging.info(f"Modo: Consolidación incremental automática por tabla")
        
        # Verificar si existe el archivo de resumen antes de continuar
        resumen_key = f"RESUMEN/{args.seccion_img}/RESUMEN_{args.seccion_img}.parquet"
        print(f"🔍 Verificando archivo de resumen: {resumen_key}")
        
        if not verificar_archivo_existe(S3_LANDING_BUCKET, resumen_key):
            print(f"❌ No se encontró el archivo de resumen: {resumen_key}")
            print("❌ No hay archivos para procesar.")
            logging.warning(f"No se encontró el archivo de resumen: {resumen_key}")
            logging.warning("No hay archivos para procesar.")
            return  # Cambiar a return en lugar de sys.exit
            
        print(f"✅ Archivo de resumen encontrado, procediendo a leer...")
        
        # Leer archivo de resumen
        df_resumen = leer_archivo_resumen(args.seccion_img)
        
        print(f"📊 Resumen leído: {len(df_resumen)} registros")
        
        # Verificar si hay datos en el resumen
        if len(df_resumen) == 0:
            print("❌ El archivo de resumen está vacío, no hay archivos para procesar.")
            logging.warning("El archivo de resumen está vacío, no hay archivos para procesar.")
            return  # Cambiar a return en lugar de sys.exit
        
        print(f"🔄 Iniciando procesamiento de archivos consolidados...")
        
        # Procesar archivos con consolidación incremental
        procesar_archivos_consolidados_incremental(df_resumen)
        
        print("✅ Proceso de consolidación incremental completado exitosamente")
        logging.info("Proceso de consolidación incremental completado exitosamente")
        
    except Exception as e:
        print(f"❌ Error en el procesamiento: {str(e)}")
        print(f"❌ Tipo de error: {type(e).__name__}")
        print(f"❌ Traceback completo:")
        import traceback
        traceback.print_exc()
        logging.error(f"Error en el procesamiento: {str(e)}")
        raise  # Re-lanzar para ver el error completo

if __name__ == "__main__":
    main()
