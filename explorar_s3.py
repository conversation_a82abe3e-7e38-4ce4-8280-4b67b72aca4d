#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para explorar la estructura de archivos en S3
"""

import boto3
import logging

# Configuración de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

S3_RAW_BUCKET = "prd-datalake-silver-zone-637423440311"

def explorar_s3(bucket, prefix):
    """
    Explora la estructura de archivos en S3
    """
    s3_client = boto3.client('s3')
    
    print(f"🔍 Explorando: s3://{bucket}/{prefix}")
    print("=" * 80)
    
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        total_archivos = 0
        
        for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
            if 'Contents' in page:
                for obj in page['Contents']:
                    key = obj['Key']
                    size_mb = obj['Size'] / (1024 * 1024)
                    print(f"📁 {key} ({size_mb:.2f} MB)")
                    total_archivos += 1
                    
                    # Limitar output para no saturar
                    if total_archivos > 50:
                        print(f"... (mostrando solo los primeros 50 archivos)")
                        break
                        
        if total_archivos == 0:
            print("❌ No se encontraron archivos")
        else:
            print(f"\n✅ Total de archivos encontrados: {total_archivos}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    # Explorar diferentes posibles rutas
    rutas_a_explorar = [
        "PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/2025/05/29",
        "PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA",
    ]
    
    for ruta in rutas_a_explorar:
        print(f"\n")
        explorar_s3(S3_RAW_BUCKET, ruta)
        print("-" * 80)
