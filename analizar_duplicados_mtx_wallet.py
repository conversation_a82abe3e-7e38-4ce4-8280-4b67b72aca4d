#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para analizar duplicados en MTX_WALLET_ORA y encontrar la mejor clave primaria
"""

import pandas as pd
import sys
import os

# Agregar el directorio actual al path
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def analizar_duplicados_mtx_wallet():
    """
    Analiza los duplicados en MTX_WALLET_ORA y busca la mejor clave primaria
    """
    print("🔍 ANÁLISIS DE DUPLICADOS EN MTX_WALLET_ORA")
    print("=" * 60)
    
    # Ubicación del archivo consolidado
    s3_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado.parquet"
    
    print(f"📁 Leyendo archivo: {s3_path}")
    
    try:
        # Leer archivo completo
        df = pd.read_parquet(s3_path, engine='pyarrow')
        
        print(f"✅ Archivo leído exitosamente:")
        print(f"   📊 Total filas: {len(df):,}")
        print(f"   📋 Total columnas: {len(df.columns)}")
        print()
        
        # 1. ANÁLISIS DE WALLET_NUMBER DUPLICADOS
        print("🔍 ANÁLISIS DE WALLET_NUMBER DUPLICADOS:")
        print("-" * 50)
        
        duplicados_wallet = df[df.duplicated(subset=['WALLET_NUMBER'], keep=False)]
        print(f"📊 Filas con WALLET_NUMBER duplicado: {len(duplicados_wallet):,}")
        
        if len(duplicados_wallet) > 0:
            print(f"📋 Muestra de duplicados:")
            duplicados_muestra = duplicados_wallet[['WALLET_NUMBER', 'USER_ID', 'MSISDN', 'STATUS', 'DATA_LAKE_PARTITION_DATE']].head(10)
            print(duplicados_muestra.to_string(index=False))
            print()
            
            # Agrupar por WALLET_NUMBER para ver las diferencias
            print(f"🔍 Ejemplos de WALLET_NUMBER con múltiples registros:")
            wallet_counts = df['WALLET_NUMBER'].value_counts()
            top_duplicates = wallet_counts[wallet_counts > 1].head(5)
            
            for wallet_num, count in top_duplicates.items():
                print(f"\n📋 WALLET_NUMBER: {wallet_num} ({count} registros)")
                subset = df[df['WALLET_NUMBER'] == wallet_num][['WALLET_NUMBER', 'USER_ID', 'STATUS', 'DATA_LAKE_PARTITION_DATE', 'data_lake_last_modified']].head(5)
                print(subset.to_string(index=False))
        
        print("\n" + "="*60)
        
        # 2. BUSCAR COMBINACIONES QUE PODRÍAN SER CLAVE PRIMARIA
        print("🔍 BÚSQUEDA DE CLAVES PRIMARIAS ALTERNATIVAS:")
        print("-" * 50)
        
        # Candidatos a clave primaria
        candidatos = [
            ['WALLET_NUMBER', 'DATA_LAKE_PARTITION_DATE'],
            ['WALLET_NUMBER', 'USER_ID'],
            ['WALLET_NUMBER', 'MSISDN'],
            ['USER_ID', 'PAYMENT_TYPE_ID'],
            ['WALLET_REF_ID', 'WALLET_NUMBER'],
            ['MPAY_PROFILE_ID'],
            ['RNUM'],  # Row number podría ser único
        ]
        
        claves_validas = []
        
        for candidato in candidatos:
            # Verificar que todas las columnas existan
            if all(col in df.columns for col in candidato):
                try:
                    # Verificar unicidad
                    unique_count = df.drop_duplicates(subset=candidato).shape[0]
                    total_count = len(df)
                    
                    # Verificar nulos
                    null_count = df[candidato].isnull().any(axis=1).sum()
                    
                    es_clave_valida = (unique_count == total_count) and (null_count == 0)
                    
                    print(f"📋 {' + '.join(candidato)}:")
                    print(f"   Únicos: {unique_count:,}/{total_count:,}")
                    print(f"   Nulos: {null_count:,}")
                    print(f"   ✅ Clave válida: {es_clave_valida}")
                    
                    if es_clave_valida:
                        claves_validas.append(candidato)
                    
                    print()
                    
                except Exception as e:
                    print(f"   ❌ Error analizando {candidato}: {str(e)}")
        
        # 3. RECOMENDACIONES
        print("\n" + "="*60)
        print("🎯 RECOMENDACIONES:")
        print("-" * 30)
        
        if claves_validas:
            print("✅ Claves primarias válidas encontradas:")
            for i, clave in enumerate(claves_validas, 1):
                print(f"{i}. {' + '.join(clave)}")
            
            # Recomendar la más simple
            clave_recomendada = min(claves_validas, key=len)
            print(f"\n🎯 RECOMENDACIÓN: {' + '.join(clave_recomendada)}")
            print(f"   (la más simple de las válidas)")
            
            # Generar configuración para .ini
            print(f"\n📝 Para actualizar tabla_primary_keys_simple.ini:")
            print(f"[MTX_WALLET_ORA]")
            print(f"key = {', '.join(clave_recomendada)}")
            
        else:
            print("❌ No se encontraron claves primarias válidas")
            print("💡 Opciones:")
            print("   1. Usar WALLET_NUMBER + otra columna para crear unicidad")
            print("   2. Generar una columna índice sintética")
            print("   3. Aceptar duplicados y usar merge con keep='last'")
        
        # 4. ANÁLISIS DE COLUMNAS INDIVIDUALES PROMETEDORAS
        print(f"\n🔍 ANÁLISIS DETALLADO DE COLUMNAS PROMETEDORAS:")
        print("-" * 50)
        
        columnas_analizar = ['RNUM', 'MPAY_PROFILE_ID', 'USER_ID', 'WALLET_REF_ID']
        
        for col in columnas_analizar:
            if col in df.columns:
                unique_count = df[col].nunique()
                total_count = len(df)
                null_count = df[col].isnull().sum()
                
                print(f"📋 {col}:")
                print(f"   Únicos: {unique_count:,}/{total_count:,}")
                print(f"   Nulos: {null_count:,}")
                print(f"   Tipo: {df[col].dtype}")
                
                # Muestra de valores
                sample = df[col].dropna().head(5).tolist()
                print(f"   Muestra: {sample}")
                print()
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analizar_duplicados_mtx_wallet()
