#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Configuración simple de claves primarias para tablas.
Lee configuración desde tabla_primary_keys_simple.ini
"""

import os
import configparser
import pandas as pd
import logging
from typing import List, Optional

# Ruta al archivo de configuración simple
CONFIG_FILE = os.path.join(os.path.dirname(__file__), 'config', 'tabla_primary_keys_simple.ini')

# Cache de configuración
TABLA_KEYS_CONFIG = {}

def cargar_configuracion_simple():
    """
    Carga la configuración simple desde el archivo .ini
    """
    global TABLA_KEYS_CONFIG
    
    try:
        if not os.path.exists(CONFIG_FILE):
            logging.error(f"❌ Archivo de configuración NO encontrado: {CONFIG_FILE}")
            logging.info(f"💡 Creando directorio config...")
            os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
            return
        
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE, encoding='utf-8')
        
        # Cargar cada sección como una tabla
        TABLA_KEYS_CONFIG = {}
        tablas_cargadas = 0
        
        for seccion in config.sections():
            if 'key' in config[seccion]:
                claves_str = config[seccion]['key']
                # Dividir por comas y limpiar espacios
                claves = [k.strip() for k in claves_str.split(',') if k.strip()]
                
                if claves:  # Solo agregar si hay claves válidas
                    TABLA_KEYS_CONFIG[seccion.upper()] = claves  # Guardar en mayúsculas
                    tablas_cargadas += 1
                    logging.info(f"📋 {seccion} → {claves}")
        
        logging.info(f"✅ Configuración cargada exitosamente:")
        logging.info(f"   📁 Archivo: {CONFIG_FILE}")
        logging.info(f"   📊 Tablas configuradas: {tablas_cargadas}")
        
        if tablas_cargadas == 0:
            logging.warning(f"⚠️ No se encontraron configuraciones válidas en el archivo .ini")
        
    except Exception as e:
        logging.error(f"❌ Error cargando configuración: {str(e)}")
        logging.warning(f"🔄 Continuando con detección automática...")
        TABLA_KEYS_CONFIG = {}

# Cargar configuración al importar
cargar_configuracion_simple()

def detectar_clave_primaria_tabla(tabla_nombre: str, df_sample: pd.DataFrame) -> List[str]:
    """
    Detecta la clave primaria EXACTAMENTE como está configurada en el archivo .ini
    Si no está configurada, usa fallbacks automáticos.
    """
    # Limpiar nombre de tabla (quitar rutas si las hay)
    tabla_nombre_clean = tabla_nombre.split('/')[-1] if '/' in tabla_nombre else tabla_nombre
    tabla_nombre_clean = tabla_nombre_clean.upper()  # Convertir a mayúsculas para comparar
    
    columnas_disponibles = list(df_sample.columns)
    
    logging.info(f"🔍 Detectando clave primaria para tabla: {tabla_nombre_clean}")
    logging.info(f"📋 Columnas disponibles: {columnas_disponibles}")
    
    # 1. PRIORIDAD MÁXIMA: Buscar configuración EXACTA en el archivo .ini
    for tabla_config, claves_config in TABLA_KEYS_CONFIG.items():
        if tabla_config.upper() == tabla_nombre_clean:
            # Verificar que TODAS las columnas configuradas existan
            claves_encontradas = [col for col in claves_config if col in columnas_disponibles]
            
            if len(claves_encontradas) == len(claves_config):
                logging.info(f"✅ CONFIGURACIÓN ENCONTRADA: {tabla_config} → {claves_encontradas}")
                return claves_encontradas
            else:
                claves_faltantes = [col for col in claves_config if col not in columnas_disponibles]
                logging.error(f"❌ CONFIGURACIÓN INCOMPLETA para {tabla_config}")
                logging.error(f"   Configurado: {claves_config}")
                logging.error(f"   Encontrado: {claves_encontradas}")  
                logging.error(f"   Faltante: {claves_faltantes}")
                # Si falta alguna columna configurada, fallar completamente
                return []
    
    # 2. Si NO está configurado: usar fallbacks automáticos
    logging.warning(f"⚠️ Tabla {tabla_nombre_clean} NO configurada en .ini")
    logging.info(f"🔄 Intentando detección automática...")
    
    # Fallback 1: USER_ID
    if 'USER_ID' in columnas_disponibles:
        logging.info(f"✅ Fallback automático: USER_ID")
        return ['USER_ID']
    
    # Fallback 2: Primera columna que termine en _ID
    columnas_id = [col for col in columnas_disponibles if col.endswith('_ID')]
    if columnas_id:
        logging.info(f"✅ Fallback automático: {columnas_id[0]} (primera columna _ID)")
        return [columnas_id[0]]
    
    # Fallback 3: Columna llamada ID
    if 'ID' in columnas_disponibles:
        logging.info(f"✅ Fallback automático: ID")
        return ['ID']
    
    # Sin clave primaria identificable
    logging.warning(f"❌ No se pudo identificar clave primaria para {tabla_nombre_clean}")
    logging.warning(f"💡 Sugerencia: Agregar en .ini → [{tabla_nombre_clean}] key = COLUMNA_ID")
    return []

def validar_clave_primaria(df: pd.DataFrame, claves: List[str], tolerancia_unicidad: float = 0.95) -> bool:
    """
    Valida si las columnas forman una clave primaria válida.
    En tablas de resumen o tablas especiales, permite validación más flexible.
    
    Args:
        df: DataFrame a validar
        claves: Lista de columnas que forman la clave primaria
        tolerancia_unicidad: Porcentaje mínimo de unicidad requerido (default: 95%)
    
    Returns:
        bool: True si la clave es válida o si es una tabla especial
    """
    if not claves or df.empty:
        # Para DataFrames vacíos, consideramos válido para no bloquear el flujo
        logging.info("DataFrame vacío, permitiendo continuar")
        return True
    
    # Casos especiales: tablas de resumen o tablas de log
    es_tabla_resumen = any(col.startswith('RESUMEN_') for col in df.columns)
    es_tabla_log = any(col in ['LOG_ID', 'TIMESTAMP', 'LOG_DATE'] for col in df.columns)
    
    if es_tabla_resumen or es_tabla_log:
        logging.info(f"⚠️ Tabla especial detectada (resumen o log), omitiendo validación estricta de clave primaria")
        return True
    
    try:
        # Verificar que todas las columnas existan
        columnas_faltantes = [col for col in claves if col not in df.columns]
        if columnas_faltantes:
            logging.warning(f"❌ Columnas no encontradas: {columnas_faltantes}")
            return False
        
        # Contar registros con valores nulos en columnas clave
        total_registros = len(df)
        registros_problematicos = 0
        
        for col in claves:
            # Verificar si hay nulos o valores vacíos en columnas clave
            mask_problematicos = (
                df[col].isna() | 
                (df[col].astype(str).str.strip() == '')
            )
            num_problematicos = mask_problematicos.sum()
            
            if num_problematicos > 0:
                porcentaje = (num_problematicos / total_registros) * 100
                logging.warning(f"❌ CLAVE PRIMARIA {col}: {num_problematicos} valores nulos o vacíos ({porcentaje:.1f}%)")
                registros_problematicos += num_problematicos
        
        # Si hay más de 1% de registros con problemas en claves, fallar
        if registros_problematicos > 0:
            porcentaje_problematicos = (registros_problematicos / (total_registros * len(claves))) * 100
            logging.warning(f"❌ VALIDACIÓN FALLIDA: {porcentaje_problematicos:.1f}% de valores problemáticos en claves primarias")
            return False
        
        # Verificar unicidad
        if len(claves) == 1:
            # Clave simple
            valores_unicos = df[claves[0]].nunique()
        else:
            # Clave compuesta
            valores_unicos = df[claves].drop_duplicates().shape[0]
        
        porcentaje_unicidad = valores_unicos / total_registros
        
        logging.info(f"📊 Unicidad: {valores_unicos}/{total_registros} ({porcentaje_unicidad:.1%})")
        
        # Validar unicidad con tolerancia
        if porcentaje_unicidad >= tolerancia_unicidad:
            logging.info(f"✅ CLAVE PRIMARIA VÁLIDA: {claves}")
            return True
        else:
            logging.warning(f"❌ CLAVE PRIMARIA CON BAJA UNICIDAD: {porcentaje_unicidad:.1%} < {tolerancia_unicidad:.1%}")
            return False
        
    except Exception as e:
        logging.warning(f"❌ Error validando clave primaria {claves}: {str(e)}")
        # En caso de error, permitir continuar para no bloquear el flujo
        return True
