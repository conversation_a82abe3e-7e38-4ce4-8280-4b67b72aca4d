#!/usr/bin/env python3
"""
VERIFICACIÓN FINAL - Sistema de Consolidación Puro Incremental
============================================================

Script de verificación final que confirma que el sistema está 100% operativo
y listo para usar en producción.
"""

import sys
import os
import subprocess
import importlib.util
from datetime import datetime

def print_header(titulo):
    """Imprime un encabezado formateado."""
    print("\n" + "="*60)
    print(f"📋 {titulo}")
    print("="*60)

def verificar_compilacion():
    """Verifica que el archivo principal compile sin errores."""
    print_header("VERIFICACIÓN DE COMPILACIÓN")
    
    try:
        result = subprocess.run(
            ['python3', '-m', 'py_compile', 'app_raw_consolidado_puro.py'],
            capture_output=True,
            text=True,
            cwd='.'
        )
        
        if result.returncode == 0:
            print("✅ Compilación exitosa: Sin errores de sintaxis")
            return True
        else:
            print(f"❌ Error de compilación: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error ejecutando compilación: {str(e)}")
        return False

def verificar_funciones():
    """Verifica que todas las funciones estén disponibles."""
    print_header("VERIFICACIÓN DE FUNCIONES")
    
    try:
        # Importar el módulo
        spec = importlib.util.spec_from_file_location(
            "app_raw_consolidado_puro", 
            "app_raw_consolidado_puro.py"
        )
        modulo = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(modulo)
        
        # Lista de funciones críticas
        funciones_criticas = [
            'preservar_tipos_originales',
            'eliminar_duplicados_puro',
            'consolidar_tabla_pura_incremental',
            'verificar_archivo_existe',
            'leer_consolidado_existente_puro',
            'merge_incremental_puro',
            'alinear_tipos_datos_puro',
            'consolidar_archivos_nuevos_puro',
            'generar_clave_registro_pura',
            'main'
        ]
        
        funciones_ok = 0
        funciones_faltantes = []
        
        for func_nombre in funciones_criticas:
            if hasattr(modulo, func_nombre):
                print(f"   ✅ {func_nombre}")
                funciones_ok += 1
            else:
                print(f"   ❌ {func_nombre}")
                funciones_faltantes.append(func_nombre)
        
        print(f"\n📊 Resumen funciones:")
        print(f"   ✅ Disponibles: {funciones_ok}/{len(funciones_criticas)}")
        print(f"   ❌ Faltantes: {len(funciones_faltantes)}")
        
        return len(funciones_faltantes) == 0
        
    except Exception as e:
        print(f"❌ Error importando módulo: {str(e)}")
        return False

def verificar_help():
    """Verifica que el script muestre help correctamente."""
    print_header("VERIFICACIÓN DE INTERFAZ")
    
    try:
        result = subprocess.run(
            ['python3', 'app_raw_consolidado_puro.py', '--help'],
            capture_output=True,
            text=True,
            cwd='.'
        )
        
        if result.returncode == 0 and "Consolidación Pura" in result.stdout:
            print("✅ Interfaz de línea de comandos funcionando")
            print("✅ Mensaje de ayuda correcto")
            return True
        else:
            print(f"❌ Error en interfaz: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error ejecutando help: {str(e)}")
        return False

def verificar_demo():
    """Verifica que la demo funcione correctamente."""
    print_header("VERIFICACIÓN DE DEMO FUNCIONAL")
    
    try:
        result = subprocess.run(
            ['python3', 'demo_consolidacion_incremental.py'],
            capture_output=True,
            text=True,
            cwd='.',
            timeout=30
        )
        
        if result.returncode == 0:
            # Verificar mensajes clave en la salida
            output = result.stdout
            
            # Simplificar verificación - si la demo se ejecuta sin errores, está funcionando
            demo_exitosa = (
                "DEMOSTRACIÓN COMPLETADA" in output and
                "Estructura preservada" in output and 
                "merge incremental" in output.lower()
            )
            
            if demo_exitosa:
                print("✅ Demo funcional completada exitosamente")
                print("✅ Consolidación incremental operativa")
                print("✅ Preservación de estructura confirmada")
                return True
            else:
                print("⚠️ Demo ejecutada pero verificación automática falló")
                print("   (Es posible que la demo funcione correctamente)")
                return False
        else:
            print(f"❌ Error ejecutando demo: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Demo timeout (más de 30 segundos)")
        return False
    except Exception as e:
        print(f"❌ Error ejecutando demo: {str(e)}")
        return False

def verificar_tests():
    """Verifica que los tests automáticos pasen."""
    print_header("VERIFICACIÓN DE TESTS AUTOMATIZADOS")
    
    try:
        result = subprocess.run(
            ['python3', 'test_funciones_puro.py'],
            capture_output=True,
            text=True,
            cwd='.',
            timeout=20
        )
        
        if result.returncode == 0 and "TODOS LOS TESTS PASARON" in result.stdout:
            print("✅ Tests automatizados ejecutados exitosamente")
            print("✅ Todas las funciones están operativas")
            return True
        else:
            print(f"❌ Tests fallaron: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Tests timeout (más de 20 segundos)")
        return False
    except Exception as e:
        print(f"❌ Error ejecutando tests: {str(e)}")
        return False

def generar_reporte_final(resultados):
    """Genera el reporte final de verificación."""
    print_header("REPORTE FINAL DE VERIFICACIÓN")
    
    total_checks = len(resultados)
    passed_checks = sum(1 for r in resultados.values() if r)
    
    print(f"📊 RESUMEN DE VERIFICACIONES:")
    for check_name, resultado in resultados.items():
        status = "✅ PASÓ" if resultado else "❌ FALLÓ"
        print(f"   {status} {check_name}")
    
    print(f"\n📈 ESTADÍSTICAS:")
    print(f"   Total verificaciones: {total_checks}")
    print(f"   Verificaciones exitosas: {passed_checks}")
    print(f"   Porcentaje de éxito: {(passed_checks/total_checks)*100:.1f}%")
    
    if passed_checks == total_checks:
        print(f"\n🎉 SISTEMA 100% OPERATIVO")
        print(f"   ✅ Todas las verificaciones pasaron")
        print(f"   ✅ Sistema listo para producción")
        print(f"   ✅ Fecha de verificación: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return True
    else:
        print(f"\n⚠️ SISTEMA REQUIERE ATENCIÓN")
        print(f"   ❌ {total_checks - passed_checks} verificaciones fallaron")
        print(f"   🔧 Revisar errores arriba antes de usar en producción")
        return False

def main():
    """Función principal de verificación."""
    print("🔍 VERIFICACIÓN FINAL DEL SISTEMA DE CONSOLIDACIÓN PURO")
    print(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Objetivo: Confirmar que el sistema está 100% operativo")
    
    # Ejecutar todas las verificaciones
    resultados = {}
    
    print("\n🚀 Iniciando verificaciones...")
    
    resultados["Compilación"] = verificar_compilacion()
    resultados["Funciones"] = verificar_funciones()
    resultados["Interfaz CLI"] = verificar_help()
    resultados["Tests automatizados"] = verificar_tests()
    resultados["Demo funcional"] = verificar_demo()
    
    # Generar reporte final
    sistema_ok = generar_reporte_final(resultados)
    
    # Exit code apropiado
    return 0 if sistema_ok else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
