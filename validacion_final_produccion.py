#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VALIDACIÓN FINAL - ARCHIVO CONSOLIDADO EN PRODUCCIÓN
==================================================

Valida que el archivo consolidado_puro.parquet en producción
contiene los 80 registros únicos correctos después del reemplazo.

Desarrollado para: Data Engineer
Fecha: 2025-06-02
"""

import pandas as pd
import boto3
import logging
from datetime import datetime

# Configuración de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def validar_archivo_produccion():
    """Valida la integridad del archivo consolidado en producción"""
    
    print("🔍 VALIDACIÓN FINAL - ARCHIVO CONSOLIDADO EN PRODUCCIÓN")
    print("=" * 60)
    
    # Archivo de producción
    archivo_produccion = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    
    # Datos esperados basados en análisis previo
    REGISTROS_UNICOS_ESPERADOS = 80  # Según archivo origen analizado previamente
    
    try:
        # Leer archivo de producción
        print(f"📁 Leyendo archivo de producción...")
        df_produccion = pd.read_parquet(archivo_produccion)
        
        # Análisis de integridad
        print(f"\n📊 ANÁLISIS DE INTEGRIDAD:")
        print(f"   • Archivo producción - Total registros: {len(df_produccion)}")
        print(f"   • Archivo producción - WALLET_NUMBER únicos: {df_produccion['WALLET_NUMBER'].nunique()}")
        print(f"   • Registros únicos esperados: {REGISTROS_UNICOS_ESPERADOS}")
        
        # Verificar que se usa WALLET_NUMBER como clave
        duplicados_wallet = df_produccion[df_produccion.duplicated(subset=['WALLET_NUMBER'], keep=False)]
        print(f"   • Duplicados de WALLET_NUMBER: {len(duplicados_wallet)}")
        
        # Validación crítica basada en datos conocidos
        registros_unicos_actuales = df_produccion['WALLET_NUMBER'].nunique()
        
        if registros_unicos_actuales == REGISTROS_UNICOS_ESPERADOS and len(duplicados_wallet) == 0:
            print(f"\n✅ VALIDACIÓN EXITOSA!")
            print(f"   ✓ Todos los {REGISTROS_UNICOS_ESPERADOS} registros únicos se preservaron")
            print(f"   ✓ No hay pérdida de datos")
            print(f"   ✓ No hay duplicados de WALLET_NUMBER (clave primaria respetada)")
            print(f"   ✓ El sistema está funcionando correctamente")
            
            # Verificar distribución de datos
            print(f"\n📊 DISTRIBUCIÓN DE DATOS:")
            print(f"   • Rango WALLET_NUMBER: {df_produccion['WALLET_NUMBER'].min()} - {df_produccion['WALLET_NUMBER'].max()}")
            
            validacion_exitosa = True
                
        else:
            print(f"\n❌ VALIDACIÓN FALLIDA!")
            if registros_unicos_actuales != REGISTROS_UNICOS_ESPERADOS:
                diferencia = REGISTROS_UNICOS_ESPERADOS - registros_unicos_actuales
                print(f"   ✗ Se esperaban {REGISTROS_UNICOS_ESPERADOS} registros únicos, pero se encontraron {registros_unicos_actuales}")
                print(f"   ✗ Diferencia: {diferencia} registros {'perdidos' if diferencia > 0 else 'extra'}")
            
            if len(duplicados_wallet) > 0:
                print(f"   ✗ Encontrados {len(duplicados_wallet)} duplicados de WALLET_NUMBER")
                print(f"   ✗ Esto indica que no se está usando WALLET_NUMBER como clave primaria")
                
            validacion_exitosa = False
                
        # Información adicional del archivo de producción
        print(f"\n📋 INFORMACIÓN DEL ARCHIVO DE PRODUCCIÓN:")
        print(f"   • Columnas: {list(df_produccion.columns)}")
        if 'DATA_UPDATE' in df_produccion.columns:
            print(f"   • Rango de fechas: {df_produccion['DATA_UPDATE'].min()} a {df_produccion['DATA_UPDATE'].max()}")
        print(f"   • Tamaño en MB: {round(df_produccion.memory_usage(deep=True).sum() / 1024 / 1024, 2)}")
        
        return validacion_exitosa
        
    except Exception as e:
        print(f"❌ Error en validación: {str(e)}")
        logging.error(f"Error en validación: {str(e)}")
        return False

if __name__ == "__main__":
    print(f"🚀 Iniciando validación final del archivo consolidado en producción...")
    print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    exito = validar_archivo_produccion()
    
    if exito:
        print(f"\n🎉 MISIÓN COMPLETADA EXITOSAMENTE!")
        print(f"   El archivo consolidado en producción está CORRECTO")
        print(f"   Todos los registros únicos se han preservado")
        print(f"   El sistema de consolidación funciona perfectamente")
    else:
        print(f"\n🚨 ATENCIÓN: El archivo de producción requiere revisión")
