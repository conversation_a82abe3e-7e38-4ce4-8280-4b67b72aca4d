#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 PRUEBA DEL FIX CRÍTICO: Sistema de Detección de Claves Primarias
===================================================================

Este script prueba que la corrección aplicada al sistema de consolidación
esté funcionando correctamente y que ahora use WALLET_NUMBER como clave primaria
para MTX_WALLET_ORA en lugar del hash completo.

OBJETIVO:
- Verificar que detectar_clave_primaria_tabla() retorne ['WALLET_NUMBER']
- Verificar que generar_clave_registro_pura() use WALLET_NUMBER como clave
- Confirmar que el bug crítico está corregido
"""

import sys
import os
import pandas as pd
import logging

# Configurar el path
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Configurar logging para ver los detalles
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

from tabla_config_simple import detectar_clave_primaria_tabla, cargar_configuracion_simple
from app_raw_consolidado_puro import generar_clave_registro_pura

def probar_fix_critico():
    """
    Prueba el fix crítico aplicado al sistema
    """
    print("🧪 PRUEBA DEL FIX CRÍTICO - SISTEMA DE CLAVES PRIMARIAS")
    print("=" * 70)
    
    # Cargar configuración
    print("📋 Cargando configuración...")
    cargar_configuracion_simple()
    
    # Crear DataFrame de prueba simulando MTX_WALLET_ORA
    print("\n📊 Creando datos de prueba MTX_WALLET_ORA...")
    df_test = pd.DataFrame({
        'WALLET_NUMBER': ['520101IND01', '107IND01', '105IND01', '520102IND01'],
        'USER_ID': ['USER001', 'USER002', 'USER003', 'USER004'],  # Tiene USER_ID (causaba el bug)
        'STATUS': ['ACTIVE', 'ACTIVE', 'INACTIVE', 'ACTIVE'],
        'MSISDN': ['51999000001', '51999000002', '51999000003', '51999000004'],
        'DATA_LAKE_PARTITION_DATE': ['2025-06-01', '2025-06-01', '2025-06-01', '2025-06-02']
    })
    
    print(f"✅ Datos creados: {len(df_test)} registros")
    print("📋 Muestra:")
    print(df_test[['WALLET_NUMBER', 'USER_ID', 'STATUS']].to_string(index=False))
    
    # PRUEBA 1: Detectar clave primaria
    print(f"\n🔍 PRUEBA 1: Detección de clave primaria")
    print("-" * 50)
    
    claves_detectadas = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_test)
    print(f"📍 Claves detectadas: {claves_detectadas}")
    
    if claves_detectadas == ['WALLET_NUMBER']:
        print("✅ FIX FUNCIONANDO: Detectó WALLET_NUMBER correctamente")
    elif claves_detectadas == ['USER_ID']:
        print("❌ BUG PERSISTE: Aún está usando USER_ID (fallback incorrecto)")
    else:
        print(f"⚠️ RESULTADO INESPERADO: {claves_detectadas}")
    
    # PRUEBA 2: Generar clave de registro
    print(f"\n🔑 PRUEBA 2: Generación de clave de registro")
    print("-" * 50)
    
    # Probar con el primer registro
    primer_registro = df_test.iloc[0]
    print(f"📋 Registro de prueba:")
    print(f"   WALLET_NUMBER: {primer_registro['WALLET_NUMBER']}")
    print(f"   USER_ID: {primer_registro['USER_ID']}")
    
    # Generar clave usando la función corregida
    clave_generada = generar_clave_registro_pura(primer_registro, "MTX_WALLET_ORA")
    print(f"🔑 Clave generada: '{clave_generada}'")
    
    # Verificar resultado
    if clave_generada == primer_registro['WALLET_NUMBER']:
        print("✅ FIX FUNCIONANDO: Usa WALLET_NUMBER como clave")
    elif clave_generada == primer_registro['USER_ID']:
        print("❌ BUG PERSISTE: Aún usa USER_ID como clave")
    elif len(clave_generada) == 32:  # MD5 hash
        print("❌ BUG PERSISTE: Aún usa hash completo")
    else:
        print(f"⚠️ RESULTADO INESPERADO: '{clave_generada}'")
    
    # PRUEBA 3: Verificar que el comportamiento es consistente
    print(f"\n🔄 PRUEBA 3: Consistencia en múltiples registros")
    print("-" * 50)
    
    claves_generadas = []
    for i, row in df_test.iterrows():
        clave = generar_clave_registro_pura(row, "MTX_WALLET_ORA")
        claves_generadas.append(clave)
        print(f"   Registro {i+1}: WALLET_NUMBER={row['WALLET_NUMBER']} → Clave='{clave}'")
    
    # Verificar que todas las claves corresponden a WALLET_NUMBER
    todas_correctas = all(
        clave == df_test.iloc[i]['WALLET_NUMBER'] 
        for i, clave in enumerate(claves_generadas)
    )
    
    if todas_correctas:
        print("✅ CONSISTENCIA: Todas las claves usan WALLET_NUMBER")
    else:
        print("❌ INCONSISTENCIA: Algunas claves no usan WALLET_NUMBER")
    
    # RESUMEN FINAL
    print(f"\n📋 RESUMEN DEL FIX")
    print("=" * 30)
    
    fix_exitoso = (
        claves_detectadas == ['WALLET_NUMBER'] and
        clave_generada == primer_registro['WALLET_NUMBER'] and
        todas_correctas
    )
    
    if fix_exitoso:
        print("🎯 ✅ FIX CRÍTICO EXITOSO")
        print("   - Detección correcta: WALLET_NUMBER")
        print("   - Generación correcta: usa WALLET_NUMBER")
        print("   - Comportamiento consistente")
        print()
        print("🚀 PRÓXIMO PASO: Ejecutar consolidación real para recuperar los 39 registros")
    else:
        print("❌ FIX FALLÓ - REQUIERE CORRECCIÓN ADICIONAL")
        print(f"   - Detección: {claves_detectadas}")
        print(f"   - Generación: '{clave_generada}'")
        print(f"   - Consistencia: {todas_correctas}")

if __name__ == "__main__":
    probar_fix_critico()
