#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug simple para análisis de duplicados
"""

import pandas as pd
import boto3
from io import BytesIO
from datetime import datetime

print("🔍 DEBUG - ANÁLISIS DE DUPLICADOS MTX_WALLET_ORA")
print("=" * 60)
print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Configuración
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
ARCHIVO_ORIGEN = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-080250_chunk_0.parquet"
CLAVE_PRIMARIA = "WALLET_NUMBER"

print(f"\n📍 Configuración:")
print(f"   Bucket: {S3_BRONZE_BUCKET}")
print(f"   Archivo: {ARCHIVO_ORIGEN}")
print(f"   Clave primaria: {CLAVE_PRIMARIA}")

try:
    print(f"\n📥 Conectando a S3...")
    s3_client = boto3.client('s3')
    print(f"   ✅ Cliente S3 creado")
    
    print(f"\n📥 Leyendo archivo...")
    response = s3_client.get_object(Bucket=S3_BRONZE_BUCKET, Key=ARCHIVO_ORIGEN)
    print(f"   ✅ Archivo obtenido de S3")
    
    parquet_content = response['Body'].read()
    print(f"   ✅ Contenido leído ({len(parquet_content)} bytes)")
    
    df = pd.read_parquet(BytesIO(parquet_content))
    print(f"   ✅ DataFrame creado")
    
    print(f"\n📊 INFORMACIÓN BÁSICA:")
    print(f"   Total registros: {len(df)}")
    print(f"   Total columnas: {len(df.columns)}")
    print(f"   Columnas: {list(df.columns)[:10]}...")  # Primeras 10
    
    if CLAVE_PRIMARIA in df.columns:
        total_registros = len(df)
        valores_unicos = df[CLAVE_PRIMARIA].nunique()
        duplicados = total_registros - valores_unicos
        
        print(f"\n📊 ANÁLISIS DE CLAVE PRIMARIA '{CLAVE_PRIMARIA}':")
        print(f"   Total registros: {total_registros}")
        print(f"   Valores únicos: {valores_unicos}")
        print(f"   Registros duplicados: {duplicados}")
        print(f"   Porcentaje duplicación: {(duplicados/total_registros)*100:.1f}%")
        
        if duplicados > 0:
            print(f"\n🔍 HAY DUPLICADOS CONFIRMADOS")
            conteos = df[CLAVE_PRIMARIA].value_counts()
            duplicados_valores = conteos[conteos > 1]
            print(f"   Valores con duplicados: {len(duplicados_valores)}")
            print(f"   Máximo duplicados: {duplicados_valores.max()}")
            
            print(f"\n📋 TOP 5 VALORES MÁS DUPLICADOS:")
            for wallet_num, cantidad in duplicados_valores.head(5).items():
                print(f"       {wallet_num}: {cantidad} registros")
        else:
            print(f"\n✅ NO HAY DUPLICADOS")
    else:
        print(f"\n❌ Columna '{CLAVE_PRIMARIA}' no encontrada")
        print(f"   Columnas disponibles: {list(df.columns)}")

except Exception as e:
    print(f"\n❌ ERROR: {str(e)}")
    import traceback
    traceback.print_exc()

print(f"\n✅ DEBUG COMPLETADO")
print(f"⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
