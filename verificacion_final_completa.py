#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Verificación final completa: Valores categóricos + manejo de NULL
"""

import pandas as pd
import numpy as np
import sys

# Configurar ruta para imports
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

from app_raw_consolidado import normalizar_tipos_datos

def verificacion_final_completa():
    """
    Verificación final que incluye tanto valores categóricos como manejo de NULL
    """
    
    print("🎯 VERIFICACIÓN FINAL COMPLETA - TODOS LOS PROBLEMAS")
    print("="*70)
    
    # Test Integral: Combinar todos los problemas identificados
    print("\n📊 TEST INTEGRAL: Caso completo como en la imagen")
    
    # Simular exactamente lo que se ve en la imagen de comparación
    datos_imagen_simulada = {
        'MPAY_PROFILE_ID': [None, None, None, None],
        'USER_GRADE': ['OPT', 'OPT', 'OPT', 'OPT'],  # Categóricos que deben preservarse
        'DESCRIPTION': ['Expense Account', 'Expense Account', None, None],  # Texto + NULL
        'WALLET_REF_ID': [None, None, None, '520'],  # Mayoría NULL + algunos válidos
        'ACCOUNT_GROUP_ID': [None, None, None, None],  # Todo NULL
        'ISSUER_ID': [None, None, None, None],
        'PAYMENT_METHOD_TYPE_ID': ['WALLET', 'WALLET', 'WALLET', 'WALLET'],  # CRÍTICO: debe preservarse
        'USER_TYPE': ['OPERATOR', 'USER', 'OPERATOR', 'ADMIN'],  # CRÍTICO: debe preservarse
        'WALLET_NUMBER': ['102IND03B', '103USA04C', '104MEX05D', '105EUR06E']  # IDs alfanuméricos
    }
    
    df_simulado = pd.DataFrame(datos_imagen_simulada)
    
    print("   📥 DATOS DE ENTRADA (simulando archivo Bronze):")
    for col in df_simulado.columns:
        valores = df_simulado[col].tolist()
        null_count = df_simulado[col].isnull().sum()
        print(f"      {col}: {valores}")
        if null_count > 0:
            print(f"          └─ ({null_count} valores NULL reales)")
    
    # Aplicar normalización con todas las mejoras
    df_consolidado = normalizar_tipos_datos(df_simulado.copy())
    
    print(f"\n   📤 DATOS CONSOLIDADOS (resultado Silver):")
    problemas_categoricos = []
    problemas_null = []
    
    for col in df_consolidado.columns:
        valores = df_consolidado[col].tolist()
        print(f"      {col}: {valores}")
        
        # Verificar problemas categóricos
        if col in ['USER_GRADE', 'PAYMENT_METHOD_TYPE_ID', 'USER_TYPE']:
            original_vals = df_simulado[col].dropna().tolist()
            consolidado_vals = [v for v in valores if v != '']
            
            if original_vals != consolidado_vals:
                problemas_categoricos.append(f"{col}: valores categóricos alterados")
        
        # Verificar problemas de NULL
        none_literales = sum(1 for v in valores if v == 'None')
        if none_literales > 0:
            problemas_null.append(f"{col}: {none_literales} valores 'None' literales")
    
    # Análisis detallado por tipo de problema
    print(f"\n🔍 ANÁLISIS DETALLADO:")
    
    print(f"\n   1️⃣ PRESERVACIÓN DE VALORES CATEGÓRICOS:")
    campos_criticos = ['USER_GRADE', 'PAYMENT_METHOD_TYPE_ID', 'USER_TYPE', 'WALLET_NUMBER']
    
    for campo in campos_criticos:
        if campo in df_consolidado.columns:
            original = [v for v in df_simulado[campo].tolist() if pd.notna(v)]
            consolidado = [v for v in df_consolidado[campo].tolist() if v != '']
            
            if original == consolidado:
                print(f"      ✅ {campo}: PRESERVADO CORRECTAMENTE")
                if len(set(consolidado)) > 0:
                    print(f"         Valores únicos: {set(consolidado)}")
            else:
                print(f"      ❌ {campo}: ALTERADO INCORRECTAMENTE")
                print(f"         Original: {set(original)}")
                print(f"         Consolidado: {set(consolidado)}")
    
    print(f"\n   2️⃣ MANEJO DE VALORES NULL:")
    campos_con_null = ['MPAY_PROFILE_ID', 'DESCRIPTION', 'WALLET_REF_ID', 'ACCOUNT_GROUP_ID', 'ISSUER_ID']
    
    for campo in campos_con_null:
        if campo in df_consolidado.columns:
            nulls_originales = df_simulado[campo].isnull().sum()
            vacios_consolidado = (df_consolidado[campo] == '').sum()
            nones_literales = (df_consolidado[campo] == 'None').sum()
            
            if nulls_originales == vacios_consolidado and nones_literales == 0:
                print(f"      ✅ {campo}: NULL → cadena vacía CORRECTO")
            elif nones_literales > 0:
                print(f"      ❌ {campo}: NULL → 'None' literal INCORRECTO ({nones_literales} casos)")
            else:
                print(f"      ⚠️  {campo}: Conversión de NULL inconsistente")
    
    # Evaluación final
    print(f"\n" + "="*70)
    print(f"📋 EVALUACIÓN FINAL COMPLETA")
    print(f"="*70)
    
    total_problemas = len(problemas_categoricos) + len(problemas_null)
    
    if total_problemas == 0:
        print(f"🎉 ¡ÉXITO TOTAL!")
        print(f"   ✅ Valores categóricos preservados (WALLET, OPT, OPERATOR)")
        print(f"   ✅ IDs alfanuméricos intactos (102IND03B, etc.)")
        print(f"   ✅ NULLs convertidos a cadenas vacías (no 'None')")
        print(f"   ✅ No hay corrupción de datos")
        
        print(f"\n🚀 SISTEMA LISTO PARA PRODUCCIÓN")
        print(f"   El archivo consolidado ahora debe coincidir con el origen")
        print(f"   sin los problemas de 'None' literales ni pérdida de categorías")
        
        print(f"\n📋 PRÓXIMOS PASOS:")
        print(f"   1. Verificar archivo consolidado final en S3")
        print(f"   2. Comparar nuevamente origen vs consolidado")
        print(f"   3. Monitorear futuras consolidaciones")
        
    else:
        print(f"⚠️  AÚN HAY {total_problemas} PROBLEMA(S):")
        
        if problemas_categoricos:
            print(f"   🔴 Problemas categóricos:")
            for p in problemas_categoricos:
                print(f"      • {p}")
        
        if problemas_null:
            print(f"   🔴 Problemas de NULL:")
            for p in problemas_null:
                print(f"      • {p}")
        
        print(f"\n   Revisar lógica de normalización")
    
    print(f"\n✅ Verificación final completa")

if __name__ == "__main__":
    verificacion_final_completa()
