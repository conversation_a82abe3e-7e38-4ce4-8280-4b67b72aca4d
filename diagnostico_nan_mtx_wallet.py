#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 DIAGNÓSTICO ESPECÍFICO: Valores NaN en MTX_WALLET_ORA
=====================================================

Data Engineer: Análisis específico del problema reportado
Problema: El destino consolidado tiene valores 'nan' que no existen en origen

Análisis:
1. Comparar origen vs destino columna por columna
2. Identificar dónde se introducen los NaN
3. Determinar la causa raíz
4. Proponer solución específica

Fecha: 2025-06-02
"""

import boto3
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from io import BytesIO
import warnings

# Suprimir warnings
warnings.filterwarnings('ignore')

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Configuración S3
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"

def leer_parquet_desde_s3(bucket: str, key: str, tipo: str) -> pd.DataFrame:
    """Lee un archivo Parquet desde S3."""
    try:
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df = pd.read_parquet(buffer)
        logging.info(f"✅ {tipo} leído: {len(df)} registros, {len(df.columns)} columnas")
        return df
    except Exception as e:
        logging.error(f"❌ Error leyendo {tipo} desde {bucket}/{key}: {str(e)}")
        return pd.DataFrame()

def analizar_diferencias_nan(df_origen: pd.DataFrame, df_destino: pd.DataFrame) -> None:
    """
    Analiza específicamente las diferencias de valores NaN entre origen y destino.
    """
    print("\n" + "="*80)
    print("🔍 DIAGNÓSTICO ESPECÍFICO: VALORES NaN")
    print("="*80)
    print(f"⏰ Análisis iniciado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n📊 RESUMEN GENERAL:")
    print(f"   📁 Origen - Registros: {len(df_origen)}, Columnas: {len(df_origen.columns)}")
    print(f"   📁 Destino - Registros: {len(df_destino)}, Columnas: {len(df_destino.columns)}")
    
    # Analizar SOLO las columnas comunes (originales)
    columnas_comunes = set(df_origen.columns).intersection(set(df_destino.columns))
    columnas_metadatos = set(df_destino.columns) - set(df_origen.columns)
    
    print(f"\n📋 ESTRUCTURA:")
    print(f"   📊 Columnas comunes: {len(columnas_comunes)}")
    print(f"   📊 Columnas metadatos agregadas: {len(columnas_metadatos)}")
    if columnas_metadatos:
        print(f"   📝 Metadatos: {', '.join(sorted(columnas_metadatos))}")
    
    # ANÁLISIS CRÍTICO: Comparar NaN por columna
    print(f"\n🚨 ANÁLISIS CRÍTICO: COMPARACIÓN DE VALORES NaN")
    print("="*60)
    
    problemas_encontrados = []
    columnas_ok = []
    
    for col in sorted(columnas_comunes):
        # Contar NaN en origen
        nan_origen = df_origen[col].isnull().sum()
        pct_origen = (nan_origen / len(df_origen)) * 100
        
        # Contar NaN en destino
        nan_destino = df_destino[col].isnull().sum()
        pct_destino = (nan_destino / len(df_destino)) * 100
        
        # Calcular diferencia
        diferencia = nan_destino - nan_origen
        
        if diferencia > 0:
            # PROBLEMA: Se agregaron NaN
            problemas_encontrados.append({
                'columna': col,
                'nan_origen': nan_origen,
                'nan_destino': nan_destino,
                'diferencia': diferencia,
                'pct_origen': pct_origen,
                'pct_destino': pct_destino,
                'tipo_origen': str(df_origen[col].dtype),
                'tipo_destino': str(df_destino[col].dtype)
            })
            
            print(f"❌ {col}:")
            print(f"   📊 Origen: {nan_origen} NaN ({pct_origen:.1f}%) | Tipo: {df_origen[col].dtype}")
            print(f"   📊 Destino: {nan_destino} NaN ({pct_destino:.1f}%) | Tipo: {df_destino[col].dtype}")
            print(f"   🚨 AGREGADOS: {diferencia} NaN durante consolidación")
            print()
        elif diferencia < 0:
            # Se eliminaron NaN (raro pero posible)
            print(f"⚠️ {col}: Se eliminaron {abs(diferencia)} NaN durante consolidación")
        else:
            # Sin cambios
            columnas_ok.append(col)
    
    # RESUMEN DE PROBLEMAS
    print(f"\n📋 RESUMEN DE DIAGNÓSTICO:")
    print(f"   ✅ Columnas sin problemas: {len(columnas_ok)}")
    print(f"   ❌ Columnas con NaN agregados: {len(problemas_encontrados)}")
    
    if problemas_encontrados:
        print(f"\n🚨 PROBLEMAS CRÍTICOS IDENTIFICADOS:")
        for i, problema in enumerate(problemas_encontrados, 1):
            print(f"   {i}. {problema['columna']}: +{problema['diferencia']} NaN "
                  f"({problema['pct_origen']:.1f}% → {problema['pct_destino']:.1f}%)")
        
        # ANÁLISIS DE POSIBLES CAUSAS
        print(f"\n🔍 ANÁLISIS DE POSIBLES CAUSAS:")
        
        # Causa 1: Conversión de tipos
        cambios_tipo = [p for p in problemas_encontrados if p['tipo_origen'] != p['tipo_destino']]
        if cambios_tipo:
            print(f"   🔧 Cambios de tipo detectados: {len(cambios_tipo)} columnas")
            for p in cambios_tipo:
                print(f"      {p['columna']}: {p['tipo_origen']} → {p['tipo_destino']}")
        
        # Causa 2: Columnas numéricas (posibles infinitos)
        cols_numericas = [p for p in problemas_encontrados if 'float' in p['tipo_origen'] or 'int' in p['tipo_origen']]
        if cols_numericas:
            print(f"   📊 Columnas numéricas afectadas: {len(cols_numericas)}")
            for p in cols_numericas:
                print(f"      {p['columna']}: Posible conversión infinitos → NaN")
        
        # Causa 3: Columnas de fecha
        cols_fecha = [p for p in problemas_encontrados if 'datetime' in p['tipo_origen'] or 'date' in p['columna'].lower()]
        if cols_fecha:
            print(f"   📅 Columnas de fecha afectadas: {len(cols_fecha)}")
            for p in cols_fecha:
                print(f"      {p['columna']}: Posible conversión fecha inválida → NaT")
        
        print(f"\n💡 RECOMENDACIONES:")
        print(f"   1. Revisar función preservar_tipos_originales() - manejo de infinitos")
        print(f"   2. Revisar conversión de DATA_LAKE_PARTITION_DATE - errors='coerce'")
        print(f"   3. Verificar que no se estén haciendo JOIN/MERGE que introduzcan NaN")
        print(f"   4. Validar que todos los archivos de origen tengan la misma estructura")
        
    else:
        print(f"   ✅ EXCELENTE: No se detectaron problemas de NaN agregados")
    
    print(f"\n✅ DIAGNÓSTICO COMPLETADO")
    print(f"⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """Función principal de diagnóstico."""
    print("🎯 DIAGNÓSTICO ESPECÍFICO: Problema NaN en MTX_WALLET_ORA")
    print("="*70)
    
    # Archivos específicos a analizar
    archivo_origen = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250530-164129_chunk_0.parquet"
    archivo_destino = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado.parquet"
    
    print(f"📁 Archivo origen: s3://{S3_BRONZE_BUCKET}/{archivo_origen}")
    print(f"📁 Archivo destino: s3://{S3_SILVER_BUCKET}/{archivo_destino}")
    
    # Leer archivos
    df_origen = leer_parquet_desde_s3(S3_BRONZE_BUCKET, archivo_origen, "ORIGEN")
    df_destino = leer_parquet_desde_s3(S3_SILVER_BUCKET, archivo_destino, "DESTINO")
    
    if df_origen.empty or df_destino.empty:
        print("❌ No se pudieron leer ambos archivos. Verificar conectividad S3.")
        return
    
    # Ejecutar análisis
    analizar_diferencias_nan(df_origen, df_destino)

if __name__ == "__main__":
    main()
