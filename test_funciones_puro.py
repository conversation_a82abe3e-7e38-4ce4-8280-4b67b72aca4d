#!/usr/bin/env python3
"""
Test de funciones del sistema de consolidación puro.
Verifica que todas las funciones estén correctamente definidas y sin errores.
"""

import sys
import importlib.util
import pandas as pd
import numpy as np
from datetime import datetime
import traceback

def test_importacion_completa():
    """Testa que el módulo se importe correctamente."""
    try:
        # Importar el módulo principal
        spec = importlib.util.spec_from_file_location(
            "app_raw_consolidado_puro", 
            "app_raw_consolidado_puro.py"
        )
        modulo = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(modulo)
        
        print("✅ Módulo importado exitosamente")
        return modulo
    except Exception as e:
        print(f"❌ Error importando módulo: {str(e)}")
        traceback.print_exc()
        return None

def test_funciones_disponibles(modulo):
    """Verifica que las funciones críticas estén disponibles."""
    funciones_criticas = [
        'preservar_tipos_originales',
        'eliminar_duplicados_puro',
        'consolidar_tabla_pura_incremental',
        'verificar_archivo_existe',
        'leer_consolidado_existente_puro',
        'merge_incremental_puro',
        'alinear_tipos_datos_puro',
        'consolidar_archivos_nuevos_puro',
        'generar_clave_registro_pura'
    ]
    
    funciones_faltantes = []
    funciones_disponibles = []
    
    for func_nombre in funciones_criticas:
        if hasattr(modulo, func_nombre):
            funciones_disponibles.append(func_nombre)
            print(f"✅ Función disponible: {func_nombre}")
        else:
            funciones_faltantes.append(func_nombre)
            print(f"❌ Función faltante: {func_nombre}")
    
    print(f"\n📊 Resumen:")
    print(f"   ✅ Funciones disponibles: {len(funciones_disponibles)}")
    print(f"   ❌ Funciones faltantes: {len(funciones_faltantes)}")
    
    return len(funciones_faltantes) == 0

def test_funcion_preservar_tipos():
    """Test básico de la función preservar_tipos_originales."""
    try:
        # Crear DataFrame de prueba
        data = {
            'id': [1, 2, 3],
            'nombre': ['A', 'B', 'C'],
            'fecha': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
            'valor': [10.5, 20.7, 30.1]
        }
        df = pd.DataFrame(data)
        
        # Importar función directamente desde archivo
        spec = importlib.util.spec_from_file_location(
            "app_raw_consolidado_puro", 
            "app_raw_consolidado_puro.py"
        )
        modulo = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(modulo)
        
        # Probar función
        df_preservado = modulo.preservar_tipos_originales(df)
        
        # Verificar que no se agregaron columnas
        if len(df_preservado.columns) == len(df.columns):
            print("✅ Test preservar_tipos_originales: ÉXITO (no se agregaron columnas)")
            return True
        else:
            print(f"❌ Test preservar_tipos_originales: FALLÓ (columnas originales: {len(df.columns)}, preservadas: {len(df_preservado.columns)})")
            return False
            
    except Exception as e:
        print(f"❌ Test preservar_tipos_originales: ERROR - {str(e)}")
        return False

def main():
    """Función principal de test."""
    print("🧪 === TESTS DEL SISTEMA DE CONSOLIDACIÓN PURO ===\n")
    
    # Test 1: Importación
    print("📋 Test 1: Importación del módulo")
    modulo = test_importacion_completa()
    if not modulo:
        print("❌ FALLO CRÍTICO: No se pudo importar el módulo")
        return
    
    print("\n" + "="*50 + "\n")
    
    # Test 2: Funciones disponibles
    print("📋 Test 2: Verificación de funciones disponibles")
    funciones_ok = test_funciones_disponibles(modulo)
    
    print("\n" + "="*50 + "\n")
    
    # Test 3: Función preservar tipos
    print("📋 Test 3: Test básico de preservar_tipos_originales")
    tipos_ok = test_funcion_preservar_tipos()
    
    print("\n" + "="*50 + "\n")
    
    # Resumen final
    print("🏁 === RESUMEN DE TESTS ===")
    print(f"   📦 Importación: {'✅ ÉXITO' if modulo else '❌ FALLO'}")
    print(f"   🔧 Funciones: {'✅ ÉXITO' if funciones_ok else '❌ FALLO'}")
    print(f"   ⚙️  Preservar tipos: {'✅ ÉXITO' if tipos_ok else '❌ FALLO'}")
    
    if modulo and funciones_ok and tipos_ok:
        print("\n🎉 TODOS LOS TESTS PASARON - El sistema está listo para usar")
        return True
    else:
        print("\n⚠️  ALGUNOS TESTS FALLARON - Revisar errores arriba")
        return False

if __name__ == "__main__":
    main()
