# Configuración de claves primarias por tabla
# Formato simple: [NOMBRE_TABLA] seguido de key = COLUMNA1,COLUMNA2
# Para clave única: key = ID
# Para clave compuesta: key = ID1,ID2,ID3

[USER_PROFILE_ORA]
key = USER_ID

[KYC_DETAILS_ORA]
key = KYC_ID

[USER_AUTH_CHANGE_HISTORY_ORA]
key = AUTHENTICATION_ID, AUTHENTICATION_TYPE, AUTHENTICATION_VALUE

[USER_MODIFICATION_HISTORY_ORA]
key = USER_HIST_ID

[MTX_CATEGORIES_ORA]
key = CATEGORY_CODE

[CHANNEL_GRADES_ORA]
key = GRADE_ID

[ISSUER_DETAILS_ORA]
key = ISSUER_ID

[USER_ACCOUNTS_ORA]
key = USER_ACCOUNTS_ID

[USER_IDENTIFIER_ORA]
key = IDENTIFIER_ID

[MTX_WALLET_ORA]
key = WALLET_NUMBER

[SYS_PAYMENT_METHOD_SUBTYPES_ORA]
key = PAYMENT_METHOD_SUBTYPE_ID

[CATEGORY_GRADE_MAPPING_ORA]
key = CATEGORY_NAME, GRADE_CODE

[SYS_SERVICE_PROVIDER_ORA]
key = PROV_SHORT_NAME_ID

[SYS_STATUS_ITEM_ORA]
key = STATUS_ID

[MTX_PARTY_BLACK_LIST_ORA]
key = ACCESS_TYPE, ACCOUNT_NUMBER

[MARKETING_PROFILE_ORA]
key = MARKETING_PROFILE_CODE

[MARKETING_PROFILE_DETAILS_ORA]
key = MARKETING_PROFILE_DETAIL_ID

[ACCOUNT_GROUP_ORA]
key = ACCOUNT_GROUP_ID

[USER_MARKETING_PROFILE_MAPPING_ORA]
key = MAPPING_ID

[MTX_WALLET_BALANCES_ORIG_ORA]
key = WALLET_NUMBER, WALLET_SEQUENCE_NUMBER

[MTX_WALLET_BALANCES_ORA]
key = WALLET_NUMBER, WALLET_SEQUENCE_NUMBER

[USER_AUTHENTICATION_ORA]
key = AUTHENTICATION_ID

[AUTHENTICATION_IDENTIFIER_ORA]
key = IDENTIFIER_ID, AUTHENTICATION_ID

[USER_ACCOUNT_HISTORY]
key = ID

[SYS_SERVICE_TYPES_ORA]
key = SERVICE_TYPE
# Agregar más tablas según necesites:
# [NOMBRE_DE_TU_TABLA]
# key = TU_COLUMNA_ID
