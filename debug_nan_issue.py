#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

print("🔍 DEBUGGANDO PROBLEMA NaN → 'nan' string")
print("=" * 50)

# Crear DataFrame de prueba
df = pd.DataFrame({'ISSUER_ID': [100.0, np.nan, 200.0]})
print(f"Original: {df['ISSUER_ID'].tolist()} (tipo: {df['ISSUER_ID'].dtype})")

# Guardar máscara ANTES de conversión
mask_nan = df['ISSUER_ID'].isna()
print(f"Máscara NaN: {mask_nan.tolist()}")

# Convertir a string (PROBLEMA)
df['ISSUER_ID'] = df['ISSUER_ID'].astype(str)
print(f"Después astype(str): {df['ISSUER_ID'].tolist()} (tipo: {df['ISSUER_ID'].dtype})")

# INTENTAR restaurar NaN
df.loc[mask_nan, 'ISSUER_ID'] = np.nan
print(f"Después restaurar NaN: {df['ISSUER_ID'].tolist()}")

# Verificar si hay strings 'nan'
strings_nan = (df['ISSUER_ID'].astype(str).str.lower() == 'nan').sum()
print(f"Strings 'nan' detectados: {strings_nan}")

# Verificar NaN reales
nan_reales = df['ISSUER_ID'].isna().sum() 
print(f"NaN reales: {nan_reales}")

print("\n🧪 ANÁLISIS DETALLADO:")
for i, val in enumerate(df['ISSUER_ID']):
    is_na = pd.isna(val)
    str_val = str(val)
    print(f"  Índice {i}: valor={val}, pd.isna()={is_na}, str()='{str_val}'")

print("\n💡 PROBLEMA IDENTIFICADO:")
print("Cuando un column object tiene np.nan, str(np.nan) devuelve 'nan'")
print("Necesitamos usar pd.NA en lugar de np.nan para columnas object")
