#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANÁLISIS DE ESTRUCTURA ORIGEN VS DESTINO - MTX_WALLET_ORA
=======================================================

Como ingeniero de datos, este script analiza:
1. La estructura de datos del origen vs destino
2. Los campos con valores nulos/nan
3. Si existe transformación/degradación en el proceso ETL

Autor: Ingeniero de Datos
Fecha: 2025-06-02
"""

import pandas as pd
import numpy as np
import boto3
import logging
from datetime import datetime
import sys

# Configuración de logs
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')

def analizar_estructura_origen_vs_destino():
    """
    Función principal que analiza las estructuras y compara origen vs destino
    """
    print("\n🔍 ANÁLISIS DE ESTRUCTURA ORIGEN VS DESTINO - MTX_WALLET_ORA")
    print("=" * 70)
    print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Rutas S3 
    ruta_origen = "s3://prd-datalake-bronze-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040917_chunk_0.parquet"
    ruta_destino = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    
    try:
        # 1. Cargar datos
        print("\n📥 Cargando datos...")
        df_origen = pd.read_parquet(ruta_origen)
        df_destino = pd.read_parquet(ruta_destino)
        
        print(f"✓ Origen cargado: {len(df_origen)} registros")
        print(f"✓ Destino cargado: {len(df_destino)} registros")
        
        # 2. Analizar estructuras
        print("\n📊 COMPARACIÓN DE ESTRUCTURAS")
        print("-" * 50)
        
        # Columnas
        columnas_origen = set(df_origen.columns)
        columnas_destino = set(df_destino.columns)
        
        print(f"Columnas en origen: {len(columnas_origen)}")
        print(f"Columnas en destino: {len(columnas_destino)}")
        
        # Diferencias de columnas
        columnas_solo_origen = columnas_origen - columnas_destino
        columnas_solo_destino = columnas_destino - columnas_origen
        
        if columnas_solo_origen:
            print(f"\n⚠️ Columnas SOLO EN ORIGEN ({len(columnas_solo_origen)}):")
            for col in sorted(columnas_solo_origen):
                print(f"   - {col}")
                
        if columnas_solo_destino:
            print(f"\n⚠️ Columnas SOLO EN DESTINO ({len(columnas_solo_destino)}):")
            for col in sorted(columnas_solo_destino):
                print(f"   - {col}")
        
        # 3. Análisis de nulos por columna
        print("\n📊 ANÁLISIS DE VALORES NULOS")
        print("-" * 50)
        
        # Columnas comunes para comparación
        columnas_comunes = columnas_origen.intersection(columnas_destino)
        print(f"Analizando {len(columnas_comunes)} columnas comunes...")
        
        # Tablas para el análisis
        tabla_nulos = {'Columna': [], 'Nulos Origen (%)': [], 'Nulos Destino (%)': [], 'Diferencia (%)': []}
        
        for col in sorted(columnas_comunes):
            # Calcular porcentajes de valores nulos
            nulos_origen_pct = round(df_origen[col].isna().mean() * 100, 2)
            nulos_destino_pct = round(df_destino[col].isna().mean() * 100, 2)
            diferencia = round(nulos_destino_pct - nulos_origen_pct, 2)
            
            # Agregar a la tabla si hay diferencias o valores nulos significativos
            if diferencia != 0 or nulos_origen_pct > 0 or nulos_destino_pct > 0:
                tabla_nulos['Columna'].append(col)
                tabla_nulos['Nulos Origen (%)'].append(nulos_origen_pct)
                tabla_nulos['Nulos Destino (%)'].append(nulos_destino_pct)
                tabla_nulos['Diferencia (%)'].append(diferencia)
        
        # Mostrar los resultados de nulos ordenados por diferencia
        df_nulos = pd.DataFrame(tabla_nulos)
        if len(df_nulos) > 0:
            df_nulos = df_nulos.sort_values('Diferencia (%)', ascending=False)
            print("\nColumnas con diferencias en nulos (origen vs destino):")
            print(df_nulos.to_string(index=False))
            
            # Alertas de columnas con diferencias significativas
            diferencias_importantes = df_nulos[abs(df_nulos['Diferencia (%)']) > 5]
            if len(diferencias_importantes) > 0:
                print("\n🚨 ALERTAS: COLUMNAS CON DIFERENCIAS SIGNIFICATIVAS")
                print(diferencias_importantes.to_string(index=False))
        else:
            print("✅ No hay diferencias en valores nulos entre origen y destino")
        
        # 4. Análisis específico de campos con NAN en la imagen
        print("\n📊 ANÁLISIS DE CAMPOS ESPECÍFICOS")
        print("-" * 50)
        
        # Verificar si ISSUER_ID existe y tiene nulos
        campos_especificos = ['ISSUER_ID']
        for campo in campos_especificos:
            if campo in columnas_comunes:
                nulos_origen = df_origen[campo].isna().sum()
                nulos_destino = df_destino[campo].isna().sum()
                
                print(f"Análisis del campo {campo}:")
                print(f"   • Origen: {nulos_origen} nulos ({round(nulos_origen/len(df_origen)*100, 2)}%)")
                print(f"   • Destino: {nulos_destino} nulos ({round(nulos_destino/len(df_destino)*100, 2)}%)")
                
                # Distribucion de valores
                print(f"\nDistribución de valores en {campo}:")
                print(f"   • Origen:")
                print(pd.DataFrame(df_origen[campo].value_counts(dropna=False).head(10)).to_string())
                print(f"   • Destino:")
                print(pd.DataFrame(df_destino[campo].value_counts(dropna=False).head(10)).to_string())
        
        # 5. Verificar clave primaria y completitud
        print("\n🔑 VERIFICACIÓN DE CLAVE PRIMARIA")
        print("-" * 50)
        clave_primaria = 'WALLET_NUMBER'  # Según configuración
        
        print(f"Verificando clave primaria: {clave_primaria}")
        
        # Verificar nulos en clave primaria
        nulos_clave_origen = df_origen[clave_primaria].isna().sum() if clave_primaria in df_origen.columns else "N/A"
        nulos_clave_destino = df_destino[clave_primaria].isna().sum() if clave_primaria in df_destino.columns else "N/A"
        
        print(f"Nulos en {clave_primaria}:")
        print(f"   • Origen: {nulos_clave_origen}")
        print(f"   • Destino: {nulos_clave_destino}")
        
        # Verificar duplicados en clave primaria
        if clave_primaria in df_origen.columns:
            duplicados_origen = df_origen[df_origen.duplicated(subset=[clave_primaria], keep=False)]
            print(f"Duplicados en origen para {clave_primaria}: {len(duplicados_origen)}")
            
        if clave_primaria in df_destino.columns:
            duplicados_destino = df_destino[df_destino.duplicated(subset=[clave_primaria], keep=False)]
            print(f"Duplicados en destino para {clave_primaria}: {len(duplicados_destino)}")
        
        # 6. Diagnóstico y recomendaciones
        print("\n�� DIAGNÓSTICO DE INGENIERO DE DATOS")
        print("-" * 50)
        
        if len(df_nulos) > 0 and any(df_nulos['Diferencia (%)'] > 0):
            print("❌ PROBLEMA DETECTADO: Incremento de valores nulos en el destino")
            print("   Esto sugiere una degradación de datos durante el proceso ETL, lo que podría provocar inconsistencias.")
            
            print("\n🔧 RECOMENDACIONES:")
            print("   1. Revisar el proceso de transformación para estos campos específicos")
            print("   2. Verificar si hay reglas de negocio que estén descartando valores o sustituyéndolos por NULL")
            print("   3. Comprobar si hay errores en la función de hash para la detección de claves primarias")
            print("   4. Revisar la definición de tabla_primary_keys_simple.ini y asegurar que está correcta")
            
        else:
            print("✅ ESTRUCTURA GENERAL: No se detectaron problemas graves en la transformación ETL")
        
        # Problema específico con NAN en campos numéricos
        print("\n❓ PROBLEMA ESPECÍFICO DE CAMPOS NAN:")
        if 'ISSUER_ID' in columnas_comunes and df_destino['ISSUER_ID'].isna().any():
            print("   El campo ISSUER_ID muestra valores NAN en el destino.")
            print("   Posibles causas:")
            print("   1. La fuente original ya contiene estos valores como NULL/NAN")
            print("   2. Hay un problema en la transformación que está convirtiendo valores vacíos a NAN")
            print("   3. Podría haber un problema de tipo de datos (numérico vs string)")
            
            # Verificar si es problema de tipo de datos
            tipo_origen = str(df_origen['ISSUER_ID'].dtype) if 'ISSUER_ID' in df_origen.columns else "N/A"
            tipo_destino = str(df_destino['ISSUER_ID'].dtype) if 'ISSUER_ID' in df_destino.columns else "N/A"
            
            print(f"\n   Tipos de datos:")
            print(f"   • Origen: {tipo_origen}")
            print(f"   • Destino: {tipo_destino}")
            
            if tipo_origen != tipo_destino:
                print("\n   ⚠️ Los tipos de datos difieren entre origen y destino.")
                print("   Esto podría estar causando problemas de interpretación de valores.")
        
        print("\n✅ ANÁLISIS COMPLETADO")
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        logging.error(f"Error en análisis: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Iniciando análisis de estructura como Ingeniero de Datos...")
    analizar_estructura_origen_vs_destino()
