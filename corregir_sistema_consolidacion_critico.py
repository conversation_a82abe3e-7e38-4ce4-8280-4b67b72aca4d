#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚨 CORRECCIÓN CRÍTICA: Sistema de Consolidación MTX_WALLET_ORA
===============================================================

PROBLEMA IDENTIFICADO:
- El sistema NO está usando WALLET_NUMBER como clave primaria
- En su lugar, está generando hash de TODOS los valores del registro
- Esto causa que registros únicos sean tratados como diferentes
- RESULTADO: Pérdida de 39 registros únicos (48.8% de datos)

CAUSA RAÍZ:
- La función generar_clave_registro_pura() no está accediendo correctamente
  a la configuración de tabla_primary_keys_simple.ini
- Se está cayendo al fallback de hash completo en lugar de usar WALLET_NUMBER

SOLUCIÓN:
- Forzar el uso correcto de WALLET_NUMBER como clave primaria
- Corregir la lógica de detección de claves
- Validar que el sistema funcione correctamente

Como Data Engineer: Este es un error CRÍTICO que debe ser corregido inmediatamente.
"""

import sys
import pandas as pd
import boto3
import logging
from io import BytesIO
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Agregar path
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Configuración S3
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"
ARCHIVO_ORIGEN = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-080250_chunk_0.parquet"
ARCHIVO_CONSOLIDADO = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
CLAVE_PRIMARIA_CORRECTA = "WALLET_NUMBER"

def leer_parquet_desde_s3(bucket: str, key: str) -> pd.DataFrame:
    """Lee un archivo parquet desde S3."""
    try:
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        parquet_content = response['Body'].read()
        df = pd.read_parquet(BytesIO(parquet_content))
        return df
    except Exception as e:
        print(f"   ❌ Error leyendo archivo: {str(e)}")
        return pd.DataFrame()

def escribir_parquet_a_s3(df: pd.DataFrame, bucket: str, key: str) -> None:
    """Escribe un DataFrame como archivo Parquet en S3."""
    try:
        s3_client = boto3.client('s3')
        buffer = BytesIO()
        df.to_parquet(buffer, index=False, engine='pyarrow')
        buffer.seek(0)
        
        s3_client.put_object(
            Bucket=bucket,
            Key=key,
            Body=buffer.getvalue(),
            ContentType='application/octet-stream'
        )
        print(f"   ✅ Archivo escrito: {bucket}/{key}")
        
    except Exception as e:
        print(f"   ❌ Error escribiendo archivo: {str(e)}")
        raise

def eliminar_duplicados_correcto(df: pd.DataFrame, clave_primaria: str) -> pd.DataFrame:
    """
    Elimina duplicados CORRECTAMENTE usando solo la clave primaria de negocio.
    Mantiene el registro más reciente por DATA_LAKE_PARTITION_DATE.
    """
    print(f"🔧 APLICANDO CORRECCIÓN CRÍTICA:")
    print(f"   📍 Clave primaria CORRECTA: {clave_primaria}")
    
    registros_originales = len(df)
    print(f"   📊 Registros originales: {registros_originales}")
    
    # Verificar valores únicos originales
    valores_unicos_originales = df[clave_primaria].nunique()
    print(f"   📊 Valores únicos de {clave_primaria}: {valores_unicos_originales}")
    
    # Verificar si realmente hay duplicados
    duplicados_reales = len(df) - valores_unicos_originales
    print(f"   📊 Duplicados REALES encontrados: {duplicados_reales}")
    
    if duplicados_reales == 0:
        print(f"   ✅ NO HAY DUPLICADOS REALES - Todos los registros son únicos")
        print(f"   🚨 PROBLEMA CONFIRMADO: El sistema anterior estaba eliminando registros únicos")
        return df
    
    # Si hay duplicados reales, eliminarlos manteniendo el más reciente
    print(f"   🔄 Eliminando {duplicados_reales} duplicados reales...")
    
    # Ordenar por fecha más reciente primero
    if 'DATA_LAKE_PARTITION_DATE' in df.columns:
        df_ordenado = df.sort_values('DATA_LAKE_PARTITION_DATE', ascending=False)
    else:
        df_ordenado = df.copy()
    
    # Eliminar duplicados manteniendo el primero (más reciente)
    df_sin_duplicados = df_ordenado.drop_duplicates(subset=[clave_primaria], keep='first')
    
    registros_finales = len(df_sin_duplicados)
    print(f"   📊 Registros finales: {registros_finales}")
    print(f"   📊 Registros eliminados: {registros_originales - registros_finales}")
    
    return df_sin_duplicados

def corregir_consolidacion_mtx_wallet():
    """
    Corrige el sistema de consolidación de MTX_WALLET_ORA aplicando
    la lógica CORRECTA de deduplicación.
    """
    print("🚨 CORRECCIÓN CRÍTICA DEL SISTEMA DE CONSOLIDACIÓN")
    print("=" * 70)
    print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Leer archivo origen
    print(f"\n📥 PASO 1: Leer archivo origen...")
    print(f"   📍 Bucket: {S3_BRONZE_BUCKET}")
    print(f"   📍 Archivo: {ARCHIVO_ORIGEN}")
    
    df_origen = leer_parquet_desde_s3(S3_BRONZE_BUCKET, ARCHIVO_ORIGEN)
    
    if df_origen.empty:
        print(f"❌ No se pudo leer el archivo origen")
        return
    
    print(f"   ✅ Archivo origen leído: {len(df_origen)} registros")
    
    # 2. Verificar el problema actual
    print(f"\n🔍 PASO 2: Verificar problema actual...")
    
    df_consolidado_actual = leer_parquet_desde_s3(S3_SILVER_BUCKET, ARCHIVO_CONSOLIDADO)
    
    if not df_consolidado_actual.empty:
        print(f"   📊 Consolidado actual: {len(df_consolidado_actual)} registros")
        print(f"   📊 Registros perdidos: {len(df_origen) - len(df_consolidado_actual)}")
        print(f"   🚨 CONFIRMADO: Pérdida de {len(df_origen) - len(df_consolidado_actual)} registros únicos")
    
    # 3. Aplicar consolidación CORRECTA
    print(f"\n🔧 PASO 3: Aplicar consolidación CORRECTA...")
    
    df_consolidado_correcto = eliminar_duplicados_correcto(df_origen, CLAVE_PRIMARIA_CORRECTA)
    
    # 4. Verificar que la corrección funciona
    print(f"\n✅ PASO 4: Verificar corrección...")
    
    # Verificar que no se perdieron registros únicos
    wallet_origen = set(df_origen[CLAVE_PRIMARIA_CORRECTA])
    wallet_corregido = set(df_consolidado_correcto[CLAVE_PRIMARIA_CORRECTA])
    
    perdidos_correccion = wallet_origen - wallet_corregido
    
    if len(perdidos_correccion) == 0:
        print(f"   ✅ CORRECCIÓN EXITOSA: NO se perdieron registros únicos")
        print(f"   📊 Origen: {len(wallet_origen)} valores únicos")
        print(f"   📊 Corregido: {len(wallet_corregido)} valores únicos")
        print(f"   🎯 PRESERVACIÓN: 100% de registros únicos mantenidos")
    else:
        print(f"   ❌ ERROR EN CORRECCIÓN: {len(perdidos_correccion)} registros aún perdidos")
        return
    
    # 5. Guardar archivo corregido
    print(f"\n💾 PASO 5: Guardar consolidado corregido...")
    
    archivo_corregido = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro_CORREGIDO.parquet"
    escribir_parquet_a_s3(df_consolidado_correcto, S3_SILVER_BUCKET, archivo_corregido)
    
    # 6. Resumen final
    print(f"\n📋 RESUMEN DE CORRECCIÓN:")
    print(f"   📍 Archivo original: {len(df_origen)} registros")
    print(f"   📍 Consolidado anterior: {len(df_consolidado_actual)} registros")
    print(f"   📍 Consolidado CORREGIDO: {len(df_consolidado_correcto)} registros")
    print(f"   📍 Registros RECUPERADOS: {len(df_consolidado_correcto) - len(df_consolidado_actual)}")
    print(f"   📍 Porcentaje RECUPERADO: {((len(df_consolidado_correcto) - len(df_consolidado_actual)) / len(df_origen)) * 100:.1f}%")
    
    print(f"\n🎯 COMO DATA ENGINEER:")
    print(f"   ✅ PROBLEMA IDENTIFICADO: Sistema usaba hash completo en lugar de WALLET_NUMBER")
    print(f"   ✅ SOLUCIÓN APLICADA: Deduplicación correcta por clave primaria de negocio")
    print(f"   ✅ DATOS RECUPERADOS: {len(df_consolidado_correcto) - len(df_consolidado_actual)} registros únicos")
    print(f"   ✅ INTEGRIDAD RESTAURADA: 100% de registros únicos preservados")
    
    print(f"\n🔧 PRÓXIMOS PASOS:")
    print(f"   1. Validar el archivo corregido: {archivo_corregido}")
    print(f"   2. Reemplazar el consolidado_puro.parquet original")
    print(f"   3. Corregir la función generar_clave_registro_pura() en el código")
    print(f"   4. Ejecutar pruebas completas del sistema")
    
    print(f"\n⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🚨 CORRECCIÓN CRÍTICA COMPLETADA")

if __name__ == "__main__":
    corregir_consolidacion_mtx_wallet()
