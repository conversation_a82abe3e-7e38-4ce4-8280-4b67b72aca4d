#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verificación de Estructura - MTX_WALLET_ORA
==========================================

Script para verificar que la estructura entre origen (Bronze Zone) 
y destino (Silver Zone consolidado) se mantiene idéntica.

Como Data Engineer, es crítico asegurar que la consolidación 
preserve la estructura original de los datos.
"""

import boto3
import pandas as pd
from io import BytesIO
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Any
import json

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Configuración S3
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"

def leer_parquet_desde_s3(bucket: str, key: str) -> pd.DataFrame:
    """Lee un archivo Parquet desde S3."""
    try:
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df = pd.read_parquet(buffer)
        logging.info(f"✅ Archivo leído: {bucket}/{key} - {len(df)} registros")
        return df
    except Exception as e:
        logging.error(f"❌ Error leyendo {bucket}/{key}: {str(e)}")
        return pd.DataFrame()

def analizar_estructura_dataframe(df: pd.DataFrame, nombre: str) -> Dict[str, Any]:
    """Analiza la estructura completa de un DataFrame."""
    if df.empty:
        return {"error": "DataFrame vacío"}
    
    estructura = {
        "nombre": nombre,
        "total_registros": len(df),
        "total_columnas": len(df.columns),
        "columnas": {},
        "tipos_de_datos": {},
        "estadisticas_nulos": {},
        "muestra_datos": {}
    }
    
    for col in df.columns:
        # Información básica de la columna
        estructura["columnas"][col] = {
            "tipo": str(df[col].dtype),
            "valores_nulos": df[col].isnull().sum(),
            "porcentaje_nulos": round((df[col].isnull().sum() / len(df)) * 100, 2),
            "valores_unicos": df[col].nunique() if df[col].nunique() <= 1000 else ">1000"
        }
        
        # Tipos de datos agrupados
        tipo_grupo = str(df[col].dtype)
        if tipo_grupo not in estructura["tipos_de_datos"]:
            estructura["tipos_de_datos"][tipo_grupo] = []
        estructura["tipos_de_datos"][tipo_grupo].append(col)
        
        # Estadísticas de nulos
        nulos = df[col].isnull().sum()
        if nulos > 0:
            estructura["estadisticas_nulos"][col] = {
                "cantidad_nulos": nulos,
                "porcentaje": round((nulos / len(df)) * 100, 2)
            }
        
        # Muestra de datos (primeros 3 valores no nulos)
        valores_no_nulos = df[col].dropna().head(3).tolist()
        estructura["muestra_datos"][col] = [str(v) for v in valores_no_nulos]
    
    return estructura

def comparar_estructuras(estructura_origen: Dict, estructura_destino: Dict) -> Dict[str, Any]:
    """Compara dos estructuras de DataFrames."""
    comparacion = {
        "timestamp": datetime.now().isoformat(),
        "resumen": {},
        "diferencias_columnas": {},
        "diferencias_tipos": {},
        "diferencias_registros": {},
        "integridad_preservada": True,
        "recomendaciones": []
    }
    
    # Resumen básico
    comparacion["resumen"] = {
        "origen": {
            "archivo": estructura_origen["nombre"],
            "registros": estructura_origen["total_registros"],
            "columnas": estructura_origen["total_columnas"]
        },
        "destino": {
            "archivo": estructura_destino["nombre"],
            "registros": estructura_destino["total_registros"],
            "columnas": estructura_destino["total_columnas"]
        }
    }
    
    # Comparar columnas
    cols_origen = set(estructura_origen["columnas"].keys())
    cols_destino = set(estructura_destino["columnas"].keys())
    
    # Excluir columnas de metadatos agregadas en consolidación
    cols_metadatos = {
        'source_file', 'source_year', 'source_month', 'source_day',
        'data_lake_consolidated_timestamp', 'data_lake_consolidation_version'
    }
    
    cols_destino_originales = cols_destino - cols_metadatos
    
    if cols_origen != cols_destino_originales:
        comparacion["integridad_preservada"] = False
        comparacion["diferencias_columnas"] = {
            "solo_en_origen": list(cols_origen - cols_destino_originales),
            "solo_en_destino": list(cols_destino_originales - cols_origen),
            "metadatos_agregados": list(cols_metadatos.intersection(cols_destino))
        }
        comparacion["recomendaciones"].append("⚠️ Diferencias en columnas detectadas")
    else:
        comparacion["diferencias_columnas"]["status"] = "✅ Columnas originales preservadas correctamente"
        comparacion["diferencias_columnas"]["metadatos_agregados"] = list(cols_metadatos.intersection(cols_destino))
    
    # Comparar tipos de datos para columnas comunes
    tipos_diferentes = {}
    for col in cols_origen.intersection(cols_destino_originales):
        tipo_origen = estructura_origen["columnas"][col]["tipo"]
        tipo_destino = estructura_destino["columnas"][col]["tipo"]
        
        if tipo_origen != tipo_destino:
            tipos_diferentes[col] = {
                "origen": tipo_origen,
                "destino": tipo_destino
            }
            comparacion["integridad_preservada"] = False
    
    if tipos_diferentes:
        comparacion["diferencias_tipos"] = tipos_diferentes
        comparacion["recomendaciones"].append("⚠️ Tipos de datos modificados - revisar preservación")
    else:
        comparacion["diferencias_tipos"]["status"] = "✅ Tipos de datos preservados correctamente"
    
    # Análisis de registros
    if estructura_destino["total_registros"] < estructura_origen["total_registros"]:
        diferencia = estructura_origen["total_registros"] - estructura_destino["total_registros"]
        comparacion["diferencias_registros"] = {
            "registros_eliminados": diferencia,
            "porcentaje_perdida": round((diferencia / estructura_origen["total_registros"]) * 100, 2),
            "motivo_probable": "Deduplicación por clave de negocio"
        }
        comparacion["recomendaciones"].append(f"ℹ️ {diferencia} registros eliminados por deduplicación")
    elif estructura_destino["total_registros"] == estructura_origen["total_registros"]:
        comparacion["diferencias_registros"]["status"] = "✅ Todos los registros preservados"
    else:
        comparacion["diferencias_registros"]["status"] = "⚠️ Más registros en destino que en origen"
        comparacion["integridad_preservada"] = False
    
    return comparacion

def generar_reporte_estructura(comparacion: Dict) -> str:
    """Genera un reporte legible de la comparación de estructuras."""
    reporte = []
    reporte.append("=" * 80)
    reporte.append("📊 REPORTE DE VERIFICACIÓN DE ESTRUCTURA - MTX_WALLET_ORA")
    reporte.append("=" * 80)
    reporte.append(f"Timestamp: {comparacion['timestamp']}")
    reporte.append("")
    
    # Resumen
    reporte.append("📋 RESUMEN DE ARCHIVOS:")
    reporte.append(f"  🔹 ORIGEN (Bronze Zone):")
    reporte.append(f"     - Archivo: {comparacion['resumen']['origen']['archivo']}")
    reporte.append(f"     - Registros: {comparacion['resumen']['origen']['registros']:,}")
    reporte.append(f"     - Columnas: {comparacion['resumen']['origen']['columnas']}")
    reporte.append("")
    reporte.append(f"  🔹 DESTINO (Silver Zone Consolidado):")
    reporte.append(f"     - Archivo: {comparacion['resumen']['destino']['archivo']}")
    reporte.append(f"     - Registros: {comparacion['resumen']['destino']['registros']:,}")
    reporte.append(f"     - Columnas: {comparacion['resumen']['destino']['columnas']}")
    reporte.append("")
    
    # Estado general
    if comparacion["integridad_preservada"]:
        reporte.append("✅ ESTADO GENERAL: ESTRUCTURA PRESERVADA CORRECTAMENTE")
    else:
        reporte.append("⚠️ ESTADO GENERAL: DIFERENCIAS DETECTADAS EN LA ESTRUCTURA")
    reporte.append("")
    
    # Diferencias en columnas
    reporte.append("📊 ANÁLISIS DE COLUMNAS:")
    if "status" in comparacion["diferencias_columnas"]:
        reporte.append(f"  {comparacion['diferencias_columnas']['status']}")
    else:
        if comparacion["diferencias_columnas"].get("solo_en_origen"):
            reporte.append(f"  ❌ Columnas perdidas: {comparacion['diferencias_columnas']['solo_en_origen']}")
        if comparacion["diferencias_columnas"].get("solo_en_destino"):
            reporte.append(f"  ➕ Columnas nuevas: {comparacion['diferencias_columnas']['solo_en_destino']}")
    
    if comparacion["diferencias_columnas"].get("metadatos_agregados"):
        reporte.append(f"  📝 Metadatos agregados: {comparacion['diferencias_columnas']['metadatos_agregados']}")
    reporte.append("")
    
    # Diferencias en tipos
    reporte.append("🔍 ANÁLISIS DE TIPOS DE DATOS:")
    if "status" in comparacion["diferencias_tipos"]:
        reporte.append(f"  {comparacion['diferencias_tipos']['status']}")
    else:
        reporte.append("  ⚠️ Tipos de datos modificados:")
        for col, tipos in comparacion["diferencias_tipos"].items():
            reporte.append(f"     - {col}: {tipos['origen']} → {tipos['destino']}")
    reporte.append("")
    
    # Análisis de registros
    reporte.append("📈 ANÁLISIS DE REGISTROS:")
    if "status" in comparacion["diferencias_registros"]:
        reporte.append(f"  {comparacion['diferencias_registros']['status']}")
    else:
        if "registros_eliminados" in comparacion["diferencias_registros"]:
            reporte.append(f"  📉 Registros eliminados: {comparacion['diferencias_registros']['registros_eliminados']:,}")
            reporte.append(f"  📊 Porcentaje de pérdida: {comparacion['diferencias_registros']['porcentaje_perdida']}%")
            reporte.append(f"  💡 Motivo probable: {comparacion['diferencias_registros']['motivo_probable']}")
    reporte.append("")
    
    # Recomendaciones
    if comparacion["recomendaciones"]:
        reporte.append("💡 RECOMENDACIONES:")
        for rec in comparacion["recomendaciones"]:
            reporte.append(f"  {rec}")
        reporte.append("")
    
    reporte.append("=" * 80)
    return "\n".join(reporte)

def main():
    """Función principal de verificación."""
    print("🔍 Iniciando verificación de estructura MTX_WALLET_ORA...")
    print("📋 Como Data Engineer, verificaremos que la consolidación preserve la estructura original")
    print()
    
    # Definir rutas de archivos
    archivo_origen = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-013525_chunk_0.parquet"
    archivo_destino = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    
    print(f"📂 Archivo ORIGEN (Bronze Zone): {archivo_origen}")
    print(f"📂 Archivo DESTINO (Silver Zone): {archivo_destino}")
    print()
    
    # Leer archivos
    print("📖 Leyendo archivo de origen (Bronze Zone)...")
    df_origen = leer_parquet_desde_s3(S3_BRONZE_BUCKET, archivo_origen)
    
    print("📖 Leyendo archivo de destino (Silver Zone)...")
    df_destino = leer_parquet_desde_s3(S3_SILVER_BUCKET, archivo_destino)
    
    if df_origen.empty or df_destino.empty:
        print("❌ Error: No se pudieron leer ambos archivos")
        return
    
    # Analizar estructuras
    print("🔍 Analizando estructura del archivo origen...")
    estructura_origen = analizar_estructura_dataframe(df_origen, archivo_origen)
    
    print("🔍 Analizando estructura del archivo destino...")
    estructura_destino = analizar_estructura_dataframe(df_destino, archivo_destino)
    
    # Comparar estructuras
    print("⚖️ Comparando estructuras...")
    comparacion = comparar_estructuras(estructura_origen, estructura_destino)
    
    # Generar y mostrar reporte
    reporte = generar_reporte_estructura(comparacion)
    print(reporte)
    
    # Guardar reporte detallado
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    archivo_reporte = f"reporte_estructura_mtx_wallet_{timestamp}.json"
    
    with open(archivo_reporte, 'w', encoding='utf-8') as f:
        json.dump({
            "comparacion": comparacion,
            "estructura_origen": estructura_origen,
            "estructura_destino": estructura_destino
        }, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"💾 Reporte detallado guardado: {archivo_reporte}")
    
    # Conclusión final
    if comparacion["integridad_preservada"]:
        print("\n✅ CONCLUSIÓN: La estructura se ha preservado correctamente en la consolidación")
    else:
        print("\n⚠️ CONCLUSIÓN: Se detectaron diferencias que requieren revisión")

if __name__ == "__main__":
    main()
