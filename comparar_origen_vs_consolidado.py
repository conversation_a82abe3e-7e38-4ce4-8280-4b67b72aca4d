#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para comparar archivo origen vs consolidado y detectar pérdida de datos
"""

import os
import sys
import pandas as pd
import boto3
import logging
from io import BytesIO
from botocore.exceptions import ClientError
import warnings

# Suprimir warnings
warnings.filterwarnings('ignore')

# Configurar logging detallado
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Agregar el directorio actual al path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def comparar_origen_vs_consolidado():
    """Compara archivo origen vs consolidado para detectar pérdida de datos"""
    
    print("\n" + "="*90)
    print("🔍 COMPARACIÓN: Archivo Origen vs Consolidado")
    print("="*90)
    
    # Configuración de archivos específicos
    bucket_bronze = "prd-datalake-bronze-zone-637423440311"
    bucket_silver = "prd-datalake-silver-zone-637423440311"
    
    archivo_origen = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250530-164129_chunk_0.parquet"
    archivo_consolidado = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado.parquet"
    
    print(f"📁 Archivo Origen (Bronze): s3://{bucket_bronze}/{archivo_origen}")
    print(f"📁 Archivo Consolidado (Silver): s3://{bucket_silver}/{archivo_consolidado}")
    
    # 1. Leer archivo origen
    print(f"\n📊 LEYENDO ARCHIVO ORIGEN...")
    df_origen = leer_archivo_s3(bucket_bronze, archivo_origen)
    
    if df_origen.empty:
        print(f"❌ No se pudo leer el archivo origen")
        return
    
    print(f"✅ Archivo origen cargado: {len(df_origen)} registros")
    print(f"📋 Columnas origen: {len(df_origen.columns)} columnas")
    
    # 2. Leer archivo consolidado
    print(f"\n📊 LEYENDO ARCHIVO CONSOLIDADO...")
    df_consolidado = leer_archivo_s3(bucket_silver, archivo_consolidado)
    
    if df_consolidado.empty:
        print(f"❌ No se pudo leer el archivo consolidado")
        return
    
    print(f"✅ Archivo consolidado cargado: {len(df_consolidado)} registros")
    print(f"📋 Columnas consolidado: {len(df_consolidado.columns)} columnas")
    
    # 3. Comparación básica de estructura
    print(f"\n🔍 COMPARACIÓN DE ESTRUCTURA:")
    print(f"   Registros - Origen: {len(df_origen)}, Consolidado: {len(df_consolidado)}")
    print(f"   Columnas - Origen: {len(df_origen.columns)}, Consolidado: {len(df_consolidado.columns)}")
    
    if len(df_origen) != len(df_consolidado):
        print(f"   ⚠️ DIFERENCIA EN NÚMERO DE REGISTROS: {len(df_origen) - len(df_consolidado)}")
    
    # 4. Comparar columnas
    print(f"\n📋 COMPARACIÓN DE COLUMNAS:")
    columnas_origen = set(df_origen.columns)
    columnas_consolidado = set(df_consolidado.columns)
    
    columnas_perdidas = columnas_origen - columnas_consolidado
    columnas_nuevas = columnas_consolidado - columnas_origen
    columnas_comunes = columnas_origen & columnas_consolidado
    
    if columnas_perdidas:
        print(f"   ❌ COLUMNAS PERDIDAS en consolidado: {len(columnas_perdidas)}")
        for col in sorted(columnas_perdidas):
            print(f"       - {col}")
    
    if columnas_nuevas:
        print(f"   ➕ COLUMNAS NUEVAS en consolidado: {len(columnas_nuevas)}")
        for col in sorted(columnas_nuevas):
            print(f"       + {col}")
    
    print(f"   ✅ COLUMNAS COMUNES: {len(columnas_comunes)}")
    
    # 5. Comparar datos en columnas comunes
    print(f"\n🔍 COMPARACIÓN DE DATOS EN COLUMNAS COMUNES:")
    
    if len(columnas_comunes) == 0:
        print(f"   ❌ No hay columnas comunes para comparar")
        return
    
    # Análisis columna por columna
    problemas_encontrados = 0
    
    for col in sorted(columnas_comunes):
        print(f"\n   📊 Analizando columna: {col}")
        
        # Estadísticas básicas
        valores_origen = df_origen[col].dropna()
        valores_consolidado = df_consolidado[col].dropna()
        
        print(f"       Valores no nulos - Origen: {len(valores_origen)}, Consolidado: {len(valores_consolidado)}")
        
        # Comparar valores únicos
        unicos_origen = set(valores_origen.astype(str))
        unicos_consolidado = set(valores_consolidado.astype(str))
        
        valores_perdidos = unicos_origen - unicos_consolidado
        valores_nuevos = unicos_consolidado - unicos_origen
        
        if valores_perdidos:
            print(f"       ❌ VALORES PERDIDOS: {len(valores_perdidos)}")
            problemas_encontrados += 1
            
            # Mostrar algunos ejemplos de valores perdidos
            ejemplos = list(valores_perdidos)[:5]
            print(f"          Ejemplos: {ejemplos}")
            
            if len(valores_perdidos) > 5:
                print(f"          ... y {len(valores_perdidos) - 5} más")
        
        if valores_nuevos:
            print(f"       ➕ VALORES NUEVOS: {len(valores_nuevos)}")
            ejemplos = list(valores_nuevos)[:5]
            print(f"          Ejemplos: {ejemplos}")
        
        if not valores_perdidos and not valores_nuevos:
            print(f"       ✅ Sin cambios en valores")
    
    # 6. Análisis específico de WALLET_NUMBER (clave primaria)
    print(f"\n🔑 ANÁLISIS ESPECÍFICO DE WALLET_NUMBER:")
    
    if 'WALLET_NUMBER' in columnas_comunes:
        analizar_wallet_number_detallado(df_origen, df_consolidado)
    else:
        print(f"   ❌ WALLET_NUMBER no está en columnas comunes")
    
    # 7. Comparar primeros registros lado a lado
    print(f"\n📄 COMPARACIÓN DE PRIMEROS 3 REGISTROS:")
    comparar_registros_detallado(df_origen, df_consolidado, columnas_comunes)
    
    # 8. Resumen final
    print(f"\n📊 RESUMEN FINAL:")
    if problemas_encontrados == 0:
        print(f"   ✅ No se detectaron pérdidas de datos")
    else:
        print(f"   ⚠️ Se detectaron {problemas_encontrados} columnas con posibles pérdidas de datos")
        print(f"   💡 Revisar la función de normalización o el proceso de consolidación")

def leer_archivo_s3(bucket, key):
    """Lee un archivo Parquet desde S3"""
    try:
        s3_client = boto3.client('s3')
        
        # Verificar si existe
        try:
            s3_client.head_object(Bucket=bucket, Key=key)
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                print(f"   ❌ Archivo NO encontrado: s3://{bucket}/{key}")
                return pd.DataFrame()
            else:
                print(f"   ❌ Error accediendo al archivo: {str(e)}")
                return pd.DataFrame()
        
        # Leer archivo
        response = s3_client.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(response['Body'].read())
        df = pd.read_parquet(buffer)
        
        return df
        
    except Exception as e:
        print(f"   ❌ Error leyendo archivo: {str(e)}")
        return pd.DataFrame()

def analizar_wallet_number_detallado(df_origen, df_consolidado):
    """Análisis detallado de la columna WALLET_NUMBER"""
    print(f"   🔍 Análisis detallado de WALLET_NUMBER:")
    
    # Valores únicos
    wallet_origen = df_origen['WALLET_NUMBER'].dropna()
    wallet_consolidado = df_consolidado['WALLET_NUMBER'].dropna()
    
    print(f"       Total registros - Origen: {len(wallet_origen)}, Consolidado: {len(wallet_consolidado)}")
    print(f"       Valores únicos - Origen: {wallet_origen.nunique()}, Consolidado: {wallet_consolidado.nunique()}")
    
    # Tipos de datos
    print(f"       Tipo de dato - Origen: {df_origen['WALLET_NUMBER'].dtype}, Consolidado: {df_consolidado['WALLET_NUMBER'].dtype}")
    
    # Muestra de valores
    print(f"       Muestra origen: {wallet_origen.head(3).tolist()}")
    print(f"       Muestra consolidado: {wallet_consolidado.head(3).tolist()}")
    
    # Valores problemáticos
    vacios_origen = (df_origen['WALLET_NUMBER'].astype(str).str.strip() == '').sum()
    vacios_consolidado = (df_consolidado['WALLET_NUMBER'].astype(str).str.strip() == '').sum()
    
    nulos_origen = df_origen['WALLET_NUMBER'].isna().sum()
    nulos_consolidado = df_consolidado['WALLET_NUMBER'].isna().sum()
    
    print(f"       Valores vacíos - Origen: {vacios_origen}, Consolidado: {vacios_consolidado}")
    print(f"       Valores nulos - Origen: {nulos_origen}, Consolidado: {nulos_consolidado}")
    
    # Comparar valores específicos
    if len(wallet_origen) > 0 and len(wallet_consolidado) > 0:
        valores_origen_set = set(wallet_origen.astype(str))
        valores_consolidado_set = set(wallet_consolidado.astype(str))
        
        perdidos = valores_origen_set - valores_consolidado_set
        if perdidos:
            print(f"       ❌ WALLET_NUMBER perdidos: {len(perdidos)}")
            print(f"          Ejemplos: {list(perdidos)[:5]}")

def comparar_registros_detallado(df_origen, df_consolidado, columnas_comunes):
    """Compara registros específicos lado a lado"""
    
    # Tomar las primeras 3 filas de cada DataFrame
    n_filas = min(3, len(df_origen), len(df_consolidado))
    
    if n_filas == 0:
        print(f"   ❌ No hay registros para comparar")
        return
    
    # Seleccionar solo columnas importantes para la comparación
    columnas_importantes = ['WALLET_NUMBER', 'USER_ID', 'PIN_NUMBER', 'STATUS', 'CREATED_ON']
    columnas_a_comparar = [col for col in columnas_importantes if col in columnas_comunes]
    
    if not columnas_a_comparar:
        # Si no hay columnas importantes, tomar las primeras 5 columnas comunes
        columnas_a_comparar = list(sorted(columnas_comunes))[:5]
    
    print(f"   📋 Comparando columnas: {columnas_a_comparar}")
    
    for i in range(n_filas):
        print(f"\n   📄 REGISTRO {i+1}:")
        print(f"       {'COLUMNA':<25} {'ORIGEN':<30} {'CONSOLIDADO':<30} {'IGUAL'}")
        print(f"       {'-'*25} {'-'*30} {'-'*30} {'-'*5}")
        
        for col in columnas_a_comparar:
            valor_origen = str(df_origen.iloc[i][col]) if i < len(df_origen) else "N/A"
            valor_consolidado = str(df_consolidado.iloc[i][col]) if i < len(df_consolidado) else "N/A"
            
            # Truncar valores largos
            valor_origen = (valor_origen[:27] + "...") if len(valor_origen) > 30 else valor_origen
            valor_consolidado = (valor_consolidado[:27] + "...") if len(valor_consolidado) > 30 else valor_consolidado
            
            es_igual = "✅" if valor_origen == valor_consolidado else "❌"
            
            print(f"       {col:<25} {valor_origen:<30} {valor_consolidado:<30} {es_igual}")

if __name__ == "__main__":
    comparar_origen_vs_consolidado()
