#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 INVESTIGACIÓN PROFUNDA: Causa Raíz de Valores NaN en Merge
===========================================================

Data Engineer: Análisis específico del problema de NaN durante merge incremental
Problema: Merge operation introduces NaN values in columns that had valid data

Análisis objetivo:
1. Comparar datos existentes vs nuevos antes del merge
2. Identificar diferencias en estructura/esquema
3. Analizar el proceso de alineación de tipos de datos  
4. Detectar exactamente dónde se introducen los NaN
5. Proponer solución definitiva

Fecha: 2025-06-02
"""

import boto3
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from io import BytesIO
import sys
import warnings

# Suprimir warnings
warnings.filterwarnings('ignore')

# Configurar ruta para imports
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Configuración S3
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"

def cargar_datos_s3(bucket, key, descripcion):
    """Carga datos desde S3 y maneja errores"""
    try:
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        df = pd.read_parquet(BytesIO(response['Body'].read()), engine='pyarrow')
        
        print(f"✅ {descripcion} cargado:")
        print(f"   📊 Registros: {len(df)}")
        print(f"   📋 Columnas: {len(df.columns)}")
        print(f"   🔑 WALLET_NUMBER únicos: {df['WALLET_NUMBER'].nunique() if 'WALLET_NUMBER' in df.columns else 'N/A'}")
        print()
        
        return df
        
    except Exception as e:
        print(f"❌ Error cargando {descripcion}: {str(e)}")
        return pd.DataFrame()

def analizar_esquemas_detallado(df1, df2, nombre1, nombre2):
    """Análisis detallado de diferencias en esquemas entre dos DataFrames"""
    print(f"🔍 ANÁLISIS DETALLADO DE ESQUEMAS: {nombre1} vs {nombre2}")
    print("=" * 70)
    
    # Comparar columnas
    cols1 = set(df1.columns)
    cols2 = set(df2.columns)
    
    comunes = cols1 & cols2
    solo_df1 = cols1 - cols2
    solo_df2 = cols2 - cols1
    
    print(f"📊 Columnas comunes: {len(comunes)}")
    print(f"📊 Solo en {nombre1}: {len(solo_df1)}")
    print(f"📊 Solo en {nombre2}: {len(solo_df2)}")
    
    if solo_df1:
        print(f"\n🔹 Columnas SOLO en {nombre1}:")
        for col in sorted(solo_df1):
            print(f"   - {col}")
    
    if solo_df2:
        print(f"\n🔹 Columnas SOLO en {nombre2}:")
        for col in sorted(solo_df2):
            print(f"   - {col}")
    
    # Analizar diferencias de tipos en columnas comunes
    print(f"\n🔍 DIFERENCIAS DE TIPOS EN COLUMNAS COMUNES:")
    diferencias_tipos = []
    
    for col in sorted(comunes):
        tipo1 = str(df1[col].dtype)
        tipo2 = str(df2[col].dtype)
        
        if tipo1 != tipo2:
            diferencias_tipos.append({
                'columna': col,
                nombre1: tipo1,
                nombre2: tipo2
            })
    
    if diferencias_tipos:
        print(f"   ⚠️ {len(diferencias_tipos)} columnas con tipos diferentes:")
        for diff in diferencias_tipos[:10]:  # Mostrar top 10
            print(f"   - {diff['columna']}: {diff[nombre1]} → {diff[nombre2]}")
    else:
        print(f"   ✅ Todos los tipos coinciden en columnas comunes")
    
    return diferencias_tipos

def analizar_valores_nan_profundo(df, nombre):
    """Análisis profundo de valores NaN en un DataFrame"""
    print(f"\n🔍 ANÁLISIS PROFUNDO DE NaN: {nombre}")
    print("-" * 50)
    
    total_cols = len(df.columns)
    cols_with_nan = []
    
    for col in df.columns:
        nan_count = df[col].isnull().sum()
        if nan_count > 0:
            nan_percentage = (nan_count / len(df)) * 100
            cols_with_nan.append({
                'columna': col,
                'nan_count': nan_count,
                'percentage': nan_percentage,
                'dtype': str(df[col].dtype)
            })
    
    print(f"📊 Total columnas: {total_cols}")
    print(f"📊 Columnas con NaN: {len(cols_with_nan)}")
    
    if cols_with_nan:
        print(f"\n📋 Detalle de columnas con NaN:")
        for info in sorted(cols_with_nan, key=lambda x: x['nan_count'], reverse=True)[:15]:
            print(f"   - {info['columna']:30} | {info['nan_count']:4d} NaN ({info['percentage']:5.1f}%) | {info['dtype']}")
    
    return cols_with_nan

def simular_proceso_merge_problematico(df_existente, df_nuevos):
    """Simula el proceso de merge para identificar dónde se introducen los NaN"""
    print(f"\n🧪 SIMULACIÓN DEL PROCESO DE MERGE PROBLEMÁTICO")
    print("=" * 70)
    
    print(f"📥 PASO 1: Datos de entrada")
    print(f"   Datos existentes: {len(df_existente)} registros, {len(df_existente.columns)} columnas")
    print(f"   Datos nuevos: {len(df_nuevos)} registros, {len(df_nuevos.columns)} columnas")
    
    # Analizar NaN ANTES del merge
    print(f"\n📋 PASO 2: Análisis NaN ANTES del merge")
    nan_antes_existente = analizar_valores_nan_profundo(df_existente, "Datos Existentes")
    nan_antes_nuevos = analizar_valores_nan_profundo(df_nuevos, "Datos Nuevos")
    
    # Simulación de alineación de columnas (lo que hace el sistema)
    print(f"\n🔧 PASO 3: Simulación de alineación de columnas")
    
    # Obtener todas las columnas únicas
    todas_columnas = list(set(df_existente.columns) | set(df_nuevos.columns))
    print(f"   Total columnas únicas: {len(todas_columnas)}")
    
    # Alinear DataFrames agregando columnas faltantes con NaN
    df_existente_alineado = df_existente.copy()
    df_nuevos_alineado = df_nuevos.copy()
    
    for col in todas_columnas:
        if col not in df_existente_alineado.columns:
            df_existente_alineado[col] = np.nan
            print(f"   ➕ Agregada columna '{col}' con NaN a datos existentes")
            
        if col not in df_nuevos_alineado.columns:
            df_nuevos_alineado[col] = np.nan
            print(f"   ➕ Agregada columna '{col}' con NaN a datos nuevos")
    
    # Análizar NaN DESPUÉS de la alineación
    print(f"\n📋 PASO 4: Análisis NaN DESPUÉS de alineación")
    nan_despues_existente = analizar_valores_nan_profundo(df_existente_alineado, "Existentes Alineados")
    nan_despues_nuevos = analizar_valores_nan_profundo(df_nuevos_alineado, "Nuevos Alineados")
    
    # Identificar columnas donde se introdujeron NaN
    print(f"\n🚨 PASO 5: IDENTIFICACIÓN DE COLUMNAS DONDE SE INTRODUCEN NaN")
    
    # Para datos existentes
    cols_existente_antes = set([info['columna'] for info in nan_antes_existente])
    cols_existente_despues = set([info['columna'] for info in nan_despues_existente])
    nuevas_nan_existente = cols_existente_despues - cols_existente_antes
    
    # Para datos nuevos
    cols_nuevos_antes = set([info['columna'] for info in nan_antes_nuevos])
    cols_nuevos_despues = set([info['columna'] for info in nan_despues_nuevos])
    nuevas_nan_nuevos = cols_nuevos_despues - cols_nuevos_antes
    
    if nuevas_nan_existente:
        print(f"   ⚠️ Columnas con NaN INTRODUCIDOS en datos existentes ({len(nuevas_nan_existente)}):")
        for col in sorted(nuevas_nan_existente):
            print(f"      - {col}")
    
    if nuevas_nan_nuevos:
        print(f"   ⚠️ Columnas con NaN INTRODUCIDOS en datos nuevos ({len(nuevas_nan_nuevos)}):")
        for col in sorted(nuevas_nan_nuevos):
            print(f"      - {col}")
    
    # Simular el concat final
    print(f"\n🔄 PASO 6: Simulación del concat final")
    df_simulado_final = pd.concat([df_existente_alineado, df_nuevos_alineado], ignore_index=True)
    
    print(f"   📊 Resultado simulado:")
    print(f"      Registros finales: {len(df_simulado_final)}")
    print(f"      Columnas finales: {len(df_simulado_final.columns)}")
    
    nan_final = analizar_valores_nan_profundo(df_simulado_final, "Resultado Final Simulado")
    
    return df_simulado_final, nuevas_nan_existente, nuevas_nan_nuevos

def investigar_merge_nan_completo():
    """Investigación completa del problema de NaN en merge"""
    print("🔍 INVESTIGACIÓN COMPLETA: PROBLEMA NaN EN MERGE INCREMENTAL")
    print("=" * 80)
    print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. Cargar datos consolidados existentes
    archivo_consolidado = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado.parquet"
    df_existente = cargar_datos_s3(S3_SILVER_BUCKET, archivo_consolidado, "Datos Consolidados Existentes")
    
    if df_existente.empty:
        print("❌ No se pudieron cargar los datos existentes")
        return
    
    # 2. Cargar datos nuevos (último archivo de origen)
    archivo_nuevo = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-013525_chunk_0.parquet"
    df_nuevos = cargar_datos_s3(S3_BRONZE_BUCKET, archivo_nuevo, "Datos Nuevos (Origen)")
    
    if df_nuevos.empty:
        print("❌ No se pudieron cargar los datos nuevos")
        return
    
    # 3. Análisis comparativo de esquemas
    diferencias_tipos = analizar_esquemas_detallado(df_existente, df_nuevos, "Existentes", "Nuevos")
    
    # 4. Análisis de valores NaN original
    print(f"\n📊 ANÁLISIS DE VALORES NaN EN DATOS ORIGINALES")
    print("=" * 70)
    
    nan_existentes = analizar_valores_nan_profundo(df_existente, "Datos Existentes")
    nan_nuevos = analizar_valores_nan_profundo(df_nuevos, "Datos Nuevos")
    
    # 5. Simulación del proceso problemático
    df_simulado, nan_introducidos_existente, nan_introducidos_nuevos = simular_proceso_merge_problematico(df_existente, df_nuevos)
    
    # 6. Conclusiones y recomendaciones
    print(f"\n🎯 CONCLUSIONES Y DIAGNÓSTICO FINAL")
    print("=" * 70)
    
    print(f"✅ CAUSA RAÍZ IDENTIFICADA:")
    print(f"   1. Los datos existentes y nuevos tienen ESQUEMAS DIFERENTES")
    print(f"   2. El proceso de alineación agrega columnas faltantes con NaN")
    print(f"   3. Esto introduce valores NaN donde antes había datos válidos")
    print()
    
    if diferencias_tipos:
        print(f"⚠️ TIPOS DE DATOS INCONSISTENTES: {len(diferencias_tipos)} columnas")
        print(f"   - Esto puede causar conversiones problemáticas durante el merge")
        print()
    
    total_nan_introducidos = len(nan_introducidos_existente) + len(nan_introducidos_nuevos)
    if total_nan_introducidos > 0:
        print(f"🚨 NaN INTRODUCIDOS DURANTE ALINEACIÓN: {total_nan_introducidos} columnas")
        print(f"   - En datos existentes: {len(nan_introducidos_existente)} columnas")
        print(f"   - En datos nuevos: {len(nan_introducidos_nuevos)} columnas")
        print()
    
    print(f"💡 RECOMENDACIONES PARA SOLUCIÓN:")
    print(f"   1. Implementar validación de esquema antes del merge")
    print(f"   2. Estandarizar estructura entre datos existentes y nuevos")
    print(f"   3. Evitar alineación automática que introduce NaN")
    print(f"   4. Usar estrategia de merge que preserve datos originales")
    print()
    
    print(f"✅ INVESTIGACIÓN COMPLETADA")
    print(f"⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    investigar_merge_nan_completo()
