#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para probar la consolidación con la corrección de normalización
"""

import os
import sys
import pandas as pd
import boto3
import logging
from datetime import datetime

# Configurar ruta para imports
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Suprimir warnings
import warnings
warnings.filterwarnings('ignore')

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Import del sistema de consolidación
from app_raw_consolidado import (
    leer_archivo_resumen,
    agrupar_archivos_por_tabla,
    consolidar_archivos_tabla_incremental
)

def test_consolidacion_corregida():
    """Prueba la consolidación con la corrección aplicada"""
    
    print("\n" + "="*80)
    print("🧪 PRUEBA DE CONSOLIDACIÓN CON CORRECCIÓN APLICADA")
    print("="*80)
    
    tabla_especifica = "MTX_WALLET_ORA"
    tabla_path = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA"
    seccion_img = "IMG_FLOW_42"
    
    print(f"📋 Tabla a procesar: {tabla_especifica}")
    print(f"⏰ Iniciando prueba: {datetime.now()}")
    
    try:
        # 1. Leer archivo de resumen
        print(f"\n🔄 Leyendo archivo de resumen...")
        df_resumen = leer_archivo_resumen(seccion_img)
        
        if df_resumen.empty:
            print(f"❌ No se pudo leer archivo de resumen")
            return
        
        print(f"✅ Resumen cargado: {len(df_resumen)} registros")
        
        # 2. Agrupar archivos por tabla
        print(f"\n🔄 Agrupando archivos por tabla...")
        grupos_archivos = agrupar_archivos_por_tabla(df_resumen)
        
        if tabla_path not in grupos_archivos:
            print(f"❌ Tabla {tabla_path} no encontrada en resumen")
            print(f"Tablas disponibles: {list(grupos_archivos.keys())}")
            return
        
        archivos_mtx = grupos_archivos[tabla_path]
        print(f"✅ Encontrados {len(archivos_mtx)} archivos para {tabla_especifica}")
        
        # 3. Ejecutar consolidación 
        print(f"\n🔄 Ejecutando consolidación incremental...")
        resultado = consolidar_archivos_tabla_incremental(tabla_path, archivos_mtx)
        
        if not resultado.empty:
            print(f"✅ Consolidación exitosa!")
            print(f"📊 Registros consolidados: {len(resultado)}")
            print(f"📋 Columnas: {len(resultado.columns)}")
            
            # Ahora vamos a comparar nuevamente para ver si se arregló
            print(f"\n🔍 Verificando si la corrección funcionó...")
            
            # Verificar valores en columnas críticas que antes se perdían
            columnas_criticas = ['PAYMENT_METHOD_TYPE_ID', 'USER_GRADE', 'USER_TYPE']
            
            for col in columnas_criticas:
                if col in resultado.columns:
                    valores_unicos = resultado[col].nunique()
                    valores_vacios = (resultado[col] == '').sum()
                    total = len(resultado)
                    
                    print(f"   📊 {col}:")
                    print(f"      Valores únicos: {valores_unicos}")
                    print(f"      Valores vacíos: {valores_vacios}/{total} ({(valores_vacios/total)*100:.1f}%)")
                    
                    if valores_vacios == total:
                        print(f"      ❌ TODOS los valores están vacíos - corrección falló")
                    elif valores_vacios < total * 0.5:
                        print(f"      ✅ Mayoría de valores preservados - corrección exitosa")
                    else:
                        print(f"      ⚠️ Algunos valores preservados - corrección parcial")
                    
                    # Mostrar ejemplos de valores
                    if valores_unicos > 0:
                        ejemplos = resultado[col].value_counts().head(3)
                        print(f"      Ejemplos de valores: {dict(ejemplos)}")
            
        else:
            print(f"❌ Consolidación resultó vacía")
        
    except Exception as e:
        print(f"❌ Error durante la prueba: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_consolidacion_corregida()
