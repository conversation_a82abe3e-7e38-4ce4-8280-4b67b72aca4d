#!/usr/bin/env python3
"""
Script de validación final para verificar que la corrección de NaN a string 'nan' está funcionando.
Prueba la función alinear_tipos_datos_puro corregida.
"""

import pandas as pd
import numpy as np
import sys
import os

# Agregar el directorio actual al path para importar la función
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar la función corregida
from app_raw_consolidado_puro import alinear_tipos_datos_puro

def test_correccion_nan():
    """Prueba que la función corregida no convierte NaN a 'nan' strings"""
    
    print("🔍 VALIDACIÓN FINAL - Corrección NaN a String")
    print("=" * 60)
    
    # Crear DataFrames de prueba que replican el problema original
    df_existente = pd.DataFrame({
        'ISSUER_ID': ['VALID_1', 'VALID_2', 'VALID_3'],
        'AMOUNT': [100.0, 200.0, 300.0],
        'STATUS': ['ACTIVE', 'PENDING', 'COMPLETE']
    })
    
    # DataFrame nuevo con NaN en ISSUER_ID (diferente tipo de datos)
    df_nuevos = pd.DataFrame({
        'ISSUER_ID': [np.nan, 'NEW_ID_1', np.nan],  # float64 con NaN
        'AMOUNT': [150, 250, 350],  # int64 
        'STATUS': ['ACTIVE', 'PENDING', 'CANCELLED']
    })
    
    print("📊 DATOS DE ENTRADA:")
    print("\ndf_existente:")
    print(f"  ISSUER_ID dtype: {df_existente['ISSUER_ID'].dtype}")
    print(f"  NaN count: {df_existente['ISSUER_ID'].isna().sum()}")
    print(f"  Values: {df_existente['ISSUER_ID'].tolist()}")
    
    print("\ndf_nuevos:")
    print(f"  ISSUER_ID dtype: {df_nuevos['ISSUER_ID'].dtype}")
    print(f"  NaN count: {df_nuevos['ISSUER_ID'].isna().sum()}")
    print(f"  Values: {df_nuevos['ISSUER_ID'].tolist()}")
    
    # Aplicar la función corregida
    print("\n🔧 APLICANDO FUNCIÓN CORREGIDA...")
    df_existente_aligned, df_nuevos_aligned = alinear_tipos_datos_puro(df_existente, df_nuevos)
    
    print("\n📈 RESULTADOS DESPUÉS DE ALINEACIÓN:")
    print("\ndf_existente_aligned:")
    print(f"  ISSUER_ID dtype: {df_existente_aligned['ISSUER_ID'].dtype}")
    print(f"  NaN count: {df_existente_aligned['ISSUER_ID'].isna().sum()}")
    print(f"  Values: {df_existente_aligned['ISSUER_ID'].tolist()}")
    
    print("\ndf_nuevos_aligned:")
    print(f"  ISSUER_ID dtype: {df_nuevos_aligned['ISSUER_ID'].dtype}")
    print(f"  NaN count: {df_nuevos_aligned['ISSUER_ID'].isna().sum()}")
    print(f"  Values: {df_nuevos_aligned['ISSUER_ID'].tolist()}")
    
    # Verificar que no hay strings 'nan'
    def contar_string_nan(series):
        """Cuenta ocurrencias de string 'nan' en una serie"""
        if series.dtype == 'object':
            return (series == 'nan').sum()
        return 0
    
    string_nan_existente = contar_string_nan(df_existente_aligned['ISSUER_ID'])
    string_nan_nuevos = contar_string_nan(df_nuevos_aligned['ISSUER_ID'])
    
    print(f"\n🎯 VERIFICACIÓN CRÍTICA:")
    print(f"  String 'nan' en df_existente: {string_nan_existente}")
    print(f"  String 'nan' en df_nuevos: {string_nan_nuevos}")
    print(f"  Total string 'nan': {string_nan_existente + string_nan_nuevos}")
    
    # Prueba de concatenación (simula el pd.concat final)
    print(f"\n🔗 PRUEBA DE CONCATENACIÓN:")
    df_concatenado = pd.concat([df_existente_aligned, df_nuevos_aligned], ignore_index=True)
    print(f"  ISSUER_ID dtype después de concat: {df_concatenado['ISSUER_ID'].dtype}")
    print(f"  NaN count después de concat: {df_concatenado['ISSUER_ID'].isna().sum()}")
    print(f"  String 'nan' después de concat: {contar_string_nan(df_concatenado['ISSUER_ID'])}")
    print(f"  Values: {df_concatenado['ISSUER_ID'].tolist()}")
    
    # Resultado final
    total_string_nan_final = contar_string_nan(df_concatenado['ISSUER_ID'])
    
    print(f"\n{'='*60}")
    if total_string_nan_final == 0:
        print("✅ ÉXITO: La corrección funciona correctamente!")
        print("   No se encontraron conversiones de NaN a string 'nan'")
        print("   Los datos se mantienen 'como dos gotas de agua' 💧💧")
        return True
    else:
        print("❌ FALLO: Aún hay conversiones de NaN a string 'nan'")
        print(f"   Se encontraron {total_string_nan_final} strings 'nan'")
        return False

if __name__ == "__main__":
    test_correccion_nan()
