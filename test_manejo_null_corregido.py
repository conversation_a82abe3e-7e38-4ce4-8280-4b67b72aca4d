#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test específico para verificar el manejo correcto de valores NULL/None
"""

import pandas as pd
import numpy as np
import sys

# Configurar ruta para imports
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

from app_raw_consolidado import normalizar_tipos_datos

def test_manejo_null():
    """
    Prueba específica para el manejo de valores NULL/None
    """
    
    print("🧪 TEST ESPECÍFICO: MANEJO CORRECTO DE NULL/None")
    print("="*60)
    
    # Test 1: Simular el caso real de la imagen
    print("\n1. 📊 TEST: Caso real de la imagen (DESCRIPTION con NULL)")
    
    datos_reales = {
        'USER_GRADE': ['OPT', 'OPT', 'OPT', None, None],  # NaN reales
        'DESCRIPTION': ['Expense Account', 'Expense Account', 'Expense Account', None, None],  # NULL reales
        'WALLET_REF_ID': [None, None, None, '520', None],  # Mixto
        'ACCOUNT_GROUP_ID': [None, None, None, None, None]  # Todo NULL
    }
    
    df_real = pd.DataFrame(datos_reales)
    print("   📥 Datos de entrada (simulando archivo Parquet):")
    for col in df_real.columns:
        valores = df_real[col].tolist()
        null_count = df_real[col].isnull().sum()
        print(f"      {col}: {valores} ({null_count} NULLs)")
    
    df_procesado = normalizar_tipos_datos(df_real.copy())
    
    print(f"\n   📤 Datos después de normalización:")
    for col in df_procesado.columns:
        valores = df_procesado[col].tolist()
        empty_count = (df_procesado[col] == '').sum()
        none_count = (df_procesado[col] == 'None').sum()
        print(f"      {col}: {valores}")
        print(f"         Cadenas vacías: {empty_count}, 'None' literales: {none_count}")
    
    # Verificar que NULL se convierte a cadena vacía, NO a 'None'
    print(f"\n   🔍 VERIFICACIÓN:")
    problemas = []
    for col in df_procesado.columns:
        none_literales = (df_procesado[col] == 'None').sum()
        if none_literales > 0:
            problemas.append(f"{col} tiene {none_literales} valores 'None' literales")
    
    if problemas:
        print(f"   ❌ PROBLEMAS DETECTADOS:")
        for problema in problemas:
            print(f"      • {problema}")
    else:
        print(f"   ✅ Perfecto: Todos los NULL se convirtieron a cadenas vacías")
    
    # Test 2: Valores 'None' que SÍ son datos válidos
    print(f"\n2. 📊 TEST: Valores 'None' como datos válidos")
    
    datos_validos = {
        'categoria_especial': ['Type1', 'Type2', 'None', 'Type3', 'Type4'],  # 'None' es válido
        'status_field': ['Active', 'None', 'Inactive', 'None', 'Pending']   # 'None' es válido
    }
    
    df_validos = pd.DataFrame(datos_validos)
    df_validos_procesado = normalizar_tipos_datos(df_validos.copy())
    
    print("   📥 Datos de entrada:")
    for col in df_validos.columns:
        print(f"      {col}: {df_validos[col].tolist()}")
    
    print(f"\n   📤 Datos después de normalización:")
    for col in df_validos_procesado.columns:
        print(f"      {col}: {df_validos_procesado[col].tolist()}")
    
    # Verificar preservación de 'None' válidos
    print(f"\n   🔍 VERIFICACIÓN:")
    for col in df_validos.columns:
        original_nones = (df_validos[col] == 'None').sum()
        procesado_nones = (df_validos_procesado[col] == 'None').sum()
        
        if original_nones == procesado_nones and original_nones > 0:
            print(f"   ✅ {col}: 'None' válidos preservados ({original_nones} valores)")
        elif original_nones > procesado_nones:
            print(f"   ⚠️  {col}: Algunos 'None' válidos eliminados ({original_nones} → {procesado_nones})")
        else:
            print(f"   ✅ {col}: Sin 'None' válidos para preservar")
    
    # Test 3: Campo DESCRIPTION específico (del caso real)
    print(f"\n3. 📊 TEST: Campo DESCRIPTION específico")
    
    description_test = {
        'DESCRIPTION': ['Expense Account', 'Expense Account', 'Expense Account', None, None, 'Transfer', None]
    }
    
    df_desc = pd.DataFrame(description_test)
    df_desc_procesado = normalizar_tipos_datos(df_desc.copy())
    
    print("   📥 DESCRIPTION original:")
    print(f"      Valores: {df_desc['DESCRIPTION'].tolist()}")
    print(f"      NULLs reales: {df_desc['DESCRIPTION'].isnull().sum()}")
    
    print(f"\n   📤 DESCRIPTION procesado:")
    print(f"      Valores: {df_desc_procesado['DESCRIPTION'].tolist()}")
    empty_count = (df_desc_procesado['DESCRIPTION'] == '').sum()
    none_count = (df_desc_procesado['DESCRIPTION'] == 'None').sum()
    print(f"      Cadenas vacías: {empty_count}")
    print(f"      'None' literales: {none_count}")
    
    if none_count == 0 and empty_count > 0:
        print(f"   ✅ PERFECTO: NULLs convertidos a cadenas vacías correctamente")
    else:
        print(f"   ❌ PROBLEMA: Aún hay conversión incorrecta de NULL")
    
    print(f"\n" + "="*60)
    print(f"📋 RESUMEN DEL TEST NULL/None")
    print(f"="*60)
    
    if not problemas and none_count == 0:
        print(f"✅ ÉXITO: Manejo de NULL corregido")
        print(f"   • NULLs reales → cadenas vacías ✓")
        print(f"   • 'None' válidos → preservados ✓") 
        print(f"   • No hay 'None' literales incorrectos ✓")
        
        print(f"\n🎯 LISTO PARA RECONSOLIDAR:")
        print(f"   python3 app_raw_consolidado.py IMG_FLOW_42")
    else:
        print(f"❌ AÚN HAY PROBLEMAS:")
        print(f"   • Revisar lógica de manejo de NULL")

if __name__ == "__main__":
    test_manejo_null()
