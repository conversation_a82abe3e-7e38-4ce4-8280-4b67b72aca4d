#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import sys
import warnings

warnings.filterwarnings('ignore')

# Importar la función que vamos a probar
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')
from app_raw_consolidado_puro import preservar_tipos_originales

def test_preservacion_estructura():
    """
    Prueba que la versión pura preserve la estructura original.
    """
    print("🧪 PROBANDO PRESERVACIÓN DE ESTRUCTURA ORIGINAL")
    print("=" * 60)
    
    # Crear DataFrame de prueba con tipos diversos como en Bronze
    datos_bronze = {
        'USER_GRADE': ['OPT', 'WALLET', None, '<null>', 'OPT'],  # object con NULLs originales
        'PAYMENT_TYPE_ID': [1, 2, None, 4, 5],  # int64 con NULL
        'ISSUER_ID': [200.0, 201.5, np.nan, 203.0, 204.0],  # float64 con NaN
        'CREATED_ON': pd.to_datetime(['2001-01-01', '2001-01-02', None, '2001-01-04', '2001-01-05']),  # datetime
        'DESCRIPTION': ['Account A', None, '<null>', 'Account D', ''],  # object con variedad de nulls
        'WALLET_REF_ID': ['W123', 'W124', None, 'W126', 'W127'],  # object normal
        'BALANCE': [100.50, 200.75, np.inf, 400.25, 500.00]  # float con infinito
    }
    
    df_original = pd.DataFrame(datos_bronze)
    
    print("📊 DATOS ORIGINALES (Bronze Zone):")
    print(df_original)
    print(f"\nTipos de datos originales:")
    print(df_original.dtypes)
    print()
    
    # Aplicar preservación (versión pura)
    df_preservado = preservar_tipos_originales(df_original)
    
    print("🔧 DATOS DESPUÉS DE PRESERVACIÓN:")
    print(df_preservado)
    print(f"\nTipos de datos preservados:")
    print(df_preservado.dtypes)
    print()
    
    # Verificaciones de preservación
    print("🔍 VERIFICACIONES DE PRESERVACIÓN:")
    print("-" * 40)
    
    # 1. Verificar que los valores categóricos se mantuvieron EXACTAMENTE
    print("📋 VALORES CATEGÓRICOS PRESERVADOS:")
    user_grade_original = set(df_original['USER_GRADE'].dropna().astype(str))
    user_grade_preservado = set(df_preservado['USER_GRADE'].dropna().astype(str))
    print(f"   USER_GRADE original: {user_grade_original}")
    print(f"   USER_GRADE preservado: {user_grade_preservado}")
    
    if user_grade_original == user_grade_preservado:
        print("   ✅ USER_GRADE: Valores categóricos preservados exactamente")
    else:
        print("   ❌ USER_GRADE: Valores categóricos CAMBIARON")
    
    # 2. Verificar que los NULLs se mantuvieron como estaban
    print("\n📊 PRESERVACIÓN DE NULLs ORIGINALES:")
    for col in ['USER_GRADE', 'DESCRIPTION', 'WALLET_REF_ID']:
        if col in df_preservado.columns:
            null_count_original = df_original[col].isna().sum()
            null_count_preservado = df_preservado[col].isna().sum()
            
            # En versión pura, los NULLs pueden convertirse a string pero NO deben limpiarse agresivamente
            valores_unicos_preservados = df_preservado[col].unique()
            
            print(f"   {col}:")
            print(f"      NULLs originales: {null_count_original}")
            print(f"      NULLs preservados: {null_count_preservado}")
            print(f"      Valores únicos: {valores_unicos_preservados}")
            
            # Verificar que '<null>' se preservó si existía
            if '<null>' in df_original[col].astype(str).values:
                if '<null>' in df_preservado[col].astype(str).values:
                    print(f"      ✅ '<null>' preservado como estaba en origen")
                else:
                    print(f"      ❌ '<null>' fue transformado (NO debería en versión pura)")
    
    # 3. Verificar que solo se agregaron metadatos
    print("\n📈 METADATOS AGREGADOS:")
    columnas_nuevas = set(df_preservado.columns) - set(df_original.columns)
    print(f"   Columnas agregadas: {columnas_nuevas}")
    
    metadatos_esperados = {'data_lake_consolidated_timestamp', 'data_lake_consolidation_version'}
    if columnas_nuevas == metadatos_esperados:
        print("   ✅ Solo metadatos básicos agregados")
    else:
        print(f"   ⚠️  Metadatos inesperados: {columnas_nuevas}")
    
    # 4. Verificar que no hay transformaciones agresivas
    print("\n🎯 VERIFICACIÓN DE NO-TRANSFORMACIÓN:")
    
    # Verificar que infinitos fueron manejados mínimamente
    infinitos_originales = np.isinf(df_original['BALANCE']).sum()
    infinitos_preservados = np.isinf(df_preservado['BALANCE']).sum() if df_preservado['BALANCE'].dtype in ['float64', 'float32'] else 0
    
    print(f"   Infinitos originales: {infinitos_originales}")
    print(f"   Infinitos preservados: {infinitos_preservados}")
    
    if infinitos_preservados == 0 and infinitos_originales > 0:
        print("   ✅ Infinitos manejados correctamente (convertidos a NaN)")
    
    # 5. Verificar timestamps
    print(f"\n📅 MANEJO DE TIMESTAMPS:")
    created_on_tipo_original = df_original['CREATED_ON'].dtype
    created_on_tipo_preservado = df_preservado['CREATED_ON'].dtype
    
    print(f"   Tipo original: {created_on_tipo_original}")
    print(f"   Tipo preservado: {created_on_tipo_preservado}")
    
    if 'datetime' in str(created_on_tipo_original) and df_preservado['CREATED_ON'].dtype == 'object':
        print("   ✅ Timestamp convertido a string para compatibilidad Parquet")
    
    print()
    print("🎯 RESUMEN DE LA PRUEBA:")
    print("=" * 60)
    
    # Verificación final
    preservacion_exitosa = True
    
    # Verificar que valores categóricos no se perdieron
    if user_grade_original != user_grade_preservado:
        preservacion_exitosa = False
        print("❌ FALLO: Valores categóricos cambiaron")
    
    # Verificar que hay metadatos
    if not columnas_nuevas:
        preservacion_exitosa = False
        print("❌ FALLO: No se agregaron metadatos")
    
    if preservacion_exitosa:
        print("✅ ÉXITO: Estructura original preservada correctamente")
        print("✅ Valores categóricos intactos (OPT, WALLET, etc.)")
        print("✅ NULLs preservados como en origen")
        print("✅ Solo transformaciones mínimas para compatibilidad")
        print("✅ Sistema listo para consolidación PURA")
    else:
        print("❌ FALLO: La preservación no funciona correctamente")
    
    return preservacion_exitosa

if __name__ == "__main__":
    exito = test_preservacion_estructura()
    
    if exito:
        print("\n🚀 VERSIÓN PURA LISTA PARA USO EN PRODUCCIÓN")
        print("📄 Estructura Bronze preservada → Silver consolidado")
        sys.exit(0)
    else:
        print("\n⚠️  NECESITA AJUSTES EN LA PRESERVACIÓN")
        sys.exit(1)
