#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Análisis específico del segundo archivo Bronze para identificar el origen de los 43 NaN
====================================================================================

OBJETIVO: Cargar y analizar el segundo archivo Bronze encontrado para entender
por qué el proceso de consolidación introduce exactamente 43 NaN adicionales.

Archivos identificados:
1. PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040917_chunk_0.parquet
2. PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2025/06/02/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040552_chunk_0.parquet
"""

import boto3
import pandas as pd
import numpy as np
import logging
from botocore.exceptions import ClientError

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Constantes
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"

def leer_parquet_desde_s3(bucket: str, key: str) -> pd.DataFrame:
    """Lee un archivo Parquet desde S3."""
    try:
        s3_path = f"s3://{bucket}/{key}"
        df = pd.read_parquet(s3_path)
        logging.info(f"✅ Archivo leído desde S3: {s3_path} ({len(df)} registros, {len(df.columns)} columnas)")
        return df
    except Exception as e:
        logging.error(f"❌ Error leyendo {bucket}/{key}: {str(e)}")
        return pd.DataFrame()

def contar_nan_detallado(df: pd.DataFrame, nombre_archivo: str) -> dict:
    """Cuenta NaN values por columna con estadísticas detalladas."""
    nan_info = {}
    total_nan = 0
    
    print(f"\n📊 === ANÁLISIS DETALLADO NaN EN {nombre_archivo} ===")
    print(f"Total registros: {len(df)}")
    print(f"Total columnas: {len(df.columns)}")
    
    # Contar NaN por columna
    for col in df.columns:
        nan_count = df[col].isna().sum()
        non_null_count = df[col].notna().sum()
        percentage = (nan_count / len(df)) * 100 if len(df) > 0 else 0
        
        nan_info[col] = {
            'nan_count': nan_count,
            'non_null_count': non_null_count,
            'percentage': percentage,
            'dtype': str(df[col].dtype)
        }
        
        if nan_count > 0:
            total_nan += nan_count
            print(f"   {col}: {nan_count} NaN ({percentage:.1f}%) | dtype: {df[col].dtype}")
    
    print(f"📈 Total NaN values: {total_nan}")
    return nan_info

def comparar_dataframes(df1: pd.DataFrame, df2: pd.DataFrame, nombre1: str, nombre2: str):
    """Compara dos DataFrames identificando diferencias específicas."""
    print(f"\n🔍 === COMPARACIÓN DETALLADA: {nombre1} vs {nombre2} ===")
    
    # Comparar esquemas
    cols1 = set(df1.columns)
    cols2 = set(df2.columns)
    
    print(f"Columnas en {nombre1}: {len(cols1)}")
    print(f"Columnas en {nombre2}: {len(cols2)}")
    print(f"Columnas comunes: {len(cols1 & cols2)}")
    
    # Columnas diferentes
    only_1 = cols1 - cols2
    only_2 = cols2 - cols1
    
    if only_1:
        print(f"Solo en {nombre1}: {list(only_1)}")
    if only_2:
        print(f"Solo en {nombre2}: {list(only_2)}")
    
    # Si hay columnas en común, comparar tipos
    common_cols = cols1 & cols2
    if common_cols:
        print(f"\n🔧 Comparación de tipos de datos:")
        for col in sorted(common_cols):
            dtype1 = df1[col].dtype
            dtype2 = df2[col].dtype
            if dtype1 != dtype2:
                print(f"   ⚠️ {col}: {dtype1} → {dtype2}")
    
    return common_cols, only_1, only_2

def simular_consolidacion_manual(df1: pd.DataFrame, df2: pd.DataFrame) -> pd.DataFrame:
    """Simula manualmente el proceso de consolidación para identificar dónde se introducen los NaN."""
    print(f"\n🧪 === SIMULACIÓN MANUAL DE CONSOLIDACIÓN ===")
    
    print(f"Input DF1: {len(df1)} registros, {len(df1.columns)} columnas")
    print(f"Input DF2: {len(df2)} registros, {len(df2.columns)} columnas")
    
    # Verificar si las columnas son exactamente las mismas
    if set(df1.columns) != set(df2.columns):
        print(f"⚠️ PROBLEMA: Los DataFrames tienen esquemas diferentes")
        print(f"   Columnas DF1: {set(df1.columns)}")
        print(f"   Columnas DF2: {set(df2.columns)}")
        print(f"   Diferencia: {set(df1.columns) ^ set(df2.columns)}")
    
    # Consolidar usando concat (como hace el sistema)
    print(f"\n🔀 Consolidando con pd.concat...")
    try:
        # Intentar diferentes métodos de concat
        
        # Método 1: concat normal
        df_concat_normal = pd.concat([df1, df2], ignore_index=True)
        nan_count_normal = df_concat_normal.isna().sum().sum()
        print(f"   Método 1 (normal): {len(df_concat_normal)} registros, {nan_count_normal} NaN totales")
        
        # Método 2: concat con sort=True
        df_concat_sort = pd.concat([df1, df2], ignore_index=True, sort=True)
        nan_count_sort = df_concat_sort.isna().sum().sum()
        print(f"   Método 2 (sort=True): {len(df_concat_sort)} registros, {nan_count_sort} NaN totales")
        
        # Método 3: concat con sort=False
        df_concat_nosort = pd.concat([df1, df2], ignore_index=True, sort=False)
        nan_count_nosort = df_concat_nosort.isna().sum().sum()
        print(f"   Método 3 (sort=False): {len(df_concat_nosort)} registros, {nan_count_nosort} NaN totales")
        
        return df_concat_normal
        
    except Exception as e:
        print(f"❌ Error en consolidación: {str(e)}")
        return pd.DataFrame()

def main():
    """Función principal para análisis específico del segundo archivo."""
    print("🔬 === ANÁLISIS ESPECÍFICO DEL SEGUNDO ARCHIVO BRONZE ===")
    print("Objetivo: Identificar exactamente dónde se introducen los 43 NaN\n")
    
    # Rutas exactas de los archivos encontrados
    archivo_1 = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040917_chunk_0.parquet"
    archivo_2 = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2025/06/02/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040552_chunk_0.parquet"
    archivo_silver = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    
    # 1. Cargar primer archivo Bronze
    print("1️⃣ Cargando primer archivo Bronze...")
    df_bronze1 = leer_parquet_desde_s3(S3_BRONZE_BUCKET, archivo_1)
    if not df_bronze1.empty:
        nan_info_1 = contar_nan_detallado(df_bronze1, "BRONZE ARCHIVO 1")
    
    # 2. Cargar segundo archivo Bronze
    print("\n2️⃣ Cargando segundo archivo Bronze...")
    df_bronze2 = leer_parquet_desde_s3(S3_BRONZE_BUCKET, archivo_2)
    if not df_bronze2.empty:
        nan_info_2 = contar_nan_detallado(df_bronze2, "BRONZE ARCHIVO 2")
    
    # 3. Cargar archivo Silver
    print("\n3️⃣ Cargando archivo Silver consolidado...")
    df_silver = leer_parquet_desde_s3(S3_SILVER_BUCKET, archivo_silver)
    if not df_silver.empty:
        nan_info_silver = contar_nan_detallado(df_silver, "SILVER CONSOLIDADO")
    
    # 4. Comparar esquemas entre archivos Bronze
    if not df_bronze1.empty and not df_bronze2.empty:
        common_cols, only_1, only_2 = comparar_dataframes(df_bronze1, df_bronze2, "Bronze1", "Bronze2")
        
        # 5. Simular consolidación manual
        df_simulado = simular_consolidacion_manual(df_bronze1, df_bronze2)
        if not df_simulado.empty:
            nan_info_simulado = contar_nan_detallado(df_simulado, "SIMULACIÓN MANUAL")
    
    # 6. Análisis de totales
    print(f"\n📊 === ANÁLISIS DE TOTALES ===")
    
    total_bronze1 = sum(info['nan_count'] for info in nan_info_1.values()) if not df_bronze1.empty else 0
    total_bronze2 = sum(info['nan_count'] for info in nan_info_2.values()) if not df_bronze2.empty else 0
    total_silver = sum(info['nan_count'] for info in nan_info_silver.values()) if not df_silver.empty else 0
    total_simulado = sum(info['nan_count'] for info in nan_info_simulado.values()) if 'nan_info_simulado' in locals() else 0
    
    print(f"NaN en Bronze1: {total_bronze1}")
    print(f"NaN en Bronze2: {total_bronze2}")
    print(f"NaN esperados (Bronze1 + Bronze2): {total_bronze1 + total_bronze2}")
    print(f"NaN en simulación manual: {total_simulado}")
    print(f"NaN en Silver real: {total_silver}")
    print(f"Diferencia simulación vs real: {total_silver - total_simulado}")
    
    # 7. Identificar diferencias específicas por columna
    if not df_bronze1.empty and not df_bronze2.empty and not df_silver.empty:
        print(f"\n🔍 === ANÁLISIS POR COLUMNA ===")
        
        all_columns = set(df_bronze1.columns) | set(df_bronze2.columns) | set(df_silver.columns)
        
        for col in sorted(all_columns):
            nan1 = nan_info_1.get(col, {}).get('nan_count', 0)
            nan2 = nan_info_2.get(col, {}).get('nan_count', 0)
            nan_silver = nan_info_silver.get(col, {}).get('nan_count', 0)
            esperado = nan1 + nan2
            diferencia = nan_silver - esperado
            
            if diferencia != 0:
                print(f"   ⚠️ {col}: Bronze1({nan1}) + Bronze2({nan2}) = {esperado} → Silver({nan_silver}) [Δ{diferencia:+d}]")
    
    # 8. Conclusiones
    print(f"\n💡 === CONCLUSIONES ===")
    print(f"✅ Análisis específico completado")
    print(f"🔍 Se ha identificado exactamente dónde se introducen los NaN adicionales")
    if total_silver > (total_bronze1 + total_bronze2):
        print(f"🚨 CONFIRMADO: El proceso de consolidación introduce {total_silver - (total_bronze1 + total_bronze2)} NaN adicionales")
        print(f"📍 Investigar específicamente la función de consolidación en app_raw_consolidado_puro.py")

if __name__ == "__main__":
    main()
