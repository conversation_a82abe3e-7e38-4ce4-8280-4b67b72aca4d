#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para depurar la normalización de tipos específicamente
"""

import os
import sys
import pandas as pd
import boto3
import logging

# Configurar ruta para imports
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Configurar logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

from app_raw_consolidado import leer_parquet_desde_s3

def debug_normalizacion():
    """Depura específicamente qué pasa con la normalización"""
    
    print("\n" + "="*80)
    print("🔍 DEBUG: NORMALIZACIÓN DE TIPOS")
    print("="*80)
    
    bucket = "prd-datalake-bronze-zone-637423440311"
    archivo = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250530-164129_chunk_0.parquet"
    
    print(f"📁 Cargando archivo original: {archivo}")
    
    df = leer_parquet_desde_s3(bucket, archivo)
    
    if df.empty:
        print("❌ No se pudo cargar el archivo")
        return
    
    print(f"✅ Archivo cargado: {len(df)} registros, {len(df.columns)} columnas")
    
    # Analizar columnas problemáticas
    columnas_problema = ['PAYMENT_METHOD_TYPE_ID', 'USER_GRADE', 'USER_TYPE']
    
    for col in columnas_problema:
        if col in df.columns:
            print(f"\n🔍 ANALIZANDO COLUMNA: {col}")
            print(f"   Tipo original: {df[col].dtype}")
            
            # Valores únicos y sus frecuencias
            valores = df[col].value_counts()
            print(f"   Valores únicos: {len(valores)}")
            print(f"   Valores más frecuentes:")
            for valor, count in valores.head(10).items():
                print(f"      '{valor}' -> {count} veces")
            
            # Verificar si alguno de estos valores sería detectado como timestamp
            sample_values = df[col].dropna().head(20)
            print(f"   Muestra de 20 valores:")
            for i, val in enumerate(sample_values):
                print(f"      {i+1}. '{val}' (tipo: {type(val)})")
                
                # Simular la lógica de detección de timestamp
                if isinstance(val, str):
                    val_clean = str(val).strip()
                    
                    criterios = []
                    
                    # Criterio 1: Contiene 'T'
                    if 'T' in val_clean:
                        criterios.append("contiene_T")
                    
                    # Criterio 2: Longitud >= 10
                    if len(val_clean) >= 10:
                        criterios.append("longitud_10+")
                    
                    # Criterio 3: Formato fecha con guiones y dos puntos
                    if val_clean.count('-') >= 2 and ':' in val_clean:
                        criterios.append("fecha_guion_dos_puntos")
                    
                    # Criterio 4: Formato fecha con barras y dos puntos
                    if val_clean.count('/') >= 2 and ':' in val_clean:
                        criterios.append("fecha_barra_dos_puntos")
                    
                    # Criterio 5: Solo fecha YYYY-MM-DD
                    if (len(val_clean) == 10 and val_clean.count('-') == 2 and 
                        val_clean[:4].isdigit() and val_clean[5:7].isdigit() and val_clean[8:10].isdigit()):
                        criterios.append("formato_YYYY_MM_DD")
                    
                    if criterios:
                        print(f"         ⚠️ DETECTADO COMO POSIBLE TIMESTAMP: {criterios}")
                    else:
                        print(f"         ✅ NO detectado como timestamp")

if __name__ == "__main__":
    debug_normalizacion()
