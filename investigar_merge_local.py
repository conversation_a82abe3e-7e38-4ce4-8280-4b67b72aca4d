#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 INVESTIGACIÓN LOCAL: Problema NaN en Merge
=============================================

Versión simplificada que usa las mismas funciones del sistema principal
para investigar el problema de NaN durante merge incremental.

Fecha: 2025-06-02
"""

import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Agregar ruta del sistema
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def analizar_merge_nan():
    """
    Analiza el problema de NaN durante el merge usando las funciones existentes.
    """
    print("🔍 INVESTIGACIÓN MERGE NaN - VERSIÓN LOCAL")
    print("="*50)
    print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Importar las funciones del sistema principal
        from app_raw_consolidado_puro import (
            leer_archivo_resumen,
            agrupar_archivos_por_tabla,
            consolidar_archivos_tabla_incremental_puro,
            leer_parquet_desde_s3
        )
        
        print("\n📋 PASO 1: Cargar resumen de archivos...")
        
        # Cargar resumen para IMG_FLOW_42 (donde está MTX_WALLET_ORA)
        resumen_df = leer_archivo_resumen('IMG_FLOW_42')
        if resumen_df.empty:
            print("❌ No se pudo cargar el resumen")
            return
            
        print(f"✅ Resumen cargado: {len(resumen_df)} registros")
        print(f"📋 Columnas del resumen: {list(resumen_df.columns)}")
        
        # Mostrar el contenido del resumen para diagnóstico
        print(f"📄 Contenido del resumen:")
        print(resumen_df.to_string(index=False))
        
        # Filtrar para MTX_WALLET_ORA (ajustar nombre de columna)
        if 'tabla_nombre' in resumen_df.columns:
            mtx_wallet_files = resumen_df[resumen_df['tabla_nombre'] == 'MTX_WALLET_ORA'].copy()
        elif 'tabla' in resumen_df.columns:
            mtx_wallet_files = resumen_df[resumen_df['tabla'] == 'MTX_WALLET_ORA'].copy()
        else:
            print("❌ No se encontró columna de tabla en el resumen")
            return
            
        print(f"📊 Archivos MTX_WALLET_ORA encontrados: {len(mtx_wallet_files)}")
        
        if len(mtx_wallet_files) == 0:
            print("❌ No se encontraron archivos MTX_WALLET_ORA")
            return
            
        print("\n📋 PASO 2: Agrupar archivos por tabla...")
        archivos_agrupados = agrupar_archivos_por_tabla(resumen_df)
        
        print(f"📋 Claves disponibles en archivos agrupados: {list(archivos_agrupados.keys())}")
        
        # Buscar MTX_WALLET_ORA con diferentes variaciones de clave
        tabla_key = None
        posibles_keys = ['MTX_WALLET_ORA', 'PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA', 'MTX_WALLET_ORA']
        
        for key in posibles_keys:
            if key in archivos_agrupados:
                tabla_key = key
                break
        
        if tabla_key is None:
            print("❌ MTX_WALLET_ORA no está en archivos agrupados")
            print("📋 Claves disponibles:")
            for key in archivos_agrupados.keys():
                print(f"   - {key}")
            return
            
        print(f"✅ MTX_WALLET_ORA encontrado con clave: {tabla_key}")
        print(f"📊 {tabla_key} agrupado con {len(archivos_agrupados[tabla_key])} archivos")
        
        # Cargar algunos archivos individuales para análisis
        print("\n📋 PASO 3: Analizar archivos individuales...")
        
        archivos_mtx = archivos_agrupados[tabla_key][:3]  # Tomar los primeros 3
        
        dataframes_individuales = []
        for i, archivo_info in enumerate(archivos_mtx):
            # La estructura real contiene 'ruta_completa' en lugar de 'bucket' y 'key'
            bucket = 'prd-datalake-bronze-zone-637423440311'  # Bucket fijo para Bronze Zone
            key = archivo_info['ruta_completa']
            
            print(f"\n🔍 Analizando archivo {i+1}: {key}")
            print(f"    📋 Estructura archivo_info: {archivo_info}")  # Debug
            
            try:
                df = leer_parquet_desde_s3(bucket, key)
                if not df.empty:
                    print(f"   📊 Registros: {len(df)}")
                    print(f"   📋 Columnas: {len(df.columns)}")
                    
                    # Contar NaN por columna
                    nan_counts = df.isnull().sum()
                    cols_with_nan = nan_counts[nan_counts > 0]
                    
                    print(f"   🔍 Columnas con NaN: {len(cols_with_nan)}")
                    if len(cols_with_nan) > 0:
                        print("   📝 Detalle NaN por columna:")
                        for col, count in cols_with_nan.head(10).items():
                            print(f"      - {col}: {count} NaN")
                    
                    dataframes_individuales.append({
                        'archivo': key,
                        'dataframe': df,
                        'nan_counts': nan_counts
                    })
                    
            except Exception as e:
                print(f"   ❌ Error cargando: {str(e)}")
        
        # Analizar diferencias entre archivos
        if len(dataframes_individuales) >= 2:
            print("\n📋 PASO 4: Comparar esquemas entre archivos...")
            
            df1_info = dataframes_individuales[0]
            df2_info = dataframes_individuales[1]
            
            df1 = df1_info['dataframe']
            df2 = df2_info['dataframe']
            
            print(f"\n🔍 Comparando: {df1_info['archivo']} vs {df2_info['archivo']}")
            
            # Comparar columnas
            cols1 = set(df1.columns)
            cols2 = set(df2.columns)
            
            comunes = cols1 & cols2
            solo_df1 = cols1 - cols2
            solo_df2 = cols2 - cols1
            
            print(f"   📊 Columnas comunes: {len(comunes)}")
            print(f"   📊 Solo en archivo 1: {len(solo_df1)}")
            print(f"   📊 Solo en archivo 2: {len(solo_df2)}")
            
            if solo_df1:
                print("   🔹 Solo en archivo 1:")
                for col in sorted(list(solo_df1)[:5]):  # Mostrar máximo 5
                    print(f"      - {col}")
            
            if solo_df2:
                print("   🔹 Solo en archivo 2:")
                for col in sorted(list(solo_df2)[:5]):  # Mostrar máximo 5
                    print(f"      - {col}")
            
            # Comparar tipos de datos en columnas comunes
            print(f"\n🔍 Diferencias de tipos de datos:")
            diferencias_tipos = 0
            for col in sorted(list(comunes)[:10]):  # Revisar las primeras 10 columnas comunes
                tipo1 = str(df1[col].dtype)
                tipo2 = str(df2[col].dtype)
                
                if tipo1 != tipo2:
                    print(f"   ⚠️ {col}: {tipo1} vs {tipo2}")
                    diferencias_tipos += 1
            
            if diferencias_tipos == 0:
                print("   ✅ No se encontraron diferencias significativas en tipos")
        
        # Simular el proceso de consolidación con seguimiento detallado
        print("\n📋 PASO 5: Simular consolidación con seguimiento...")
        
        if len(dataframes_individuales) >= 2:
            print("\n🔬 Simulando merge entre los primeros dos archivos...")
            
            df1 = dataframes_individuales[0]['dataframe'].copy()
            df2 = dataframes_individuales[1]['dataframe'].copy()
            
            print(f"   📊 Archivo 1: {len(df1)} registros, {len(df1.columns)} columnas")
            print(f"   📊 Archivo 2: {len(df2)} registros, {len(df2.columns)} columnas")
            
            # Contar NaN antes del merge
            nan_antes_1 = df1.isnull().sum().sum()
            nan_antes_2 = df2.isnull().sum().sum()
            
            print(f"   🔍 NaN antes del merge - Archivo 1: {nan_antes_1}, Archivo 2: {nan_antes_2}")
            
            # Simular concat (proceso típico de consolidación)
            try:
                df_merged = pd.concat([df1, df2], ignore_index=True, sort=False)
                nan_despues = df_merged.isnull().sum().sum()
                
                print(f"   📊 Resultado merge: {len(df_merged)} registros, {len(df_merged.columns)} columnas")
                print(f"   🔍 NaN después del merge: {nan_despues}")
                print(f"   📈 Diferencia NaN: {nan_despues - (nan_antes_1 + nan_antes_2)}")
                
                if nan_despues > (nan_antes_1 + nan_antes_2):
                    print("   ⚠️ SE DETECTÓ INTRODUCCIÓN DE NaN DURANTE EL MERGE!")
                    
                    # Identificar qué columnas ganaron NaN
                    print("\n   🔍 Analizando columnas que ganaron NaN:")
                    for col in df_merged.columns:
                        nan_col_1 = df1[col].isnull().sum() if col in df1.columns else 0
                        nan_col_2 = df2[col].isnull().sum() if col in df2.columns else 0
                        nan_col_merged = df_merged[col].isnull().sum()
                        
                        esperado = nan_col_1 + nan_col_2
                        if nan_col_merged > esperado:
                            print(f"      ⚠️ {col}: {esperado} esperado, {nan_col_merged} obtenido (+{nan_col_merged - esperado})")
                
            except Exception as e:
                print(f"   ❌ Error en simulación de merge: {str(e)}")
        
        # PASO 6: Analizar archivo consolidado existente vs individual
        print("\n📋 PASO 6: Comparar con archivo consolidado existente...")
        
        try:
            # Intentar leer el archivo consolidado
            consolidado_key = f"{tabla_key}/consolidado_puro.parquet"
            df_consolidado = leer_parquet_desde_s3('prd-datalake-silver-zone-637423440311', consolidado_key)
            
            if not df_consolidado.empty and len(dataframes_individuales) > 0:
                df_individual = dataframes_individuales[0]['dataframe']
                
                print(f"✅ Archivo consolidado cargado")
                print(f"   📊 Consolidado: {len(df_consolidado)} registros, {len(df_consolidado.columns)} columnas")
                print(f"   📊 Individual: {len(df_individual)} registros, {len(df_individual.columns)} columnas")
                
                # Comparar esquemas
                cols_consolidado = set(df_consolidado.columns)
                cols_individual = set(df_individual.columns)
                
                solo_consolidado = cols_consolidado - cols_individual
                solo_individual = cols_individual - cols_consolidado
                comunes = cols_consolidado & cols_individual
                
                print(f"   📊 Columnas comunes: {len(comunes)}")
                print(f"   📊 Solo en consolidado: {len(solo_consolidado)}")
                print(f"   📊 Solo en individual: {len(solo_individual)}")
                
                if solo_consolidado:
                    print("   🔹 Solo en consolidado:")
                    for col in sorted(list(solo_consolidado)[:5]):
                        print(f"      - {col}")
                
                if solo_individual:
                    print("   🔹 Solo en individual:")
                    for col in sorted(list(solo_individual)[:5]):
                        print(f"      - {col}")
                
                # Analizar diferencias de NaN
                print(f"\n🔍 Análisis de NaN consolidado vs individual:")
                
                nan_consolidado_total = df_consolidado.isnull().sum().sum()
                nan_individual_total = df_individual.isnull().sum().sum()
                
                print(f"   📊 Total NaN consolidado: {nan_consolidado_total}")
                print(f"   📊 Total NaN individual: {nan_individual_total}")
                
                # Comparar NaN por columna común
                print(f"\n   📋 NaN por columna común:")
                for col in sorted(list(comunes)[:10]):  # Primeras 10 columnas comunes
                    nan_cons = df_consolidado[col].isnull().sum()
                    nan_indiv = df_individual[col].isnull().sum()
                    
                    if nan_cons > 0 or nan_indiv > 0:
                        proporcion_cons = (nan_cons / len(df_consolidado)) * 100
                        proporcion_indiv = (nan_indiv / len(df_individual)) * 100
                        print(f"      {col}: consolidado={nan_cons}({proporcion_cons:.1f}%), individual={nan_indiv}({proporcion_indiv:.1f}%)")
                
                # Simular merge del individual con consolidado
                print(f"\n🔬 Simulando merge individual + consolidado...")
                
                nan_antes_total = nan_consolidado_total + nan_individual_total
                
                try:
                    df_simulado = pd.concat([df_consolidado, df_individual], ignore_index=True, sort=False)
                    nan_simulado_total = df_simulado.isnull().sum().sum()
                    
                    print(f"   📊 Resultado simulación: {len(df_simulado)} registros")
                    print(f"   🔍 NaN antes: {nan_antes_total}")
                    print(f"   🔍 NaN después: {nan_simulado_total}")
                    print(f"   📈 Diferencia: {nan_simulado_total - nan_antes_total}")
                    
                    if nan_simulado_total > nan_antes_total:
                        print("   ⚠️ SE DETECTÓ INTRODUCCIÓN DE NaN EN LA SIMULACIÓN!")
                        
                        # Identificar columnas problemáticas
                        print("\n   🔍 Columnas que ganaron NaN en la simulación:")
                        for col in df_simulado.columns:
                            nan_cons_col = df_consolidado[col].isnull().sum() if col in df_consolidado.columns else 0
                            nan_indiv_col = df_individual[col].isnull().sum() if col in df_individual.columns else 0
                            nan_simulado_col = df_simulado[col].isnull().sum()
                            
                            esperado = nan_cons_col + nan_indiv_col
                            if nan_simulado_col > esperado:
                                print(f"      ⚠️ {col}: esperado={esperado}, obtenido={nan_simulado_col} (+{nan_simulado_col - esperado})")
                                
                                # Analizar por qué se introdujeron NaN
                                if col not in df_consolidado.columns:
                                    print(f"         🔍 CAUSA: Columna '{col}' NO existe en consolidado")
                                elif col not in df_individual.columns:
                                    print(f"         🔍 CAUSA: Columna '{col}' NO existe en individual")
                                else:
                                    print(f"         🔍 CAUSA: Diferencia de tipos o estructura")
                    else:
                        print("   ✅ No se detectó introducción de NaN en la simulación")
                        
                except Exception as e:
                    print(f"   ❌ Error en simulación: {str(e)}")
            else:
                print("❌ No se pudo cargar el archivo consolidado o no hay archivos individuales")
                
        except Exception as e:
            print(f"❌ Error en análisis consolidado: {str(e)}")
        
        # PASO 7: Buscar más archivos MTX_WALLET_ORA en diferentes ubicaciones
        print("\n📋 PASO 7: Búsqueda exhaustiva de archivos MTX_WALLET_ORA...")
        
        try:
            import boto3
            s3_client = boto3.client('s3')
            
            # Buckets a buscar
            buckets_busqueda = [
                'prd-datalake-bronze-zone-637423440311',
                'prd-datalake-silver-zone-637423440311'
            ]
            
            archivos_encontrados = []
            
            for bucket in buckets_busqueda:
                print(f"\n🔍 Buscando en {bucket}...")
                
                # Buscar archivos MTX_WALLET_ORA en diferentes rutas
                prefijos_busqueda = [
                    'PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/',
                    'MTX_WALLET_ORA/',
                    'PDP_PROD10_MAINDB/MTX_WALLET_ORA/',
                ]
                
                for prefijo in prefijos_busqueda:
                    try:
                        paginator = s3_client.get_paginator('list_objects_v2')
                        for page in paginator.paginate(Bucket=bucket, Prefix=prefijo):
                            if 'Contents' in page:
                                for obj in page['Contents']:
                                    if obj['Key'].endswith('.parquet') and 'MTX_WALLET_ORA' in obj['Key']:
                                        archivos_encontrados.append({
                                            'bucket': bucket,
                                            'key': obj['Key'],
                                            'size': obj['Size'],
                                            'last_modified': obj['LastModified']
                                        })
                    except Exception as e:
                        print(f"   ⚠️ Error buscando en {prefijo}: {str(e)}")
            
            print(f"\n✅ Archivos MTX_WALLET_ORA encontrados: {len(archivos_encontrados)}")
            
            if len(archivos_encontrados) > 1:
                print("📋 Lista de archivos encontrados:")
                for i, archivo in enumerate(archivos_encontrados[:10], 1):  # Mostrar máximo 10
                    size_mb = archivo['size'] / (1024 * 1024)
                    print(f"   {i}. {archivo['key']} ({size_mb:.2f} MB)")
                    print(f"      📅 Modificado: {archivo['last_modified']}")
                
                # Si hay múltiples archivos, analizar algunos adicionales
                if len(archivos_encontrados) >= 3:
                    print(f"\n🔬 Analizando archivos adicionales para comparación...")
                    
                    archivos_analizar = archivos_encontrados[1:3]  # Analizar el 2do y 3er archivo
                    
                    for i, archivo_info in enumerate(archivos_analizar, 2):
                        print(f"\n🔍 Analizando archivo adicional {i}: {archivo_info['key']}")
                        
                        try:
                            df_adicional = leer_parquet_desde_s3(archivo_info['bucket'], archivo_info['key'])
                            if not df_adicional.empty:
                                print(f"   📊 Registros: {len(df_adicional)}")
                                print(f"   📋 Columnas: {len(df_adicional.columns)}")
                                
                                # Contar NaN
                                nan_counts_adicional = df_adicional.isnull().sum()
                                cols_with_nan_adicional = nan_counts_adicional[nan_counts_adicional > 0]
                                
                                print(f"   🔍 Columnas con NaN: {len(cols_with_nan_adicional)}")
                                
                                # Comparar con el primer archivo si está disponible
                                if len(dataframes_individuales) > 0:
                                    df_primero = dataframes_individuales[0]['dataframe']
                                    
                                    # Comparar esquemas
                                    cols_primero = set(df_primero.columns)
                                    cols_adicional = set(df_adicional.columns)
                                    
                                    diferencias_esquema = (cols_primero - cols_adicional) | (cols_adicional - cols_primero)
                                    
                                    if diferencias_esquema:
                                        print(f"   ⚠️ Diferencias de esquema con primer archivo: {len(diferencias_esquema)} columnas")
                                        print(f"       Solo en primero: {len(cols_primero - cols_adicional)}")
                                        print(f"       Solo en adicional: {len(cols_adicional - cols_primero)}")
                                    else:
                                        print(f"   ✅ Esquema idéntico al primer archivo")
                                
                        except Exception as e:
                            print(f"   ❌ Error cargando archivo adicional: {str(e)}")
            else:
                print("ℹ️ Solo se encontró un archivo, no es posible hacer comparación adicional")
                
        except Exception as e:
            print(f"❌ Error en búsqueda exhaustiva: {str(e)}")

        print(f"\n✅ Investigación completada: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ Error durante investigación: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analizar_merge_nan()
