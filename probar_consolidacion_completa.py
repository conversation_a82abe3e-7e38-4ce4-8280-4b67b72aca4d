#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para ejecutar consolidación específica de MTX_WALLET_ORA y detectar dónde falla
"""

import os
import sys
import pandas as pd
import boto3
import logging
from io import BytesIO
import warnings

# Suprimir warnings
warnings.filterwarnings('ignore')

# Configurar logging detallado
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Agregar el directorio actual al path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from tabla_config_simple import (
    detectar_clave_primaria_tabla,
    validar_clave_primaria
)

from app_raw_consolidado import (
    leer_archivo_resumen,
    agrupar_archivos_por_tabla,
    consolidar_archivos_tabla_incremental,
    escribir_parquet_a_s3
)

def probar_consolidacion_mtx_wallet():
    """Prueba la consolidación completa de MTX_WALLET_ORA paso a paso"""
    
    print("\n" + "="*80)
    print("🚀 PRUEBA COMPLETA: Consolidación MTX_WALLET_ORA")
    print("="*80)
    
    seccion_img = "IMG_FLOW_42"
    tabla_objetivo = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA"
    
    try:
        # 1. Leer archivo de resumen
        print(f"\n📋 PASO 1: Leer archivo de resumen...")
        df_resumen = leer_archivo_resumen(seccion_img)
        
        if df_resumen.empty:
            print(f"❌ No se pudo leer el archivo de resumen")
            return
        
        print(f"✅ Resumen cargado: {len(df_resumen)} registros")
        
        # 2. Agrupar archivos por tabla
        print(f"\n📋 PASO 2: Agrupar archivos por tabla...")
        grupos_archivos = agrupar_archivos_por_tabla(df_resumen)
        
        if tabla_objetivo not in grupos_archivos:
            print(f"❌ Tabla {tabla_objetivo} no encontrada en resumen")
            print(f"Tablas disponibles: {list(grupos_archivos.keys())}")
            return
        
        archivos_mtx = grupos_archivos[tabla_objetivo]
        print(f"✅ Encontrados {len(archivos_mtx)} archivos para MTX_WALLET_ORA")
        
        for archivo in archivos_mtx:
            print(f"   - {archivo['nombre_archivo']}")
        
        # 3. Probar consolidación paso a paso
        print(f"\n📋 PASO 3: Consolidación incremental...")
        print(f"   Tabla: {tabla_objetivo}")
        print(f"   Archivos a procesar: {len(archivos_mtx)}")
        
        # Ejecutar consolidación con logging detallado
        df_consolidado = consolidar_archivos_tabla_incremental(tabla_objetivo, archivos_mtx)
        
        if df_consolidado.empty:
            print(f"❌ Consolidación resultó en DataFrame vacío")
            return
        
        print(f"✅ Consolidación exitosa: {len(df_consolidado)} registros consolidados")
        print(f"📋 Columnas del consolidado: {list(df_consolidado.columns)}")
        
        # 4. Verificar llaves primarias en el consolidado final
        print(f"\n📋 PASO 4: Verificar llaves primarias en consolidado final...")
        
        tabla_nombre = tabla_objetivo.split('/')[-1]  # MTX_WALLET_ORA
        claves_detectadas = detectar_clave_primaria_tabla(tabla_nombre, df_consolidado)
        print(f"   Claves detectadas: {claves_detectadas}")
        
        if claves_detectadas:
            es_valida = validar_clave_primaria(df_consolidado, claves_detectadas)
            print(f"   ¿Validación exitosa?: {es_valida}")
            
            if not es_valida:
                print(f"⚠️ PROBLEMA EN CONSOLIDADO FINAL:")
                analizar_problemas_consolidado(df_consolidado, claves_detectadas)
            else:
                print(f"✅ Consolidado final tiene llaves primarias válidas")
        
        # 5. Mostrar estadísticas finales
        print(f"\n📊 ESTADÍSTICAS FINALES:")
        print(f"   Total registros: {len(df_consolidado)}")
        
        if claves_detectadas and len(claves_detectadas) == 1:
            col_clave = claves_detectadas[0]
            if col_clave in df_consolidado.columns:
                valores_unicos = df_consolidado[col_clave].nunique()
                print(f"   Valores únicos en {col_clave}: {valores_unicos}")
                print(f"   Porcentaje unicidad: {(valores_unicos/len(df_consolidado))*100:.1f}%")
                
                # Detectar duplicados si los hay
                duplicados = df_consolidado[df_consolidado[col_clave].duplicated()]
                if len(duplicados) > 0:
                    print(f"   ⚠️ DUPLICADOS ENCONTRADOS: {len(duplicados)} registros")
                    print(f"   Valores duplicados:")
                    valores_duplicados = duplicados[col_clave].value_counts()
                    for valor, count in valores_duplicados.head(5).items():
                        print(f"       {valor}: {count} veces")
                else:
                    print(f"   ✅ No hay duplicados en {col_clave}")
        
        print(f"\n✅ PROCESO COMPLETADO EXITOSAMENTE")
        
    except Exception as e:
        print(f"❌ ERROR EN EL PROCESO: {str(e)}")
        logging.exception("Error completo:")

def analizar_problemas_consolidado(df, claves):
    """Analiza problemas específicos en el consolidado"""
    print(f"   🔍 Analizando problemas en consolidado...")
    
    for col in claves:
        if col in df.columns:
            # Problemas de nulos/vacíos
            nulos = df[col].isna().sum()
            vacios = (df[col].astype(str).str.strip() == '').sum()
            
            if nulos > 0:
                print(f"       - {col}: {nulos} valores nulos ({(nulos/len(df))*100:.1f}%)")
            if vacios > 0:
                print(f"       - {col}: {vacios} valores vacíos ({(vacios/len(df))*100:.1f}%)")
            
            # Problemas de duplicados
            valores_unicos = df[col].nunique()
            total = len(df)
            duplicados = total - valores_unicos
            
            if duplicados > 0:
                print(f"       - {col}: {duplicados} valores duplicados ({(duplicados/total)*100:.1f}%)")
                
                # Mostrar ejemplos de duplicados
                duplicated_values = df[df[col].duplicated(keep=False)][col].value_counts().head(3)
                print(f"         Ejemplos de valores duplicados:")
                for valor, count in duplicated_values.items():
                    print(f"           '{valor}': aparece {count} veces")

if __name__ == "__main__":
    probar_consolidacion_mtx_wallet()
