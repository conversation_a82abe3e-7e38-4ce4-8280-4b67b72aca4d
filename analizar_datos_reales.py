#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para analizar datos reales y identificar problemas específicos con llaves primarias
"""

import sys
import os
import pandas as pd
import logging
import boto3
from io import BytesIO
from botocore.exceptions import ClientError

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Importar funciones de configuración
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from tabla_config_simple import detectar_clave_primaria_tabla, validar_clave_primaria

# Configuración de S3
S3_LANDING_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_RAW_BUCKET = "prd-datalake-silver-zone-637423440311"

def analizar_datos_reales_tabla(tabla_nombre, limite_archivos=3):
    """
    Analiza datos reales de una tabla específica en S3
    """
    print(f"\n🔍 ANALIZANDO DATOS REALES: {tabla_nombre}")
    print("=" * 60)
    
    try:
        s3_client = boto3.client('s3')
        
        # Buscar archivos de la tabla en el bucket landing
        prefijo_busqueda = f"CONSOLIDADOS/{tabla_nombre}/"
        
        print(f"📁 Buscando archivos en: {S3_LANDING_BUCKET}/{prefijo_busqueda}")
        
        # Listar archivos
        paginator = s3_client.get_paginator('list_objects_v2')
        archivos_encontrados = []
        
        for page in paginator.paginate(Bucket=S3_LANDING_BUCKET, Prefix=prefijo_busqueda):
            if 'Contents' in page:
                for obj in page['Contents']:
                    if obj['Key'].endswith('.parquet'):
                        archivos_encontrados.append(obj['Key'])
        
        if not archivos_encontrados:
            # Intentar en bucket raw/silver
            print(f"📁 No encontrado en landing, buscando en: {S3_RAW_BUCKET}/{tabla_nombre}/")
            
            for page in paginator.paginate(Bucket=S3_RAW_BUCKET, Prefix=f"{tabla_nombre}/"):
                if 'Contents' in page:
                    for obj in page['Contents']:
                        if obj['Key'].endswith('.parquet'):
                            archivos_encontrados.append(obj['Key'])
            
            if archivos_encontrados:
                bucket_actual = S3_RAW_BUCKET
            else:
                print(f"❌ No se encontraron archivos .parquet para {tabla_nombre}")
                return
        else:
            bucket_actual = S3_LANDING_BUCKET
        
        print(f"✅ Encontrados {len(archivos_encontrados)} archivos .parquet")
        
        # Analizar los primeros archivos (límite para no sobrecargar)
        archivos_analizar = archivos_encontrados[:limite_archivos]
        
        for i, archivo_key in enumerate(archivos_analizar, 1):
            print(f"\n📄 Archivo {i}/{len(archivos_analizar)}: {archivo_key}")
            
            try:
                # Leer archivo Parquet
                response = s3_client.get_object(Bucket=bucket_actual, Key=archivo_key)
                buffer = BytesIO(response['Body'].read())
                df = pd.read_parquet(buffer)
                
                print(f"   📊 Filas: {len(df)}, Columnas: {len(df.columns)}")
                print(f"   📋 Columnas disponibles: {list(df.columns)}")
                
                # Detectar clave primaria
                claves_detectadas = detectar_clave_primaria_tabla(tabla_nombre, df)
                print(f"   🔑 Claves detectadas: {claves_detectadas}")
                
                if claves_detectadas:
                    # Analizar cada clave detectada
                    for clave in claves_detectadas:
                        if clave in df.columns:
                            valores_unicos = df[clave].nunique()
                            total_filas = len(df)
                            nulos = df[clave].isna().sum()
                            vacios = (df[clave].astype(str).str.strip() == '').sum()
                            unicidad = valores_unicos / total_filas if total_filas > 0 else 0
                            
                            print(f"   🔍 Análisis de {clave}:")
                            print(f"      - Valores únicos: {valores_unicos}/{total_filas} ({unicidad:.1%})")
                            print(f"      - Valores nulos: {nulos}")
                            print(f"      - Valores vacíos: {vacios}")
                            
                            # Mostrar muestra de valores
                            muestra = df[clave].dropna().head(5).tolist()
                            print(f"      - Muestra: {muestra}")
                    
                    # Validar clave primaria
                    es_valida = validar_clave_primaria(df, claves_detectadas)
                    print(f"   ✅ ¿Clave válida?: {es_valida}")
                
                else:
                    print(f"   ❌ No se detectaron claves primarias válidas")
                    
                    # Sugerir columnas candidatas
                    candidatas = [col for col in df.columns if 'ID' in col.upper() or 'NUMBER' in col.upper()]
                    if candidatas:
                        print(f"   💡 Columnas candidatas para clave primaria: {candidatas}")
                
            except Exception as e:
                print(f"   ❌ Error leyendo archivo: {str(e)}")
                
    except Exception as e:
        print(f"❌ Error general analizando {tabla_nombre}: {str(e)}")

def analizar_multiples_tablas():
    """Analiza múltiples tablas configuradas"""
    tablas_analizar = [
        "MTX_WALLET_ORA",
        "USER_PROFILE_ORA", 
        "MTX_CATEGORIES_ORA"
    ]
    
    print("🔍 ANÁLISIS DE DATOS REALES - MÚLTIPLES TABLAS")
    print("=" * 70)
    
    for tabla in tablas_analizar:
        analizar_datos_reales_tabla(tabla, limite_archivos=2)
        print()

if __name__ == "__main__":
    # Permitir análisis de tabla específica como parámetro
    if len(sys.argv) > 1:
        tabla_especifica = sys.argv[1]
        analizar_datos_reales_tabla(tabla_especifica)
    else:
        analizar_multiples_tablas()
