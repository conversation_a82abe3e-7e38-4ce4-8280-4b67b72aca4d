#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Consolidación Específica MTX_WALLET_ORA
======================================

Script para consolidar específicamente MTX_WALLET_ORA y verificar la integridad
"""

import sys
import pandas as pd
import boto3
import logging
from io import BytesIO
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Configurar ruta para imports
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Importar funciones del sistema principal
from app_raw_consolidado_puro import (
    leer_archivo_resumen,
    agrupar_archivos_por_tabla,
    consolidar_archivos_tabla_incremental_puro,
    leer_parquet_desde_s3,
    escribir_parquet_a_s3_puro
)

def consolidar_mtx_wallet_especifico():
    """Consolida específicamente MTX_WALLET_ORA."""
    
    print("🎯 CONSOLIDACIÓN ESPECÍFICA MTX_WALLET_ORA")
    print("=" * 60)
    print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    seccion_img = "IMG_FLOW_42"
    tabla_objetivo = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA"
    
    try:
        # 1. Leer archivo de resumen
        print(f"\n📋 PASO 1: Leer archivo de resumen...")
        df_resumen = leer_archivo_resumen(seccion_img)
        
        if df_resumen.empty:
            print(f"❌ No se pudo leer el archivo de resumen")
            return False
        
        print(f"✅ Resumen cargado: {len(df_resumen)} registros")
        
        # 2. Agrupar archivos por tabla
        print(f"\n📋 PASO 2: Agrupar archivos por tabla...")
        grupos_archivos = agrupar_archivos_por_tabla(df_resumen)
        
        if tabla_objetivo not in grupos_archivos:
            print(f"❌ Tabla {tabla_objetivo} no encontrada en resumen")
            print(f"Tablas disponibles: {list(grupos_archivos.keys())}")
            return False
        
        archivos_mtx = grupos_archivos[tabla_objetivo]
        print(f"✅ Encontrados {len(archivos_mtx)} archivos para MTX_WALLET_ORA")
        
        for archivo in archivos_mtx:
            print(f"   📁 {archivo['nombre_archivo']}")
            print(f"      📊 Registros: {archivo.get('num_registros', 'N/A')}")
            print(f"      📅 Fecha: {archivo.get('partition_date', 'N/A')}")
        
        # 3. Leer archivo origen directamente para verificar
        print(f"\n📋 PASO 3: Verificar archivo origen...")
        archivo_origen = archivos_mtx[0]['ruta_completa']
        bucket_origen = "prd-datalake-bronze-zone-637423440311"  # Bucket landing/bronze
        
        print(f"   📍 Bucket: {bucket_origen}")
        print(f"   📍 Archivo: {archivo_origen}")
        
        df_origen = leer_parquet_desde_s3(bucket_origen, archivo_origen)
        
        if df_origen.empty:
            print(f"❌ No se pudo leer archivo origen")
            return False
        
        print(f"✅ Archivo origen leído:")
        print(f"   📊 Registros: {len(df_origen)}")
        print(f"   📊 Columnas: {len(df_origen.columns)}")
        print(f"   🔑 WALLET_NUMBER únicos: {df_origen['WALLET_NUMBER'].nunique()}")
        
        # 4. Ejecutar consolidación
        print(f"\n📋 PASO 4: Ejecutar consolidación...")
        resultado = consolidar_archivos_tabla_incremental_puro(tabla_objetivo, archivos_mtx)
        
        if resultado.empty:
            print(f"❌ Consolidación resultó en DataFrame vacío")
            return False
        
        print(f"✅ Consolidación exitosa:")
        print(f"   📊 Registros consolidados: {len(resultado)}")
        print(f"   📊 Columnas: {len(resultado.columns)}")
        print(f"   🔑 WALLET_NUMBER únicos: {resultado['WALLET_NUMBER'].nunique()}")
        
        # 5. Verificar integridad
        print(f"\n📋 PASO 5: Verificar integridad...")
        
        wallet_origen = set(df_origen['WALLET_NUMBER'])
        wallet_consolidado = set(resultado['WALLET_NUMBER'])
        
        perdidos = wallet_origen - wallet_consolidado
        nuevos = wallet_consolidado - wallet_origen
        comunes = wallet_origen.intersection(wallet_consolidado)
        
        print(f"   📊 Registros origen: {len(df_origen)}")
        print(f"   📊 Registros consolidado: {len(resultado)}")
        print(f"   📊 WALLET_NUMBER origen: {len(wallet_origen)}")
        print(f"   📊 WALLET_NUMBER consolidado: {len(wallet_consolidado)}")
        print(f"   📊 Comunes: {len(comunes)}")
        print(f"   📊 Perdidos: {len(perdidos)}")
        print(f"   📊 Nuevos: {len(nuevos)}")
        
        if len(perdidos) == 0:
            print(f"   ✅ PERFECTO: Todos los registros preservados")
            retorno = True
        else:
            print(f"   ❌ PROBLEMA: Se perdieron {len(perdidos)} registros")
            print(f"   📋 Ejemplos perdidos: {list(perdidos)[:5]}")
            retorno = False
        
        # 6. Guardar consolidado
        print(f"\n📋 PASO 6: Guardar consolidado...")
        
        bucket_destino = "prd-datalake-silver-zone-637423440311"
        archivo_destino = f"{tabla_objetivo}/consolidado_puro.parquet"
        
        try:
            escribir_parquet_a_s3_puro(resultado, bucket_destino, archivo_destino)
            print(f"✅ Consolidado guardado exitosamente")
            print(f"   📍 Ubicación: s3://{bucket_destino}/{archivo_destino}")
        except Exception as e:
            print(f"❌ Error guardando consolidado: {str(e)}")
            retorno = False
        
        print(f"\n✅ CONSOLIDACIÓN COMPLETADA")
        print(f"⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return retorno
        
    except Exception as e:
        print(f"❌ Error durante consolidación: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    exito = consolidar_mtx_wallet_especifico()
    if exito:
        print(f"\n🎉 ÉXITO: Consolidación completada correctamente")
    else:
        print(f"\n💥 FALLO: Problemas durante la consolidación")
