#!/usr/bin/env python3
"""
==========================================
 Módulo ETL Runner
 Autor: <PERSON><PERSON><PERSON><PERSON> Galarza
 Descripción: Ejecuta el flujo principal del ETL.
 Fecha: 2024-03-07
==========================================
"""
import sys
import pathlib

def run_etl(seccion_img: str):
    """
    Función que invoca la lógica principal del ETL.
    """
    # Asegúrate de que el directorio 'src' esté en el path
    project_root = pathlib.Path(__file__).parent.resolve()
    sys.path.insert(0, str(project_root))

    # Ahora puedes importar tu 'main' desde src/etl/main.py
    from src.etl.main import main

    # Prepara los argumentos para el 'main' que usa argparse
    sys.argv = ["main.py", seccion_img] 
    # Nota: El primer elemento (simulamos el script) + seccion_img

    # Ejecuta el main
    main()


if __name__ == "__main__":
    # Verificamos que el usuario pase la sección IMG
    if len(sys.argv) < 2:
        print("Uso: python3 app_landing.py [SECCION_IMG]")
        sys.exit(1)

    seccion_img = sys.argv[1]
    run_etl(seccion_img)