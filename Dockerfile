# Flow_ETL_Landing_ED
# Autor: <PERSON><PERSON><PERSON><PERSON>rz<PERSON>
# Última actualización: 2024-03-07

# Usar una imagen base de Python optimizada para producción
FROM python:3.9-slim

# Establecer variables de entorno
ENV PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=UTF-8 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    # Configuraciones específicas para gestión de memoria
    MALLOC_ARENA_MAX=2 \
    MALLOC_TRIM_THRESHOLD_=65536 \
    MALLOC_MMAP_THRESHOLD_=65536 \
    PYTHONMALLOC=malloc \
    # Configuración de Dask
    DASK_DISTRIBUTED__WORKER__MEMORY__TARGET=0.6 \
    DASK_DISTRIBUTED__WORKER__MEMORY__SPILL=0.7 \
    DASK_DISTRIBUTED__WORKER__MEMORY__PAUSE=0.8 \
    DASK_DISTRIBUTED__WORKER__MEMORY__TERMINATE=0.95 \
    # Configuración de pandas y numpy
    PANDAS_MAX_ROWS=100000 \
    PANDAS_CHUNKSIZE=10000 \
    NUMPY_MAX_THREADS=4

# Instalar herramientas necesarias y limpiar caché
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq-dev \
    gcc \
    python3-dev \
    default-jre \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Configurar Java para JDBC
ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

WORKDIR /app

# Copiar requirements.txt primero
COPY requirements.txt .

# Instalar dependencias optimizadas con mayor timeout y reintentos
RUN pip install --no-cache-dir --timeout 300 --retries 5 numpy==1.23.5 && \
    pip install --no-cache-dir --timeout 300 --retries 5 dask[complete]==2023.5.0 && \
    pip install --no-cache-dir --timeout 300 --retries 5 -r requirements.txt

# Script de inicio para configurar límites de memoria
COPY <<EOF /app/entrypoint.sh
#!/bin/bash
# Configurar límites de memoria del sistema
ulimit -v 7340032  # 7GB en KB
ulimit -m 7340032  # 7GB en KB

# Configurar Python para usar menos memoria
export PYTHONMALLOC=malloc
export MALLOC_TRIM_THRESHOLD_=65536
export MALLOC_ARENA_MAX=2

# Configurar Dask para uso eficiente de memoria
export DASK_MEMORY_LIMIT="7GB"
export DASK_TEMPORARY_DIRECTORY="/tmp/dask-worker-space"

# Crear directorio temporal para Dask
mkdir -p \$DASK_TEMPORARY_DIRECTORY

# Ejecutar la aplicación con los argumentos pasados
cd /app
exec python3 -X faulthandler app.py "\$@"
EOF

RUN chmod +x /app/entrypoint.sh

# Copiar el código de la aplicación
COPY . .

# Asegurar que los drivers JDBC estén en el lugar correcto
RUN mkdir -p /app/src/etl/driver

# Crear directorio temporal para Dask
RUN mkdir -p /tmp/dask-worker-space && \
    chmod 777 /tmp/dask-worker-space

# Verificar la instalación de Java
RUN java -version

# Healthcheck para monitorear uso de memoria
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python3 -c 'import psutil; exit(0) if psutil.virtual_memory().percent < 85 else exit(1)'

# Comando para ejecutar el script
CMD ["python", "app_landing.py"]