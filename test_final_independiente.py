#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test independiente final para verificar las mejoras
"""

import pandas as pd
import numpy as np
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Copiar la función normalizar_tipos_datos directamente para test independiente
def normalizar_tipos_datos_test(df: pd.DataFrame) -> pd.DataFrame:
    """
    Versión de test de la función normalizar_tipos_datos con las mejoras aplicadas.
    """
    if df.empty:
        return df
    
    df_normalizado = df.copy()
    
    for col in df_normalizado.columns:
        try:
            dtype = df_normalizado[col].dtype
            
            if dtype == 'object':
                sample_values = df_normalizado[col].dropna().head(100)
                
                if not sample_values.empty:
                    # Lista de valores que NO son timestamps
                    valores_no_timestamp = {
                        'WALLET', 'OPT', 'OPERATOR', 'ADMIN', 'USER', 'GUEST', 'CLIENT', 'CUSTOMER',
                        'ACTIVE', 'INACTIVE', 'PENDING', 'APPROVED', 'REJECTED', 'CANCELLED',
                        'YES', 'NO', 'TRUE', 'FALSE', 'ON', 'OFF', 'ENABLED', 'DISABLED',
                        'HIGH', 'MEDIUM', 'LOW', 'CRITICAL', 'WARNING', 'INFO', 'ERROR',
                        'BRONZE', 'SILVER', 'GOLD', 'PLATINUM', 'VIP', 'PREMIUM', 'BASIC'
                    }
                    
                    is_timestamp = False
                    timestamp_count = 0
                    
                    # Verificar valores que claramente NO son timestamps
                    valores_no_timestamp_encontrados = 0
                    for val in sample_values:
                        if isinstance(val, str):
                            val_upper = str(val).strip().upper()
                            if val_upper in valores_no_timestamp:
                                valores_no_timestamp_encontrados += 1
                    
                    # Si más del 30% son valores claramente no-timestamp, no convertir
                    if valores_no_timestamp_encontrados > len(sample_values) * 0.3:
                        is_timestamp = False
                        logging.info(f"Columna {col}: No convertir a timestamp - contiene valores como WALLET/OPT/OPERATOR")
                    else:
                        # Proceder con detección normal de timestamps
                        for val in sample_values:
                            if isinstance(val, str):
                                val_clean = str(val).strip()
                                
                                if (
                                    ('T' in val_clean and len(val_clean) >= 19) or
                                    (len(val_clean) >= 16 and 
                                     (
                                        (val_clean.count('-') == 2 and val_clean.count(':') >= 1 and 
                                         val_clean[:4].isdigit() and val_clean[5:7].isdigit()) or
                                        (val_clean.count('/') == 2 and val_clean.count(':') >= 1 and
                                         val_clean[6:10].isdigit())
                                     )
                                    )
                                ):
                                    timestamp_count += 1
                        
                        if len(sample_values) > 0 and (timestamp_count / len(sample_values)) >= 0.7:
                            is_timestamp = True
                    
                    if is_timestamp:
                        try:
                            df_normalizado[col] = pd.to_datetime(
                                df_normalizado[col], 
                                errors='coerce',
                                infer_datetime_format=True
                            ).dt.strftime('%Y-%m-%d %H:%M:%S')
                            logging.info(f"Columna {col} convertida de timestamp string a formato ISO string")
                        except Exception as e:
                            df_normalizado[col] = df_normalizado[col].astype(str)
                            logging.warning(f"Error convirtiendo timestamp en columna {col}: {str(e)}")
                    else:
                        df_normalizado[col] = df_normalizado[col].astype(str)
                        
        except Exception as e:
            logging.warning(f"Error normalizando columna {col}: {str(e)}")
            df_normalizado[col] = df_normalizado[col].astype(str)
    
    # Manejo selectivo de valores problemáticos
    for col in df_normalizado.columns:
        if df_normalizado[col].dtype == 'object':
            df_normalizado[col] = df_normalizado[col].fillna('')
            df_normalizado[col] = df_normalizado[col].replace(['nan', 'NaN', 'null'], '')
            
            mask_none = (df_normalizado[col] == 'None')
            if mask_none.any():
                unique_values = df_normalizado[col].unique()
                total_values = len(unique_values)
                none_count = (df_normalizado[col] == 'None').sum()
                
                if (none_count / len(df_normalizado) > 0.5) or (total_values <= 2 and '' in unique_values):
                    df_normalizado[col] = df_normalizado[col].replace('None', '')
                    logging.info(f"Columna {col}: Reemplazados {none_count} valores 'None' por cadena vacía")
                else:
                    logging.info(f"Columna {col}: Mantenidos {none_count} valores 'None' (datos válidos)")
    
    return df_normalizado

def main():
    print("🎯 TEST FINAL INDEPENDIENTE - VERIFICACIÓN DE MEJORAS")
    print("="*60)
    
    # Test 1: Datos que causaban el problema original
    print("\n1. 🧪 TEST: Problemas originales resueltos")
    
    datos_problematicos = {
        'PAYMENT_METHOD_TYPE_ID': ['WALLET', 'WALLET', 'WALLET', 'CARD'],
        'USER_GRADE': ['OPT', 'OPT', 'PREMIUM', 'OPT'],  
        'USER_TYPE': ['OPERATOR', 'USER', 'ADMIN', 'OPERATOR'],
        'WALLET_NUMBER': ['102IND03B', '103USA04C', '104MEX05D', '105EUR06E']
    }
    
    df_original = pd.DataFrame(datos_problematicos)
    df_mejorado = normalizar_tipos_datos_test(df_original.copy())
    
    print("   📊 Datos originales vs. después de mejoras:")
    
    todo_preservado = True
    for col in df_original.columns:
        original_vals = df_original[col].tolist()
        mejorado_vals = df_mejorado[col].tolist()
        
        print(f"\n   🔍 {col}:")
        print(f"      Original:  {original_vals}")
        print(f"      Mejorado:  {mejorado_vals}")
        
        if original_vals == mejorado_vals:
            print(f"      ✅ PRESERVADO CORRECTAMENTE")
        else:
            todo_preservado = False
            print(f"      ❌ CAMBIOS DETECTADOS")
            for i, (orig, mejr) in enumerate(zip(original_vals, mejorado_vals)):
                if orig != mejr:
                    print(f"         Fila {i}: '{orig}' → '{mejr}'")
    
    # Test 2: Timestamps legítimos
    print(f"\n2. 🧪 TEST: Funcionalidad de timestamps preservada")
    
    datos_timestamps = {
        'timestamp_real': ['2023-01-01T10:30:00', '2023-01-02T11:45:00', '2023-01-03T09:15:00'],
        'fecha_completa': ['2023-01-01 10:30:00', '2023-01-02 11:45:00', '2023-01-03 09:15:00']
    }
    
    df_ts_original = pd.DataFrame(datos_timestamps)
    df_ts_mejorado = normalizar_tipos_datos_test(df_ts_original.copy())
    
    timestamps_funcionan = False
    for col in df_ts_original.columns:
        ts_original = df_ts_original[col].tolist()
        ts_mejorado = df_ts_mejorado[col].tolist()
        
        if ts_original != ts_mejorado:
            timestamps_funcionan = True
            print(f"   ✅ {col}: Conversión de timestamp aplicada")
            print(f"      Ejemplo: '{ts_original[0]}' → '{ts_mejorado[0]}'")
        else:
            print(f"   ℹ️  {col}: No se detectó como timestamp (criterios estrictos)")
    
    # Test 3: Resistencia general
    print(f"\n3. 🧪 TEST: Resistencia con datos mixtos")
    
    datos_mixtos = {
        'mezclado': ['WALLET', 'VALUE123', 'OPT', 'ADMIN'],
        'alfanumerico': ['TXN123ABC', 'USR456DEF', '102IND03B', 'WAL789GHI']
    }
    
    df_mix_original = pd.DataFrame(datos_mixtos)
    df_mix_mejorado = normalizar_tipos_datos_test(df_mix_original.copy())
    
    resistencia_ok = True
    for col in df_mix_original.columns:
        mix_original = df_mix_original[col].tolist()
        mix_mejorado = df_mix_mejorado[col].tolist()
        
        if mix_original == mix_mejorado:
            print(f"   ✅ {col}: Datos mixtos preservados correctamente")
        else:
            resistencia_ok = False
            print(f"   ⚠️  {col}: Algunos cambios en datos mixtos")
    
    # Resumen final
    print(f"\n" + "="*60)
    print(f"📋 RESUMEN FINAL")
    print(f"="*60)
    
    if todo_preservado:
        print(f"✅ PROBLEMA PRINCIPAL RESUELTO:")
        print(f"   • Valores categóricos como 'WALLET', 'OPT', 'OPERATOR' preservados")
        print(f"   • IDs alfanuméricos como '102IND03B' mantenidos intactos")
        print(f"   • No hay corrupción de datos críticos")
    else:
        print(f"❌ PROBLEMA PRINCIPAL: AÚN PRESENTE")
        print(f"   • Algunos valores categóricos se siguen perdiendo")
    
    if timestamps_funcionan or resistencia_ok:
        print(f"✅ FUNCIONALIDADES PRESERVADAS:")
        print(f"   • Sistema de timestamps funciona cuando corresponde")
        print(f"   • Resistencia general a datos mixtos mantenida")
    
    if todo_preservado:
        print(f"\n🎉 ¡ÉXITO TOTAL!")
        print(f"   Las mejoras han resuelto completamente el problema")
        print(f"   El sistema está listo para consolidación en producción")
        
        print(f"\n📈 SIGUIENTE PASO:")
        print(f"   Ejecutar: python3 app_raw_consolidado.py IMG_FLOW_42")
        print(f"   Para aplicar estas mejoras al proceso completo")
    else:
        print(f"\n⚠️  REQUIERE ATENCIÓN ADICIONAL")
        print(f"   Revisar la lógica de normalización")
    
    print(f"\n✅ Test independiente completado")

if __name__ == "__main__":
    main()
