#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Análisis Data Engineer: Origen vs Consolidado MTX_WALLET_ORA
===========================================================

Comparación detallada de:
- Archivo origen: PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-024359_chunk_0.parquet
- Archivo consolidado: consolidado_puro.parquet
- Clave primaria: WALLET_NUMBER
"""

import boto3
import pandas as pd
import logging
from datetime import datetime
import warnings
from io import BytesIO

# Suprimir warnings
warnings.filterwarnings('ignore')

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Configuración S3
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"

# Rutas específicas
ARCHIVO_ORIGEN = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-024359_chunk_0.parquet"
ARCHIVO_CONSOLIDADO = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"

CLAVE_PRIMARIA = "WALLET_NUMBER"

def leer_parquet_desde_s3(bucket: str, key: str, descripcion: str) -> pd.DataFrame:
    """Lee un archivo parquet desde S3."""
    try:
        s3_client = boto3.client('s3')
        
        print(f"📥 Leyendo {descripcion}...")
        print(f"   📍 Bucket: {bucket}")
        print(f"   📍 Key: {key}")
        
        response = s3_client.get_object(Bucket=bucket, Key=key)
        parquet_content = response['Body'].read()
        
        df = pd.read_parquet(BytesIO(parquet_content))
        
        # Verificar tamaño del archivo
        file_size_mb = len(parquet_content) / (1024 * 1024)
        
        print(f"   ✅ Archivo leído exitosamente")
        print(f"   📊 Tamaño archivo: {file_size_mb:.2f} MB")
        print(f"   📊 Registros: {len(df):,}")
        print(f"   📊 Columnas: {len(df.columns)}")
        
        return df
        
    except Exception as e:
        print(f"   ❌ Error leyendo {descripcion}: {str(e)}")
        return pd.DataFrame()

def analizar_clave_primaria(df: pd.DataFrame, nombre_archivo: str) -> dict:
    """Analiza la clave primaria en un DataFrame."""
    
    print(f"\n🔑 ANÁLISIS DE CLAVE PRIMARIA: {nombre_archivo}")
    print("=" * 60)
    
    resultado = {
        'archivo': nombre_archivo,
        'registros_totales': len(df),
        'clave_presente': False,
        'valores_unicos': 0,
        'valores_nulos': 0,
        'duplicados': 0,
        'muestra_valores': []
    }
    
    if CLAVE_PRIMARIA not in df.columns:
        print(f"   ❌ Clave primaria '{CLAVE_PRIMARIA}' NO encontrada")
        print(f"   📋 Columnas disponibles: {list(df.columns)}")
        return resultado
    
    resultado['clave_presente'] = True
    
    # Análisis de la clave primaria
    valores_clave = df[CLAVE_PRIMARIA]
    valores_unicos = valores_clave.nunique()
    valores_nulos = valores_clave.isnull().sum()
    duplicados = len(df) - valores_unicos - valores_nulos
    
    resultado.update({
        'valores_unicos': valores_unicos,
        'valores_nulos': valores_nulos,
        'duplicados': duplicados,
        'muestra_valores': valores_clave.dropna().head(5).tolist()
    })
    
    print(f"   ✅ Clave primaria '{CLAVE_PRIMARIA}' encontrada")
    print(f"   📊 Registros totales: {len(df):,}")
    print(f"   📊 Valores únicos: {valores_unicos:,}")
    print(f"   📊 Valores nulos: {valores_nulos:,}")
    print(f"   📊 Duplicados detectados: {duplicados:,}")
    print(f"   📋 Muestra de valores: {resultado['muestra_valores']}")
    
    # Verificar integridad
    if valores_nulos > 0:
        print(f"   ⚠️  ADVERTENCIA: {valores_nulos} registros tienen clave primaria NULL")
    
    if duplicados > 0:
        print(f"   ⚠️  ADVERTENCIA: {duplicados} registros duplicados detectados")
        
        # Mostrar algunos duplicados
        duplicados_df = df[df.duplicated(subset=[CLAVE_PRIMARIA], keep=False)]
        if not duplicados_df.empty:
            print(f"   📋 Ejemplo de duplicados:")
            valores_duplicados = duplicados_df[CLAVE_PRIMARIA].value_counts().head(3)
            for valor, count in valores_duplicados.items():
                print(f"      {valor}: {count} veces")
    
    # Calcular porcentaje de unicidad
    total_validos = len(df) - valores_nulos
    if total_validos > 0:
        porcentaje_unicidad = (valores_unicos / total_validos) * 100
        print(f"   📈 Porcentaje de unicidad: {porcentaje_unicidad:.1f}%")
        
        if porcentaje_unicidad == 100.0:
            print(f"   ✅ EXCELENTE: Clave primaria única al 100%")
        elif porcentaje_unicidad >= 95.0:
            print(f"   ✅ BUENO: Clave primaria mayormente única")
        else:
            print(f"   ❌ PROBLEMA: Baja unicidad en clave primaria")
    
    return resultado

def comparar_estructuras(df_origen: pd.DataFrame, df_consolidado: pd.DataFrame):
    """Compara las estructuras de los DataFrames."""
    
    print(f"\n📊 COMPARACIÓN DE ESTRUCTURAS")
    print("=" * 60)
    
    print(f"📋 ORIGEN:")
    print(f"   Registros: {len(df_origen):,}")
    print(f"   Columnas: {len(df_origen.columns)}")
    print(f"   Columnas: {list(df_origen.columns)}")
    
    print(f"\n📋 CONSOLIDADO:")
    print(f"   Registros: {len(df_consolidado):,}")
    print(f"   Columnas: {len(df_consolidado.columns)}")
    print(f"   Columnas: {list(df_consolidado.columns)}")
    
    # Comparar columnas
    columnas_origen = set(df_origen.columns)
    columnas_consolidado = set(df_consolidado.columns)
    
    if columnas_origen == columnas_consolidado:
        print(f"\n   ✅ ESTRUCTURA PRESERVADA: Columnas idénticas")
    else:
        print(f"\n   ❌ ESTRUCTURA MODIFICADA:")
        
        faltantes_consolidado = columnas_origen - columnas_consolidado
        if faltantes_consolidado:
            print(f"      Columnas perdidas: {list(faltantes_consolidado)}")
        
        agregadas_consolidado = columnas_consolidado - columnas_origen
        if agregadas_consolidado:
            print(f"      Columnas agregadas: {list(agregadas_consolidado)}")

def analizar_tipos_datos(df: pd.DataFrame, nombre: str):
    """Analiza los tipos de datos."""
    
    print(f"\n📊 TIPOS DE DATOS - {nombre}")
    print("=" * 60)
    
    for col in df.columns:
        dtype = str(df[col].dtype)
        valores_unicos = df[col].nunique()
        valores_nulos = df[col].isnull().sum()
        
        print(f"   {col:30s} | {dtype:15s} | Únicos: {valores_unicos:8,} | Nulos: {valores_nulos:8,}")

def comparar_datos_clave_primaria(df_origen: pd.DataFrame, df_consolidado: pd.DataFrame):
    """Compara los datos de la clave primaria entre origen y consolidado."""
    
    print(f"\n🔍 COMPARACIÓN DETALLADA DE CLAVE PRIMARIA")
    print("=" * 60)
    
    if CLAVE_PRIMARIA not in df_origen.columns or CLAVE_PRIMARIA not in df_consolidado.columns:
        print(f"   ❌ No se puede comparar: clave primaria ausente")
        return
    
    valores_origen = set(df_origen[CLAVE_PRIMARIA].dropna())
    valores_consolidado = set(df_consolidado[CLAVE_PRIMARIA].dropna())
    
    print(f"   📊 Valores únicos en ORIGEN: {len(valores_origen):,}")
    print(f"   📊 Valores únicos en CONSOLIDADO: {len(valores_consolidado):,}")
    
    # Verificar pérdidas
    valores_perdidos = valores_origen - valores_consolidado
    valores_nuevos = valores_consolidado - valores_origen
    valores_comunes = valores_origen.intersection(valores_consolidado)
    
    print(f"   📊 Valores comunes: {len(valores_comunes):,}")
    print(f"   📊 Valores perdidos: {len(valores_perdidos):,}")
    print(f"   📊 Valores nuevos: {len(valores_nuevos):,}")
    
    if len(valores_perdidos) > 0:
        print(f"   ❌ PROBLEMA: Se perdieron {len(valores_perdidos)} registros")
        print(f"      Ejemplos perdidos: {list(valores_perdidos)[:5]}")
    
    if len(valores_nuevos) > 0:
        print(f"   ℹ️  INFO: {len(valores_nuevos)} registros nuevos en consolidado")
        print(f"      Ejemplos nuevos: {list(valores_nuevos)[:5]}")
    
    if len(valores_perdidos) == 0 and len(valores_nuevos) == 0:
        print(f"   ✅ PERFECTO: Consolidación preservó todos los registros")
    elif len(valores_perdidos) == 0:
        print(f"   ✅ BUENO: No se perdieron registros (hay algunos nuevos)")
    else:
        print(f"   ❌ ATENCIÓN: Se perdieron registros en la consolidación")

def generar_reporte_final(analisis_origen: dict, analisis_consolidado: dict):
    """Genera el reporte final del análisis."""
    
    print(f"\n📋 REPORTE FINAL DE ANÁLISIS")
    print("=" * 80)
    
    print(f"📅 Fecha de análisis: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔑 Clave primaria analizada: {CLAVE_PRIMARIA}")
    
    print(f"\n📊 RESUMEN CUANTITATIVO:")
    print(f"   ORIGEN:")
    print(f"      📁 Archivo: {analisis_origen['archivo']}")
    print(f"      📊 Registros: {analisis_origen['registros_totales']:,}")
    print(f"      🔑 Valores únicos clave: {analisis_origen['valores_unicos']:,}")
    print(f"      ❌ Nulos en clave: {analisis_origen['valores_nulos']:,}")
    print(f"      🔄 Duplicados: {analisis_origen['duplicados']:,}")
    
    print(f"\n   CONSOLIDADO:")
    print(f"      📁 Archivo: {analisis_consolidado['archivo']}")
    print(f"      📊 Registros: {analisis_consolidado['registros_totales']:,}")
    print(f"      🔑 Valores únicos clave: {analisis_consolidado['valores_unicos']:,}")
    print(f"      ❌ Nulos en clave: {analisis_consolidado['valores_nulos']:,}")
    print(f"      🔄 Duplicados: {analisis_consolidado['duplicados']:,}")
    
    # Calcular eficiencia de consolidación
    if analisis_origen['registros_totales'] > 0:
        eficiencia = (analisis_consolidado['registros_totales'] / analisis_origen['registros_totales']) * 100
        print(f"\n📈 EFICIENCIA DE CONSOLIDACIÓN:")
        print(f"   📊 Retención de datos: {eficiencia:.1f}%")
        
        if eficiencia >= 100:
            print(f"   ✅ EXCELENTE: Todos los datos preservados (o más por múltiples fuentes)")
        elif eficiencia >= 95:
            print(f"   ✅ MUY BUENO: Minimal pérdida de datos")
        elif eficiencia >= 90:
            print(f"   ⚠️  ACEPTABLE: Algunas pérdidas detectadas")
        else:
            print(f"   ❌ CRÍTICO: Pérdida significativa de datos")
    
    print(f"\n🎯 EVALUACIÓN CALIDAD CLAVE PRIMARIA:")
    
    # Evaluar calidad del origen
    if analisis_origen['clave_presente']:
        if analisis_origen['duplicados'] == 0 and analisis_origen['valores_nulos'] == 0:
            print(f"   ✅ ORIGEN: Clave primaria perfecta")
        elif analisis_origen['duplicados'] == 0:
            print(f"   ⚠️  ORIGEN: Clave única pero con algunos nulos")
        else:
            print(f"   ❌ ORIGEN: Problemas de unicidad en clave primaria")
    else:
        print(f"   ❌ ORIGEN: Clave primaria no encontrada")
    
    # Evaluar calidad del consolidado
    if analisis_consolidado['clave_presente']:
        if analisis_consolidado['duplicados'] == 0 and analisis_consolidado['valores_nulos'] == 0:
            print(f"   ✅ CONSOLIDADO: Clave primaria perfecta")
        elif analisis_consolidado['duplicados'] == 0:
            print(f"   ⚠️  CONSOLIDADO: Clave única pero con algunos nulos")
        else:
            print(f"   ❌ CONSOLIDADO: Problemas de unicidad en clave primaria")
    else:
        print(f"   ❌ CONSOLIDADO: Clave primaria no encontrada")

def main():
    """Función principal del análisis."""
    
    print("🔍 ANÁLISIS DATA ENGINEER: ORIGEN vs CONSOLIDADO MTX_WALLET_ORA")
    print("=" * 80)
    print(f"⏰ Inicio del análisis: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Objetivo: Verificar integridad de consolidación con clave {CLAVE_PRIMARIA}")
    
    # 1. Leer archivo origen
    print(f"\n📥 PASO 1: LEER ARCHIVO ORIGEN")
    df_origen = leer_parquet_desde_s3(S3_BRONZE_BUCKET, ARCHIVO_ORIGEN, "ARCHIVO ORIGEN")
    
    if df_origen.empty:
        print(f"❌ No se pudo leer el archivo origen. Terminando análisis.")
        return
    
    # 2. Leer archivo consolidado
    print(f"\n📥 PASO 2: LEER ARCHIVO CONSOLIDADO")
    df_consolidado = leer_parquet_desde_s3(S3_SILVER_BUCKET, ARCHIVO_CONSOLIDADO, "ARCHIVO CONSOLIDADO")
    
    if df_consolidado.empty:
        print(f"❌ No se pudo leer el archivo consolidado. Terminando análisis.")
        return
    
    # 3. Analizar estructuras
    print(f"\n📊 PASO 3: ANÁLISIS DE ESTRUCTURAS")
    comparar_estructuras(df_origen, df_consolidado)
    
    # 4. Analizar tipos de datos
    analizar_tipos_datos(df_origen, "ORIGEN")
    analizar_tipos_datos(df_consolidado, "CONSOLIDADO")
    
    # 5. Analizar clave primaria en origen
    print(f"\n🔑 PASO 4: ANÁLISIS DE CLAVE PRIMARIA")
    analisis_origen = analizar_clave_primaria(df_origen, "ORIGEN")
    analisis_consolidado = analizar_clave_primaria(df_consolidado, "CONSOLIDADO")
    
    # 6. Comparar datos de clave primaria
    print(f"\n🔍 PASO 5: COMPARACIÓN DE DATOS")
    comparar_datos_clave_primaria(df_origen, df_consolidado)
    
    # 7. Generar reporte final
    print(f"\n📋 PASO 6: REPORTE FINAL")
    generar_reporte_final(analisis_origen, analisis_consolidado)
    
    print(f"\n✅ ANÁLISIS COMPLETADO")
    print(f"⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
