#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para probar la nueva configuración de MTX_WALLET_ORA
"""

import pandas as pd
import sys
import os

# Agregar el directorio actual al path
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

from tabla_config_simple import detectar_clave_primaria_tabla, cargar_configuracion_simple, TABLA_KEYS_CONFIG
import logging

# Configurar logging detallado
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def probar_nueva_configuracion():
    """
    Prueba la nueva configuración de clave primaria para MTX_WALLET_ORA
    """
    print("🧪 PROBANDO NUEVA CONFIGURACIÓN DE MTX_WALLET_ORA")
    print("=" * 60)
    
    # Recargar configuración
    cargar_configuracion_simple()
    
    print("🔑 Configuración cargada:")
    for tabla, claves in TABLA_KEYS_CONFIG.items():
        if 'MTX_WALLET' in tabla:
            print(f"   {tabla}: {claves}")
    print()
    
    # Crear un DataFrame de muestra con las columnas que sabemos que existen
    print("📋 Creando DataFrame de muestra...")
    
    # Simular datos basados en lo que vimos antes
    df_muestra = pd.DataFrame({
        'WALLET_NUMBER': ['520101IND01', '107IND01', '105IND01', '520101IND01'],  # Con un duplicado
        'PIN_NUMBER': ['PIN1', 'PIN2', 'PIN3', 'PIN4'],
        'USER_ID': ['USER1', 'USER2', 'USER3', 'USER4'],
        'STATUS': ['ACTIVE', 'ACTIVE', 'INACTIVE', 'ACTIVE'],
        'DATA_LAKE_PARTITION_DATE': ['2025-05-29', '2025-05-29', '2025-05-29', '2025-05-30'],  # Fechas diferentes
        'RNUM': ['1', '2', '3', '4'],  # Posible clave única
        'data_lake_last_modified': ['2025-05-29 10:00:00', '2025-05-29 11:00:00', '2025-05-29 12:00:00', '2025-05-30 10:00:00']
    })
    
    print(f"✅ DataFrame creado con {len(df_muestra)} filas")
    print("📊 Datos de muestra:")
    print(df_muestra[['WALLET_NUMBER', 'DATA_LAKE_PARTITION_DATE', 'RNUM']].to_string(index=False))
    print()
    
    # Probar detección de clave primaria
    print("🔍 PROBANDO DETECCIÓN DE CLAVE PRIMARIA:")
    print("-" * 50)
    
    claves_detectadas = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_muestra)
    
    print()
    print("📋 RESULTADO:")
    print("-" * 20)
    if claves_detectadas:
        print(f"✅ Claves primarias detectadas: {claves_detectadas}")
        
        # Verificar si es realmente única
        if len(claves_detectadas) > 0:
            duplicados = df_muestra.duplicated(subset=claves_detectadas).sum()
            print(f"✅ Verificación de unicidad: {duplicados} duplicados encontrados")
            
            if duplicados == 0:
                print("🎯 ¡Clave primaria válida confirmada!")
            else:
                print("⚠️ La clave configurada aún tiene duplicados")
    else:
        print("❌ No se detectaron claves primarias válidas")
    
    print()
    
    # Probar otras combinaciones
    print("🔍 PROBANDO OTRAS COMBINACIONES:")
    print("-" * 40)
    
    combinaciones_probar = [
        ['RNUM'],
        ['WALLET_NUMBER', 'USER_ID'],
        ['WALLET_NUMBER', 'STATUS'],
    ]
    
    for combo in combinaciones_probar:
        if all(col in df_muestra.columns for col in combo):
            duplicados = df_muestra.duplicated(subset=combo).sum()
            print(f"📋 {' + '.join(combo)}: {duplicados} duplicados")
        else:
            print(f"❌ {' + '.join(combo)}: columnas faltantes")
    
    print()
    print("✅ Prueba completada")

if __name__ == "__main__":
    probar_nueva_configuracion()
