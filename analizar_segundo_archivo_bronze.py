#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analiza el segundo archivo Bronze encontrado para entender el origen de los 43 NaN adicionales
==================================================================================================

OBJETIVO: Analizar el segundo archivo Bronze Zone para determinar si contiene los 9 registros
que están causando la introducción de NaN values adicionales durante la consolidación.

Basado en hallazgos previos:
- Bronze Zone tiene 865 NaN values
- Silver Zone tiene 908 NaN values  
- Diferencia: +43 NaN values (consistente +9 por columna en múltiples columnas)

Archivo a analizar: PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040552_chunk_0.parquet
"""

import boto3
import pandas as pd
import numpy as np
import logging
from botocore.exceptions import ClientError

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Constantes
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"

def leer_parquet_desde_s3(bucket: str, key: str) -> pd.DataFrame:
    """Lee un archivo Parquet desde S3."""
    try:
        s3_path = f"s3://{bucket}/{key}"
        df = pd.read_parquet(s3_path)
        logging.info(f"✅ Archivo leído desde S3: {s3_path} ({len(df)} registros, {len(df.columns)} columnas)")
        return df
    except Exception as e:
        logging.error(f"❌ Error leyendo {bucket}/{key}: {str(e)}")
        return pd.DataFrame()

def contar_nan_por_columna(df: pd.DataFrame, nombre_archivo: str) -> dict:
    """Cuenta NaN values por columna en un DataFrame."""
    nan_counts = {}
    total_nan = 0
    
    print(f"\n📊 === ANÁLISIS NaN EN {nombre_archivo} ===")
    print(f"Total registros: {len(df)}")
    print(f"Total columnas: {len(df.columns)}")
    
    for col in df.columns:
        nan_count = df[col].isna().sum()
        if nan_count > 0:
            nan_counts[col] = nan_count
            total_nan += nan_count
            print(f"   {col}: {nan_count} NaN")
    
    print(f"📈 Total NaN values: {total_nan}")
    return nan_counts

def analizar_diferencias_nan(archivo1_nans: dict, archivo2_nans: dict, archivo1_name: str, archivo2_name: str):
    """Analiza las diferencias en NaN counts entre dos archivos."""
    print(f"\n🔍 === COMPARACIÓN NaN: {archivo1_name} vs {archivo2_name} ===")
    
    todas_columnas = set(archivo1_nans.keys()) | set(archivo2_nans.keys())
    diferencias = {}
    
    for col in sorted(todas_columnas):
        count1 = archivo1_nans.get(col, 0)
        count2 = archivo2_nans.get(col, 0)
        diferencia = count2 - count1
        
        if diferencia != 0:
            diferencias[col] = {
                'archivo1': count1,
                'archivo2': count2,
                'diferencia': diferencia
            }
            print(f"   {col}: {count1} → {count2} (Δ {diferencia:+d})")
    
    return diferencias

def main():
    """Función principal para analizar el segundo archivo Bronze."""
    print("🔬 === ANÁLISIS DEL SEGUNDO ARCHIVO BRONZE ===")
    print("Objetivo: Encontrar el origen de los 43 NaN adicionales\n")
    
    # Definir archivos a analizar
    archivos_bronze = {
        "Archivo1": "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/DATA/2025/05/30/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250530-040552_chunk_0.parquet",
        "Archivo2": "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/DATA/2025/06/02/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040552_chunk_0.parquet"
    }
    
    archivo_silver = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    
    # 1. Cargar y analizar el primer archivo Bronze (ya analizado previamente)
    print("1️⃣ Cargando primer archivo Bronze...")
    df_bronze1 = leer_parquet_desde_s3(S3_BRONZE_BUCKET, archivos_bronze["Archivo1"])
    nan_counts_bronze1 = contar_nan_por_columna(df_bronze1, "BRONZE ARCHIVO 1")
    
    # 2. Cargar y analizar el segundo archivo Bronze
    print("\n2️⃣ Cargando segundo archivo Bronze...")
    df_bronze2 = leer_parquet_desde_s3(S3_BRONZE_BUCKET, archivos_bronze["Archivo2"])
    nan_counts_bronze2 = contar_nan_por_columna(df_bronze2, "BRONZE ARCHIVO 2")
    
    # 3. Cargar archivo Silver consolidado
    print("\n3️⃣ Cargando archivo Silver consolidado...")
    df_silver = leer_parquet_desde_s3(S3_SILVER_BUCKET, archivo_silver)
    nan_counts_silver = contar_nan_por_columna(df_silver, "SILVER CONSOLIDADO")
    
    # 4. Calcular total NaN en ambos archivos Bronze
    total_nan_bronze1 = sum(nan_counts_bronze1.values())
    total_nan_bronze2 = sum(nan_counts_bronze2.values())
    total_nan_silver = sum(nan_counts_silver.values())
    total_nan_bronze_combinado = total_nan_bronze1 + total_nan_bronze2
    
    print(f"\n📊 === RESUMEN DE NaN TOTALES ===")
    print(f"Bronze Archivo 1: {total_nan_bronze1} NaN")
    print(f"Bronze Archivo 2: {total_nan_bronze2} NaN")
    print(f"Bronze Combinado: {total_nan_bronze_combinado} NaN")
    print(f"Silver Consolidado: {total_nan_silver} NaN")
    print(f"Diferencia: {total_nan_silver - total_nan_bronze_combinado:+d} NaN")
    
    # 5. Analizar diferencias específicas
    print(f"\n🔍 === ANÁLISIS DE DIFERENCIAS ===")
    
    # Comparar Bronze1 vs Bronze2
    diferencias_bronze = analizar_diferencias_nan(nan_counts_bronze1, nan_counts_bronze2, "Bronze1", "Bronze2")
    
    # Comparar Bronze combinado vs Silver
    # Simular Bronze combinado sumando NaN counts
    bronze_combinado_nans = {}
    todas_cols = set(nan_counts_bronze1.keys()) | set(nan_counts_bronze2.keys())
    for col in todas_cols:
        bronze_combinado_nans[col] = nan_counts_bronze1.get(col, 0) + nan_counts_bronze2.get(col, 0)
    
    diferencias_consolidacion = analizar_diferencias_nan(bronze_combinado_nans, nan_counts_silver, "Bronze Combinado", "Silver")
    
    # 6. Análisis específico de registros únicos
    print(f"\n🔬 === ANÁLISIS DE REGISTROS ÚNICOS ===")
    
    # Verificar si hay registros únicos en cada archivo
    if not df_bronze1.empty and not df_bronze2.empty:
        # Detectar clave primaria
        posibles_claves = ['WALLET_ID', 'ID', 'USER_ID']
        clave_primaria = None
        
        for clave in posibles_claves:
            if clave in df_bronze1.columns:
                clave_primaria = clave
                break
        
        if clave_primaria:
            ids_bronze1 = set(df_bronze1[clave_primaria].dropna())
            ids_bronze2 = set(df_bronze2[clave_primaria].dropna())
            
            print(f"Clave primaria detectada: {clave_primaria}")
            print(f"IDs únicos en Bronze1: {len(ids_bronze1)}")
            print(f"IDs únicos en Bronze2: {len(ids_bronze2)}")
            print(f"IDs comunes: {len(ids_bronze1 & ids_bronze2)}")
            print(f"IDs solo en Bronze1: {len(ids_bronze1 - ids_bronze2)}")
            print(f"IDs solo en Bronze2: {len(ids_bronze2 - ids_bronze1)}")
            
            # Verificar si hay registros con más NaN en Bronze2
            if len(ids_bronze2 - ids_bronze1) > 0:
                print(f"\n🚨 HALLAZGO: Bronze2 tiene {len(ids_bronze2 - ids_bronze1)} registros únicos que podrían explicar los NaN adicionales")
                
                # Mostrar algunos ejemplos
                ids_unicos_bronze2 = list(ids_bronze2 - ids_bronze1)[:5]
                print(f"Ejemplos de IDs únicos en Bronze2: {ids_unicos_bronze2}")
    
    # 7. Conclusiones y recomendaciones
    print(f"\n💡 === CONCLUSIONES ===")
    
    if total_nan_silver > total_nan_bronze_combinado:
        diferencia_total = total_nan_silver - total_nan_bronze_combinado
        print(f"🚨 CONFIRMADO: El proceso de consolidación introduce {diferencia_total} NaN adicionales")
        print(f"📍 Esto indica un problema en la función merge_incremental_puro o alinear_tipos_datos_puro")
    elif total_nan_silver == total_nan_bronze_combinado:
        print(f"✅ Los NaN totales coinciden - el problema podría estar en la distribución por columnas")
    else:
        print(f"❓ Los archivos Bronze tienen más NaN que Silver - revisar lógica de consolidación")
    
    print(f"\n📋 === RECOMENDACIONES ===")
    print(f"1. 🔧 Revisar función 'alinear_tipos_datos_puro' - posible conversión que introduce NaN")
    print(f"2. 🔧 Revisar función 'merge_incremental_puro' - posible problema en pd.concat")
    print(f"3. 🔧 Revisar función 'preservar_tipos_originales' - posible modificación no deseada")
    print(f"4. 🔬 Analizar registros específicos que tienen NaN en Silver pero no en Bronze")

if __name__ == "__main__":
    main()
