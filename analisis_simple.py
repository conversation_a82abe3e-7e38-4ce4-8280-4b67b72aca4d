#!/usr/bin/env python3

print("=== ANÁLISIS SIMPLE MTX_WALLET_ORA ===")
print("Iniciando análisis...")

try:
    import pandas as pd
    import boto3
    from io import BytesIO
    print("✅ Módulos importados")

    # Configuración
    bucket_bronze = "prd-datalake-bronze-zone-637423440311"
    bucket_silver = "prd-datalake-silver-zone-637423440311"
    archivo_origen = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-080250_chunk_0.parquet"
    archivo_consolidado = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
    
    print(f"📥 Leyendo archivo origen...")
    s3 = boto3.client('s3')
    
    # Leer origen
    response = s3.get_object(Bucket=bucket_bronze, Key=archivo_origen)
    df_origen = pd.read_parquet(BytesIO(response['Body'].read()))
    print(f"   Origen: {len(df_origen)} registros")
    
    # Leer consolidado
    response = s3.get_object(Bucket=bucket_silver, Key=archivo_consolidado)
    df_consolidado = pd.read_parquet(BytesIO(response['Body'].read()))
    print(f"   Consolidado: {len(df_consolidado)} registros")
    
    # Análisis
    clave = "WALLET_NUMBER"
    origen_unicos = df_origen[clave].nunique()
    consolidado_unicos = df_consolidado[clave].nunique()
    
    print(f"\n📊 RESULTADOS:")
    print(f"   Origen único {clave}: {origen_unicos}")
    print(f"   Consolidado único {clave}: {consolidado_unicos}")
    print(f"   Diferencia: {origen_unicos - consolidado_unicos}")
    
    if origen_unicos == consolidado_unicos:
        print(f"   ✅ NO HAY PÉRDIDA DE DATOS")
    else:
        print(f"   ❌ HAY PÉRDIDA DE DATOS")
        
        # Identificar perdidos
        origen_set = set(df_origen[clave])
        consolidado_set = set(df_consolidado[clave])
        perdidos = origen_set - consolidado_set
        
        print(f"   📋 WALLET_NUMBER perdidos: {len(perdidos)}")
        if len(perdidos) <= 10:
            for w in sorted(perdidos):
                print(f"       {w}")
        else:
            print(f"       {sorted(list(perdidos))[:5]}... y {len(perdidos)-5} más")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n=== FIN ANÁLISIS ===")
