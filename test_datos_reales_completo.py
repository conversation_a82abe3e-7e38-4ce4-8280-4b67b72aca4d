#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import boto3
import pandas as pd
from io import BytesIO
import sys
import warnings

warnings.filterwarnings('ignore')

# Importar función de normalización
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')
from app_raw_consolidado import normalizar_tipos_datos

def test_datos_reales_origen():
    """
    Prueba con datos reales del origen para verificar que los NULLs se manejan correctamente.
    """
    print("🔍 PROBANDO CON DATOS REALES DE ORIGEN")
    print("=" * 60)
    
    try:
        # Leer archivo de resumen real
        s3_client = boto3.client('s3')
        bucket_origen = "prd-datalake-bronze-zone-637423440311"
        resumen_key = "RESUMEN/IMG_FLOW_42/RESUMEN_IMG_FLOW_42.parquet"
        
        print(f"📂 Leyendo datos reales de: {bucket_origen}/{resumen_key}")
        
        response = s3_client.get_object(Bucket=bucket_origen, Key=resumen_key)
        buffer = BytesIO(response['Body'].read())
        df_resumen = pd.read_parquet(buffer)
        
        print(f"📊 Resumen leído: {len(df_resumen)} registros")
        print("Columnas:", list(df_resumen.columns))
        print()
        
        if df_resumen.empty:
            print("❌ No hay datos en el resumen")
            return False
        
        # Obtener información del primer archivo
        primer_registro = df_resumen.iloc[0]
        
        # Construir la ruta del archivo basándose en la estructura del resumen
        db_parent = primer_registro['db_parent']
        tabla_nombre = primer_registro['tabla_nombre']
        año = primer_registro['año']
        mes = primer_registro['mes'].zfill(2)
        dia = primer_registro['dia'].zfill(2)
        nombre_archivo = primer_registro['nombre_archivo']
        
        tabla_key = f"{db_parent}/{tabla_nombre}/{año}/{mes}/{dia}/{nombre_archivo}"
        
        print(f"📄 Leyendo archivo real: {tabla_key}")
        
        # Leer archivo real de datos
        response = s3_client.get_object(Bucket=bucket_origen, Key=tabla_key)
        buffer = BytesIO(response['Body'].read())
        df_original = pd.read_parquet(buffer)
        
        print(f"📊 Datos originales: {len(df_original)} filas, {len(df_original.columns)} columnas")
        print()
        
        # Mostrar muestra de datos originales
        print("📋 MUESTRA DE DATOS ORIGINALES (primeras 5 filas):")
        print("-" * 50)
        print(df_original.head(5))
        print()
        
        # Verificar tipos de datos originales
        print("📊 TIPOS DE DATOS ORIGINALES:")
        print("-" * 30)
        for col in df_original.columns:
            tipo = df_original[col].dtype
            nulos = df_original[col].isna().sum()
            print(f"   {col}: {tipo} (NaN: {nulos})")
        print()
        
        # Aplicar normalización
        print("🔧 APLICANDO NORMALIZACIÓN...")
        df_normalizado = normalizar_tipos_datos(df_original)
        print("✅ Normalización completada")
        print()
        
        # Mostrar datos normalizados
        print("📋 MUESTRA DE DATOS NORMALIZADOS (primeras 5 filas):")
        print("-" * 50)
        print(df_normalizado.head(5))
        print()
        
        # Verificar que no quedan valores NULL problemáticos
        print("🔍 VERIFICACIÓN DE LIMPIEZA DE NULL:")
        print("-" * 40)
        
        valores_null_total = 0
        for col in df_normalizado.columns:
            if df_normalizado[col].dtype == 'object':
                null_count = df_normalizado[col].isin([
                    'null', 'NULL', '<null>', 'None', 'NONE', '<None>',
                    'nan', 'NaN', 'nil', 'NIL', 'undefined', 'N/A', 'n/a'
                ]).sum()
                
                empty_count = (df_normalizado[col] == '').sum()
                valores_null_total += null_count
                
                print(f"   {col}: NULL={null_count}, Vacías={empty_count}")
                
                if null_count > 0:
                    valores_problematicos = df_normalizado[col][
                        df_normalizado[col].isin([
                            'null', 'NULL', '<null>', 'None', 'NONE', '<None>',
                            'nan', 'NaN', 'nil', 'NIL', 'undefined', 'N/A', 'n/a'
                        ])
                    ].unique()
                    print(f"      ❌ Valores problemáticos: {valores_problematicos}")
                else:
                    print(f"      ✅ Limpio")
        
        print()
        
        # Verificar valores categóricos preservados
        print("📋 VALORES CATEGÓRICOS PRESERVADOS:")
        print("-" * 40)
        
        for col in df_normalizado.columns:
            if df_normalizado[col].dtype == 'object':
                valores_unicos = df_normalizado[col].unique()
                # Mostrar valores que no son cadenas vacías
                valores_no_vacios = [v for v in valores_unicos if v != '']
                if valores_no_vacios:
                    print(f"   {col}: {valores_no_vacios[:5]}{'...' if len(valores_no_vacios) > 5 else ''}")
        
        print()
        print("🎯 RESULTADO DE LA PRUEBA:")
        print("=" * 60)
        
        if valores_null_total == 0:
            print("✅ ÉXITO TOTAL: Datos reales procesados correctamente")
            print("✅ Todos los valores NULL convertidos a cadenas vacías")
            print("✅ Valores categóricos preservados")
            print("✅ Sistema listo para cualquier tabla")
            return True
        else:
            print(f"❌ FALLO: Se encontraron {valores_null_total} valores NULL sin procesar")
            return False
        
    except Exception as e:
        print(f"❌ Error en la prueba: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    exito = test_datos_reales_origen()
    
    if exito:
        print("\n🚀 SISTEMA COMPLETAMENTE FUNCIONAL CON DATOS REALES")
        print("🎉 ¡Listo para procesar cualquier tabla sin pérdida de datos!")
    else:
        print("\n⚠️  NECESITA AJUSTES")
