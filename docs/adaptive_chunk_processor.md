# Procesador Adaptativo de Chunks

## Descripciu00f3n

El `AdaptiveChunkProcessor` es una soluciu00f3n implementada para optimizar el rendimiento del modo estu00e1ndar del ETL, evitando problemas de memoria al procesar grandes volu00famenes de datos sin modificar la lu00f3gica existente.

## Problema que resuelve

El modo estu00e1ndar utiliza `cursor.fetchall()` para cargar todos los resultados de una consulta en memoria, lo que puede causar:

- Errores de memoria insuficiente (`thread result found`)
- Alto consumo de RAM (como se ve en los logs: `Tendencia de memoria alta detectada: ~1097.60MB`)
- Tiempos de procesamiento largos
- Inestabilidad en el sistema

## Soluciu00f3n

El procesador adaptativo:

1. Reemplaza `fetchall()` con `fetchmany()` con tamau00f1o de chunk adaptativo
2. Monitorea el uso de memoria en tiempo real
3. Ajusta el tamau00f1o del chunk automu00e1ticamente segu00fan el uso de memoria
4. Mantiene la misma interfaz para no afectar la lu00f3gica existente

## Paru00e1metros configurables

- `initial_chunk_size`: Tamau00f1o inicial del chunk (por defecto: 50,000 filas)
- `min_size`: Tamau00f1o mu00ednimo permitido (por defecto: 5,000 filas)
- `max_size`: Tamau00f1o mu00e1ximo permitido (por defecto: 200,000 filas)
- `memory_threshold`: Umbral de porcentaje de memoria para reducir tamau00f1o (por defecto: 70%)
- `memory_check_frequency`: Cada cuu00e1ntos chunks verificar el uso de memoria (por defecto: 5)

## Funcionamiento

1. **Inicializaciu00f3n**: Comienza con un tamau00f1o de chunk conservador (50,000 filas)
2. **Monitoreo**: Verifica el uso de memoria periu00f3dicamente
3. **Ajuste dinu00e1mico**:
   - Si el uso de memoria supera el umbral (70%), reduce el tamau00f1o del chunk en un 30%
   - Si el uso de memoria estu00e1 por debajo del umbral (50%), aumenta el tamau00f1o del chunk en un 30%
   - Nunca excede los lu00edmites mu00ednimo (5,000) y mu00e1ximo (200,000)
4. **Manejo de errores**: Si ocurre un error, reduce el tamau00f1o del chunk a la mitad y reintenta
5. **Liberaciu00f3n de memoria**: Ejecuta el recolector de basura periu00f3dicamente para liberar memoria

## Estadu00edsticas

El procesador mantiene estadu00edsticas que se registran en los logs:

- `total_rows`: Total de filas procesadas
- `chunks_processed`: Nu00famero de chunks procesados
- `memory_adjustments`: Nu00famero de veces que se ajustu00f3 el tamau00f1o del chunk
- `final_chunk_size`: Tamau00f1o final del chunk

## Integraciu00f3n

El procesador se integra de forma transparente en el modo estu00e1ndar, sin afectar la lu00f3gica existente:

```python
# Antes
resultados = cursor.fetchall()

# Despuu00e9s
from .adaptive_chunk_processor import AdaptiveChunkProcessor
processor = AdaptiveChunkProcessor(cursor)
resultados = processor.fetchall_in_chunks()
```

## Ventajas

- **Sin cambios en la lu00f3gica existente**: Mantiene la misma interfaz y comportamiento
- **Optimizaciu00f3n automu00e1tica**: Se adapta a las condiciones del sistema en tiempo real
- **Robustez**: Maneja errores y reintentos automu00e1ticamente
- **Eficiencia de memoria**: Evita cargar todos los datos en memoria a la vez
- **Transparencia**: Proporciona estadu00edsticas detalladas sobre el procesamiento

## Recomendaciones

- Utilizar este procesador para tablas grandes donde el modo ru00e1pido no estu00e1 habilitado
- Monitorear los logs para ver las estadu00edsticas y ajustar los paru00e1metros si es necesario
- Considerar habilitar el modo ru00e1pido para tablas muy grandes (>10 millones de filas)
