"""
==========================================
 Módulo ETL
 Autor: G<PERSON><PERSON>los Cardenas Galarza
 Fecha: 2024-03-07
==========================================
"""

Flow_ETL_Landing_ED/                            # 🚀 Proyecto ETL para extracción y carga en S3 y bases de datos
├── 📂 config/                                  # Configuración global del proyecto
│   ├── 📄 s3_db_config.ini                     # Credenciales y conexión a S3 y BD
│   ├── 📄 s3_output_config.ini                 # Configuración de almacenamiento en S3
│   ├── 📄 s3_sql_config.ini                    # Consultas SQL específicas para extracción a S3
├── 📂 docs/                                    # Documentación técnica y guías de uso
├── 📂 logs/                                    # Archivos de registro y monitoreo
│   ├── 📄 s3_log_config.ini                    # Configuración de logging en S3
├── 📂 src/                                     # Código fuente del proyecto
│   ├── 📂 etl/                                 # 🔄 Módulo principal de ETL
│   │   ├── 📂 driver/                          # Drivers JDBC para conexión con bases de datos
│   │   │   ├── 📄 mysql-connector-j-8.2.0.jar  # Driver para MySQL
│   │   │   ├── 📄 postgresql-42.6.0.jar        # Driver para PostgreSQL
│   │   ├── 📄 __init__.py                      # Inicialización del módulo
│   │   ├── 📄 config_loader.py                 # Carga y validación de configuraciones
│   │   ├── 📄 data_transformer.py              # Normalización y transformación de datos
│   │   ├── 📄 db_manager.py                    # Gestión de conexiones y transacciones en BD
│   │   ├── 📄 error_handler.py                 # Registro y manejo de errores en el ETL
│   │   ├── 📄 etl_runner.py                    # Módulo que ejecuta todo el flujo ETL
│   │   ├── 📄 executor.py                      # Gestión de ejecución de tareas y procesos
│   │   ├── 📄 fast_mode_direct.py              # Implementación del ETL en modo rápido
│   │   ├── 📄 fast_mode_manager.py             # Gestión y validación del modo rápido
│   │   ├── 📄 main.py                          # Punto de entrada del sistema ETL
│   │   ├── 📄 query_builder.py                 # Generador dinámico de consultas SQL
│   │   ├── 📄 rate_limiter.py                  # Control de tasa de procesamiento de datos
│   │   ├── 📄 resumen_manager.py               # Generación de resúmenes de ejecución
│   │   ├── 📄 s3_log_handler.py                # Registro de logs en S3
│   │   ├── 📄 s3_manager.py                    # Manejo de operaciones con Amazon S3
│   ├── 📂 utils/                               # 🔧 Módulos auxiliares y herramientas
│   │   ├── 📄 aws_secrets.py                   # Manejo seguro de credenciales AWS
│   ├── 📂 tests/                               # 🧪 Pruebas unitarias y de integración
│   │   ├── 📄 test_db_connections.py           # Prueba de conexión general a bases de datos
│   │   ├── 📄 test_mysql_connection.py         # Prueba específica para MySQL
│   │   ├── 📄 test_postgres_connection.py      # Prueba específica para PostgreSQL
├── 📄 app_landing.py                           # Script principal de ejecución del ETL
├── 📄 Dockerfile                               # Configuración para contenedores Docker
├── 📄 etl_process.log                          # Registro detallado del proceso ETL
├── 📄 flow.js                                  # Script de automatización en JavaScript
├── 📄 pytest.ini                               # Configuración de pruebas con pytest
├── 📄 README_MODO_RAPIDO.md                    # Guía específica para el modo rápido
├── 📄 README.md                                # Documentación principal del proyecto
├── 📄 requirements.txt                         # Dependencias necesarias para el ETL
└── 📄 test.js                                  # Script JavaScript para pruebas