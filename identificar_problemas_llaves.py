#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para identificar problemas específicos con la lectura de llaves primarias
"""

import pandas as pd
import logging
from tabla_config_simple import detectar_clave_primaria_tabla, validar_clave_primaria

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_problemas_comunes():
    """Identifica los problemas más comunes con llaves primarias"""
    
    print("🔍 IDENTIFICANDO PROBLEMAS COMUNES CON LLAVES PRIMARIAS")
    print("=" * 60)
    
    # Problema 1: Columna clave no existe en los datos
    print("\n1️⃣ PROBLEMA: Columna clave configurada no existe en datos")
    df_sin_wallet = pd.DataFrame({
        'USER_ID': [1, 2, 3],
        'BALANCE': [100, 200, 300],
        'STATUS': ['A', 'A', 'I']
        # Nota: WALLET_NUMBER no está aquí
    })
    
    print(f"   Columnas disponibles: {list(df_sin_wallet.columns)}")
    claves = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_sin_wallet)
    print(f"   Claves detectadas: {claves}")
    
    # Problema 2: Valores nulos en clave primaria
    print("\n2️⃣ PROBLEMA: Valores nulos en clave primaria")
    df_con_nulos = pd.DataFrame({
        'WALLET_NUMBER': ['W001', None, 'W003'],
        'USER_ID': [1, 2, 3],
        'BALANCE': [100, 200, 300]
    })
    
    print(f"   Valores clave: {df_con_nulos['WALLET_NUMBER'].tolist()}")
    claves = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_con_nulos)
    if claves:
        valido = validar_clave_primaria(df_con_nulos, claves)
        print(f"   ¿Es válida?: {valido}")
    
    # Problema 3: Valores duplicados en clave primaria
    print("\n3️⃣ PROBLEMA: Valores duplicados en clave primaria")
    df_con_duplicados = pd.DataFrame({
        'WALLET_NUMBER': ['W001', 'W002', 'W001'],  # W001 duplicado
        'USER_ID': [1, 2, 3],
        'BALANCE': [100, 200, 300]
    })
    
    print(f"   Valores clave: {df_con_duplicados['WALLET_NUMBER'].tolist()}")
    claves = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_con_duplicados)
    if claves:
        valido = validar_clave_primaria(df_con_duplicados, claves)
        print(f"   ¿Es válida?: {valido}")
    
    # Problema 4: Valores vacíos en clave primaria
    print("\n4️⃣ PROBLEMA: Valores vacíos en clave primaria")
    df_con_vacios = pd.DataFrame({
        'WALLET_NUMBER': ['W001', '', 'W003'],  # Valor vacío
        'USER_ID': [1, 2, 3],
        'BALANCE': [100, 200, 300]
    })
    
    print(f"   Valores clave: {df_con_vacios['WALLET_NUMBER'].tolist()}")
    claves = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_con_vacios)
    if claves:
        valido = validar_clave_primaria(df_con_vacios, claves)
        print(f"   ¿Es válida?: {valido}")
    
    # Problema 5: Tabla no configurada
    print("\n5️⃣ PROBLEMA: Tabla no configurada en .ini")
    df_tabla_nueva = pd.DataFrame({
        'ID': [1, 2, 3],
        'NAME': ['A', 'B', 'C'],
        'STATUS': ['ACTIVE', 'ACTIVE', 'INACTIVE']
    })
    
    claves = detectar_clave_primaria_tabla("TABLA_NO_CONFIGURADA", df_tabla_nueva)
    print(f"   Claves detectadas (fallback): {claves}")
    
    # Caso exitoso para comparar
    print("\n✅ CASO EXITOSO: Datos correctos")
    df_correcto = pd.DataFrame({
        'WALLET_NUMBER': ['W001', 'W002', 'W003'],
        'USER_ID': [1, 2, 3],
        'BALANCE': [100, 200, 300]
    })
    
    claves = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_correcto)
    if claves:
        valido = validar_clave_primaria(df_correcto, claves)
        print(f"   Claves detectadas: {claves}")
        print(f"   ¿Es válida?: {valido}")
    
    print("\n" + "=" * 60)
    print("💡 RESUMEN DE PROBLEMAS POSIBLES:")
    print("   1. Columna configurada no existe en datos reales")
    print("   2. Valores nulos en columnas de clave primaria")
    print("   3. Valores duplicados en clave primaria")
    print("   4. Valores vacíos ('') en clave primaria")
    print("   5. Tabla no configurada en .ini")
    print("\n🔧 SOLUCIONES:")
    print("   - Verificar que las columnas configuradas existan en tus datos")
    print("   - Limpiar datos antes de consolidar (eliminar nulos/vacíos)")
    print("   - Verificar unicidad de claves antes de procesar")
    print("   - Agregar configuración para tablas nuevas")

if __name__ == "__main__":
    test_problemas_comunes()
