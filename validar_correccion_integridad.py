#!/usr/bin/env python3
"""
Script para probar la corrección de integridad de datos en la función alinear_tipos_datos_puro()
Este script valida que la corrección resuelva el problema de introducción de NaN adicionales.
"""

import pandas as pd
import numpy as np
import logging
import sys
import os

# Agregar el directorio actual al path para importar el módulo
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Importar la función corregida
from app_raw_consolidado_puro import alinear_tipos_datos_puro

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def crear_datos_test():
    """Crea datos de prueba que replican el problema real de MTX_WALLET_ORA"""
    
    # DataFrame existente (primer archivo Bronze - sin columnas problemáticas)
    df_existente = pd.DataFrame({
        'WALLET_ID': [1, 2, 3, 4, 5],
        'USER_ID': ['U001', 'U002', 'U003', 'U004', 'U005'],
        'BALANCE': [100.50, 200.00, 150.75, 75.25, 300.00],
        'STATUS': ['ACTIVE', 'INACTIVE', 'ACTIVE', 'PENDING', 'ACTIVE'],
        'PIN_NUMBER': ['1234', None, '5678', '9999', '1111'],  # 1 NaN original
        'LAST_UPDATE': pd.to_datetime(['2025-01-01', '2025-01-02', None, '2025-01-03', '2025-01-04']),  # 1 NaN original
        'CREATED_DATE': pd.to_datetime(['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'])
    })
    
    # DataFrame nuevos (segundo archivo Bronze - con columnas adicionales que causan el problema)
    df_nuevos = pd.DataFrame({
        'WALLET_ID': [6, 7, 8],
        'USER_ID': ['U006', 'U007', 'U008'],
        'BALANCE': [125.75, 180.50, 90.25],
        'STATUS': ['ACTIVE', 'INACTIVE', 'PENDING'],
        'PIN_NUMBER': ['2222', '3333', None],  # 1 NaN original
        'LAST_UPDATE': pd.to_datetime(['2025-01-05', '2025-01-06', '2025-01-07']),
        'CREATED_DATE': pd.to_datetime(['2024-01-06', '2024-01-07', '2024-01-08']),
        # COLUMNAS ADICIONALES que causan el problema (estas son las que se agregan al existente)
        'PIN_MODIFIED_ON': pd.to_datetime(['2025-01-01', '2025-01-02', '2025-01-03']),  # datetime
        'PIN_STATUS': ['ENABLED', 'DISABLED', 'ENABLED'],  # string
        'PIN_REQUIRED': [True, False, True],  # boolean
        'VALID_FROM_DATE': pd.to_datetime(['2024-12-01', '2024-12-02', '2024-12-03']),  # datetime
        'EXPIRY_DATE': pd.to_datetime(['2025-12-01', '2025-12-02', '2025-12-03']),  # datetime
        'LAST_TRANSFER_TYPE': ['DEPOSIT', 'WITHDRAWAL', 'TRANSFER'],  # string
        'DESCRIPTION': ['Wallet 6', 'Wallet 7', 'Wallet 8']  # string
    })
    
    return df_existente, df_nuevos

def contar_nan_por_columna(df: pd.DataFrame, nombre: str) -> Dict[str, int]:
    """Cuenta NaN por columna y retorna un diccionario"""
    nan_count = {}
    for col in df.columns:
        nan_count[col] = df[col].isna().sum()
    
    total_nan = sum(nan_count.values())
    logging.info(f"📊 {nombre}: {total_nan} NaN totales")
    
    # Mostrar solo columnas con NaN
    for col, count in nan_count.items():
        if count > 0:
            logging.info(f"   {col}: {count} NaN")
    
    return nan_count

def probar_funcion_corregida():
    """Prueba la función corregida y valida que no introduzca NaN adicionales"""
    
    logging.info("🧪 === PROBANDO FUNCIÓN CORREGIDA ===")
    
    # Crear datos de prueba
    df_existente, df_nuevos = crear_datos_test()
    
    # Contar NaN iniciales
    logging.info("\n📋 ESTADO INICIAL:")
    nan_existente_inicial = contar_nan_por_columna(df_existente, "DF_EXISTENTE")
    nan_nuevos_inicial = contar_nan_por_columna(df_nuevos, "DF_NUEVOS")
    
    total_nan_inicial = sum(nan_existente_inicial.values()) + sum(nan_nuevos_inicial.values())
    logging.info(f"🎯 TOTAL NaN INICIAL: {total_nan_inicial}")
    
    # Ejecutar función corregida
    logging.info("\n🔧 EJECUTANDO FUNCIÓN CORREGIDA:")
    df_existente_alineado, df_nuevos_alineado = alinear_tipos_datos_puro(df_existente, df_nuevos)
    
    # Contar NaN después de la alineación
    logging.info("\n📋 ESTADO DESPUÉS DE ALINEACIÓN:")
    nan_existente_final = contar_nan_por_columna(df_existente_alineado, "DF_EXISTENTE_ALINEADO")
    nan_nuevos_final = contar_nan_por_columna(df_nuevos_alineado, "DF_NUEVOS_ALINEADO")
    
    total_nan_final = sum(nan_existente_final.values()) + sum(nan_nuevos_final.values())
    logging.info(f"🎯 TOTAL NaN FINAL: {total_nan_final}")
    
    # Calcular diferencia
    diferencia_nan = total_nan_final - total_nan_inicial
    logging.info(f"\n🔍 DIFERENCIA NaN: {diferencia_nan:+d}")
    
    if diferencia_nan == 0:
        logging.info("✅ ÉXITO: La función corregida NO introduce NaN adicionales")
        return True
    else:
        logging.error(f"❌ PROBLEMA: La función introduce {diferencia_nan} NaN adicionales")
        
        # Análisis detallado de qué columnas ganaron NaN
        logging.info("\n🔍 ANÁLISIS DETALLADO POR COLUMNAS:")
        for col in df_existente_alineado.columns:
            nan_inicial = nan_existente_inicial.get(col, 0)
            nan_final = nan_existente_final.get(col, 0)
            if nan_final > nan_inicial:
                logging.error(f"   ❌ {col}: +{nan_final - nan_inicial} NaN adicionales")
        
        return False

def simular_proceso_completo():
    """Simula el proceso completo de consolidación con la función corregida"""
    
    logging.info("\n🔄 === SIMULANDO PROCESO COMPLETO DE CONSOLIDACIÓN ===")
    
    # Crear datos de prueba
    df_existente, df_nuevos = crear_datos_test()
    
    # Paso 1: Alinear tipos (función corregida)
    df_existente_alineado, df_nuevos_alineado = alinear_tipos_datos_puro(df_existente, df_nuevos)
    
    # Paso 2: Simular concat (como en merge_incremental_puro)
    logging.info("🔗 Simulando pd.concat()...")
    df_consolidado = pd.concat([df_existente_alineado, df_nuevos_alineado], ignore_index=True)
    
    # Análisis final
    nan_original_total = df_existente.isna().sum().sum() + df_nuevos.isna().sum().sum()
    nan_consolidado_total = df_consolidado.isna().sum().sum()
    
    logging.info(f"\n🎯 RESULTADO FINAL:")
    logging.info(f"   NaN originales: {nan_original_total}")
    logging.info(f"   NaN consolidado: {nan_consolidado_total}")
    logging.info(f"   Diferencia: {nan_consolidado_total - nan_original_total:+d}")
    
    if nan_consolidado_total == nan_original_total:
        logging.info("✅ ÉXITO TOTAL: El proceso completo preserva la integridad de datos")
        return True
    else:
        logging.error("❌ PROBLEMA: El proceso introduce cambios en NaN")
        return False

def main():
    """Función principal de pruebas"""
    logging.info("🚀 INICIANDO PRUEBAS DE VALIDACIÓN DE CORRECCIÓN")
    
    # Prueba 1: Función corregida
    exito_funcion = probar_funcion_corregida()
    
    # Prueba 2: Proceso completo
    exito_proceso = simular_proceso_completo()
    
    # Resultado final
    logging.info("\n" + "="*80)
    logging.info("🏁 RESULTADO FINAL DE VALIDACIÓN")
    
    if exito_funcion and exito_proceso:
        logging.info("✅ VALIDACIÓN EXITOSA: La corrección resuelve el problema de integridad de datos")
        logging.info("🎯 La función alinear_tipos_datos_puro() ya NO introduce NaN adicionales")
        logging.info("🎯 El proceso de consolidación preserva la integridad de datos")
        return True
    else:
        logging.error("❌ VALIDACIÓN FALLIDA: La corrección necesita ajustes adicionales")
        return False

if __name__ == "__main__":
    exito = main()
    sys.exit(0 if exito else 1)
