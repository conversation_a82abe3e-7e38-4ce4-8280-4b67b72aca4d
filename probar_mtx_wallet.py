#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para probar la detección de clave primaria específicamente para MTX_WALLET_ORA
"""

import pandas as pd
import sys
import os

# Agregar el directorio actual al path
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

from tabla_config_simple import detectar_clave_primaria_tabla, cargar_configuracion_simple
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def probar_mtx_wallet():
    """
    Prueba la detección de clave primaria para MTX_WALLET_ORA
    """
    print("🧪 PRUEBA DE DETECCIÓN DE CLAVE PRIMARIA PARA MTX_WALLET_ORA")
    print("=" * 70)
    
    # Recargar configuración
    cargar_configuracion_simple()
    
    # Ubicación del archivo consolidado
    s3_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado.parquet"
    
    print(f"📁 Leyendo archivo: {s3_path}")
    
    try:
        # Leer solo una pequeña muestra para probar
        df_sample = pd.read_parquet(s3_path, engine='pyarrow')
        
        print(f"✅ Archivo leído exitosamente:")
        print(f"   📊 Filas: {len(df_sample):,}")
        print(f"   📋 Columnas: {len(df_sample.columns)}")
        print()
        
        print("📋 COLUMNAS ENCONTRADAS:")
        print("-" * 40)
        for i, col in enumerate(df_sample.columns, 1):
            print(f"{i:2d}. {col} ({df_sample[col].dtype})")
        print()
        
        # Probar detección de clave primaria
        print("🔍 PROBANDO DETECCIÓN DE CLAVE PRIMARIA:")
        print("-" * 50)
        
        claves_detectadas = detectar_clave_primaria_tabla("MTX_WALLET_ORA", df_sample)
        
        print()
        print("📋 RESULTADO:")
        print("-" * 20)
        if claves_detectadas:
            print(f"✅ Claves primarias detectadas: {claves_detectadas}")
        else:
            print("❌ No se detectaron claves primarias válidas")
        
        print()
        
        # Análisis adicional para columnas WALLET
        print("🔍 ANÁLISIS DE COLUMNAS WALLET:")
        print("-" * 40)
        wallet_cols = [col for col in df_sample.columns if 'WALLET' in col.upper()]
        
        if wallet_cols:
            for col in wallet_cols:
                unique_count = df_sample[col].nunique()
                total_count = len(df_sample)
                null_count = df_sample[col].isnull().sum()
                
                print(f"📋 {col}:")
                print(f"   Tipo: {df_sample[col].dtype}")
                print(f"   Únicos: {unique_count:,}/{total_count:,}")
                print(f"   Nulos: {null_count:,}")
                print(f"   Es clave primaria: {unique_count == total_count and null_count == 0}")
                
                # Muestra de valores
                sample = df_sample[col].dropna().head(5).tolist()
                print(f"   Muestra: {sample}")
                print()
        else:
            print("❌ No se encontraron columnas con 'WALLET'")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    probar_mtx_wallet()
