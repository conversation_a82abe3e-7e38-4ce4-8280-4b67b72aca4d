#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de prueba simple para verificar la corrección de claves primarias.
"""

import pandas as pd
import numpy as np
import sys
import os

# Agregar el directorio actual al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from tabla_config_simple import detectar_clave_primaria_tabla, validar_clave_primaria
    from app_raw_consolidado import normalizar_tipos_datos
    print("✅ Módulos importados exitosamente")
except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    sys.exit(1)

def main():
    print("🧪 PRUEBA SIMPLE DE CORRECCIÓN DE CLAVES PRIMARIAS")
    print("=" * 50)
    
    # Crear DataFrame de prueba
    data = {
        'WALLET_NUMBER': ['WAL001', 'WAL002', None, 'WAL004', 'None', 'nan'],
        'USER_ID': ['USR001', 'USR002', 'USR003', 'USR004', 'USR005', 'USR006'],
        'BALANCE': [100.0, 200.0, 300.0, 400.0, 500.0, 600.0]
    }
    
    df = pd.DataFrame(data)
    print(f"\n📊 DataFrame creado: {len(df)} registros")
    print("Datos originales:")
    print(df['WALLET_NUMBER'].tolist())
    
    # Detectar clave primaria
    print(f"\n🔍 Detectando clave primaria...")
    claves = detectar_clave_primaria_tabla('MTX_WALLET_ORA', df)
    print(f"Claves detectadas: {claves}")
    
    # Aplicar normalización nueva
    print(f"\n✅ Aplicando normalización corregida...")
    df_normalizado = normalizar_tipos_datos(df, 'MTX_WALLET_ORA')
    print("Datos después de normalización:")
    print(df_normalizado['WALLET_NUMBER'].tolist())
    
    # Verificar valores vacíos
    vacios = (df_normalizado['WALLET_NUMBER'] == '').sum()
    nulos = df_normalizado['WALLET_NUMBER'].isna().sum()
    print(f"\n📊 Resultados:")
    print(f"Valores vacíos (''): {vacios}")
    print(f"Valores nulos (NaN): {nulos}")
    
    # Validar clave primaria
    es_valida = validar_clave_primaria(df_normalizado, claves)
    print(f"Validación de clave primaria: {es_valida}")
    
    if vacios == 0 and es_valida:
        print("\n✅ ¡CORRECCIÓN EXITOSA! No hay valores vacíos en la clave primaria.")
    else:
        print(f"\n❌ Problema detectado: {vacios} valores vacíos encontrados.")

if __name__ == "__main__":
    main()
