# __init__.py vacío (o con inicializaciones necesarias).

# Módulos del paquete ETL

# Importar componentes principales
from .main import main
from .etl_runner import procesar_configuracion, ejecutar_query_wrapper
from .db_manager import DBManager
from .query_builder import construir_query
from .config_loader import cargar_configuracion, cargar_rango_config
from .data_transformer import limpiar_dataframe, forzar_tipos_dataframe
from .s3_manager import upload_df_chunk_to_s3, gestionar_archivos_s3
from .s3_log_handler import S3LogHandler, configure_s3_logging
from .adaptive_chunk_processor import AdaptiveChunkProcessor

# Intentar importar componentes opcionales
try:
    from .fast_mode_manager import FastModeManager
except ImportError:
    pass