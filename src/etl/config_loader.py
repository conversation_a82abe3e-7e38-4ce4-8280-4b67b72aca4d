import logging
import configparser
import io
import boto3
import os
from botocore.config import Config

def _load_s3_config():
    config = configparser.ConfigParser()
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

    try:
        config.read(os.path.join(base_dir, 'config/s3_db_config.ini'))
        bucket = config.get('CONFIGS_DB', 'bucket')
        prefix = config.get('CONFIGS_DB', 'prefix')

        config.read(os.path.join(base_dir, 'config/s3_sql_config.ini'))
        sql_prefix = config.get('CONFIGS_SQL', 'prefix')

        return bucket, prefix, sql_prefix
    except Exception as e:
        logging.error(f"Error cargando configuraciones S3: {e}")
        raise

S3_BUCKET, S3_PREFIX, S3_SQL_PATH = _load_s3_config()
S3_CONFIG_PATH = S3_PREFIX

# S3 client optimizado
S3_CLIENT = boto3.client('s3', config=Config(
    max_pool_connections=50,
    retries={'max_attempts': 3, 'mode': 'standard'}
))

def leer_archivo_s3(bucket: str, key: str) -> str:
    """
    Lee un archivo desde S3 y retorna su contenido
    """
    try:
        # Limpiar rutas duplicadas
        key = key.replace('ETL_CONFIG/CONFIGS/ETL_CONFIG/CONFIGS/', 'ETL_CONFIG/CONFIGS/')
        key = key.replace('///', '/')
        key = key.replace('//', '/')

        logging.info(f"Intentando cargar archivo desde S3: s3://{bucket}/{key}")
        response = S3_CLIENT.get_object(Bucket=bucket, Key=key)
        contenido = response['Body'].read().decode('utf-8')
        logging.info(f"Archivo cargado exitosamente desde S3: s3://{bucket}/{key}")
        return contenido
    except Exception as e:
        logging.error(f"Error CRÍTICO leyendo archivo S3 s3://{bucket}/{key}: {e}")
        raise FileNotFoundError(f"No se pudo cargar el archivo desde S3: s3://{bucket}/{key}")

def cargar_configuracion(archivo: str) -> configparser.ConfigParser:
    """Carga la configuración ÚNICAMENTE desde S3"""
    print("\n=== Iniciando carga de configuración ===")

    # Eliminar prefijos duplicados si existen
    if archivo.startswith(S3_PREFIX):
        archivo = archivo.replace(S3_PREFIX, '')
    if archivo.startswith('CONFIGS/'):
        archivo = archivo.replace('CONFIGS/', '')

    # Leer archivo de configuración principal desde S3
    config_content = leer_archivo_s3(S3_BUCKET, f"{S3_CONFIG_PATH}{archivo}")
    config = configparser.ConfigParser()
    config.read_string(config_content)
    print(f"✓ Archivo de configuración principal cargado desde S3: {archivo}")

    # Leer archivo de conexiones desde S3
    conexion_paths = [
        f"{S3_CONFIG_PATH}conexion_settings.ini",
        "conexion_settings.ini"
    ]

    conexion_config = configparser.ConfigParser()
    conexion_content = None

    for conexion_path in conexion_paths:
        try:
            conexion_content = leer_archivo_s3(S3_BUCKET, conexion_path)
            conexion_config.read_string(conexion_content)
            print(f"✓ Archivo de configuración de conexiones cargado desde S3: {conexion_path}")
            break
        except Exception as e:
            logging.error(f"Error cargando {conexion_path} desde S3: {e}")

    if not conexion_content:
        raise FileNotFoundError("No se encontró ningún archivo de configuración de conexiones en S3")

    # Validar que existan las secciones necesarias
    if 'mysql' not in conexion_config or 'postgres' not in conexion_config:
        raise ValueError("El archivo conexion_settings.ini debe contener las secciones 'mysql' y 'postgres'")

    # Guardar las configuraciones de conexión de manera genérica
    # Primero, asegurarse de que las secciones básicas existan
    config['mysql_connection'] = conexion_config['mysql']
    config['postgres_connection'] = conexion_config['postgres']

    # Agregar configuración de Oracle si existe
    if 'oracle' in conexion_config:
        config['oracle_connection'] = conexion_config['oracle']
        print(f"✓ Configuración de conexión oracle_connection cargada")

    # Luego, procesar todas las secciones del archivo de configuración
    for seccion in conexion_config.sections():
        # Si la sección ya tiene "_connection" al final, usarla directamente
        if seccion.endswith('_connection'):
            config[seccion] = conexion_config[seccion]
            print(f"✓ Configuración de conexión {seccion} cargada directamente")
        # Si no, crear una sección "_connection" correspondiente
        elif seccion not in ['mysql', 'postgres', 'performance', 'output']:  # Excluir secciones no relacionadas con conexiones
            connection_section = f"{seccion}_connection"
            config[connection_section] = conexion_config[seccion]
            print(f"✓ Configuración de conexión {connection_section} cargada")

    print("✓ Configuración cargada exitosamente desde S3")
    return config

def listar_archivos_s3(bucket: str, prefix: str):
    """
    Lista todos los archivos en un bucket S3 con el prefijo especificado.
    """
    try:
        s3_client = boto3.client('s3')
        paginator = s3_client.get_paginator('list_objects_v2')

        archivos = []
        for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
            if 'Contents' in page:
                for obj in page['Contents']:
                    archivos.append(obj['Key'])

        return sorted(archivos)
    except Exception as e:
        logging.error(f"Error al listar archivos en S3 ({bucket}/{prefix}): {e}")
        return []

def cargar_rango_config(tabla: str) -> dict:
    """
    Carga la configuración de rangos desde cargar_rango.ini
    """
    try:
        config_content = leer_archivo_s3(S3_BUCKET, f"{S3_CONFIG_PATH}cargar_rango.ini")
        config = configparser.ConfigParser()
        config.read_string(config_content)

        if tabla not in config:
            logging.warning(f"No se encontró configuración de rango para {tabla}")
            return {}

        return {
            'rango_columna': config[tabla].get('rango_columna', ''),
            'tipo_carga': config[tabla].get('tipo_carga', 'full')
        }
    except Exception as e:
        logging.error(f"Error cargando configuración de rango: {e}")
        return {}
