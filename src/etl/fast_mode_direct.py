#!/usr/bin/env python3
import jaydebeapi
import pandas as pd
import time
from pathlib import Path
import logging
import boto3
from datetime import datetime
import os
import configparser

# Configuración S3
from .s3_manager import S3_OUTPUT_BUCKET

def ejecutar_fast_mode_direct(query_info):
    """
    Ejecuta la query en modo rápido usando conexión JDBC - implementación directa
    basada en test_jdbc_subir_s3.py
    """
    start_time = time.time()
    
    # Extraer información básica
    query = query_info['query'].strip()
    if query.endswith(';'):
        query = query[:-1]
            
    db_type = query_info['db_type'].lower()
    db_config = query_info['db_config']
    tabla_nombre = query_info.get('name', 'DEFAULT')
    db_parent = query_info.get('db_parent', 'DEFAULT')
    
    # Configurar driver y URL de conexión
    if db_type == 'postgres':
        driver_path = str(Path(__file__).parent / 'driver' / 'postgresql-42.6.0.jar')
        driver_class = 'org.postgresql.Driver'
        
        # URL con parámetros optimizados para PostgreSQL
        jdbc_url = (
            f"jdbc:postgresql://{db_config['host']}:{db_config['port']}/{db_config['database']}"
            "?prepareThreshold=5"
            "&fetchSize=10000"
            "&autoCommit=false"
            "&tcpKeepAlive=true"
            f"&ApplicationName=ETL_FastMode_{tabla_nombre}"
        )
    else:  # mysql
        driver_path = str(Path(__file__).parent / 'driver' / 'mysql-connector-j-8.2.0.jar')
        driver_class = 'com.mysql.cj.jdbc.Driver'
        
        # URL con parámetros optimizados para MySQL - EXACTAMENTE como en test_jdbc_subir_s3.py
        jdbc_url = (
            f"jdbc:mysql://{db_config['host']}:{db_config['port']}/{db_config['database']}"
            "?useServerPrepStmts=true"
            "&cachePrepStmts=true"
            "&rewriteBatchedStatements=true"
            "&useCompression=true"
        )
    
    logging.info(f"Conectando a: {jdbc_url}")
    if db_type == 'mysql':
        logging.info(f"USANDO MODO RÁPIDO MYSQL (carga completa sin dividir)")
    else:
        logging.info(f"ACTIVANDO modo rápido para {tabla_nombre}")
    
    try:
        # 1. Probar conexión
        conn_start = time.time()
        conn = jaydebeapi.connect(
            driver_class,
            jdbc_url,
            [db_config['user'], db_config['password']],
            driver_path
        )
        conn_time = time.time() - conn_start
        logging.info(f"Conexión establecida en {conn_time:.2f} segundos")

        cursor = conn.cursor()
        
        try:
            # Configurar timeouts y parámetros de sesión
            if db_type == 'postgres':
                cursor.execute("SET statement_timeout = '3600s'")
            else:  # mysql
                cursor.execute("SET SESSION NET_READ_TIMEOUT=3600")
                cursor.execute("SET SESSION NET_WRITE_TIMEOUT=3600")
                cursor.execute("SET SESSION WAIT_TIMEOUT=3600")
            
            # 2. Ejecutar COUNT query primero
            count_start = time.time()
            count_query = f"SELECT COUNT(*) as total FROM ({query}) as subquery"
            
            try:
                cursor.execute(count_query)
                total_rows = cursor.fetchone()[0]
                count_time = time.time() - count_start
                logging.info(f"Total de registros: {total_rows} (tiempo: {count_time:.2f} segundos)")
            except Exception as e:
                logging.warning(f"No se pudo ejecutar COUNT query: {str(e)}")
                total_rows = "desconocido"
                count_time = 0

            # 3. Ejecutar query principal optimizado
            query_start = time.time()
            cursor.execute(query)
            columnas = [desc[0] for desc in cursor.description]
            resultados = cursor.fetchall()
            query_time = time.time() - query_start
            
            # 4. Convertir a DataFrame
            df_start = time.time()
            df = pd.DataFrame(resultados, columns=columnas)
            total_rows = len(df)
            df_time = time.time() - df_start

            # 5. Subir a S3
            s3_start = time.time()
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            
            # Configurar bucket y prefijo base
            bucket = S3_OUTPUT_BUCKET
            fecha_actual = datetime.now()
            fecha_carpeta = f"{fecha_actual.year:04d}/{fecha_actual.month:02d}/{fecha_actual.day:02d}"
            s3_prefix = f"landing/{db_parent.upper()}/{tabla_nombre.upper()}/{fecha_carpeta}/"
            
            # Limpiar la partición
            from .s3_manager import gestionar_archivos_s3
            gestionar_archivos_s3(bucket, s3_prefix, modo='limpiar')
            
            # Subir el DataFrame a S3
            s3_key = f"{s3_prefix}[{db_parent.upper()}]-{tabla_nombre.upper()}_{timestamp}.parquet"
            
            # Subir archivo a S3
            s3 = boto3.client('s3')
            parquet_buffer = df.to_parquet(compression='snappy')
            
            s3.put_object(
                Bucket=bucket,
                Key=s3_key,
                Body=parquet_buffer
            )
            
            logging.info(f"Archivo subido: s3://{bucket}/{s3_key}")
            archivos_subidos = [s3_key]
            
            s3_time = time.time() - s3_start
            total_time = time.time() - start_time
            
            # Mostrar resultados
            logging.info(f"\nTiempos de ejecución:")
            logging.info(f"- Conexión: {conn_time:.2f} segundos")
            if count_time > 0:
                logging.info(f"- Count query: {count_time:.2f} segundos")
            logging.info(f"- Query principal: {query_time:.2f} segundos")
            logging.info(f"- Conversión a DataFrame: {df_time:.2f} segundos")
            logging.info(f"- Subida a S3: {s3_time:.2f} segundos")
            logging.info(f"- Tiempo total: {total_time:.2f} segundos")
            
            # Mostrar tamaño del DataFrame
            memory_usage = df.memory_usage(deep=True).sum() / (1024 * 1024)  # MB
            logging.info(f"\nUso de memoria:")
            logging.info(f"- DataFrame en memoria: {memory_usage:.2f} MB")
            
            # Mostrar información del DataFrame
            logging.info(f"\nInformación del DataFrame:")
            logging.info(f"- Dimensiones: {df.shape}")
            logging.info(f"- Columnas: {', '.join(df.columns)}")
            logging.info(f"- Primeros 5 registros:\n{df.head()}")
            
            return {
                'success': True,
                'registros': total_rows,
                'archivos': archivos_subidos,
                'tiempo_total': total_time
            }
            
        finally:
            cursor.close()
            
    except Exception as e:
        logging.error(f"Error en modo rapido: {str(e)}")
        return {'success': False, 'error': str(e)}
    finally:
        if 'conn' in locals():
            conn.close()
            logging.info("Conexión cerrada")
