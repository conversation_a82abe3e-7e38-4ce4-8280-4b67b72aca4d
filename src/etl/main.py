import sys
import logging
import argparse
import datetime
import time
import psutil
import gc
import os

# IMPORTS locales (ajustados a la nueva estructura)
from .config_loader import (cargar_configuracion, listar_archivos_s3,
                          S3_BUCKET, S3_PREFIX)  # Importar las configuraciones
from .etl_runner import procesar_configuracion
from .resumen_manager import eliminar_resumen_existente
from .s3_log_handler import configure_s3_logging

def main():
    """
    Punto de entrada principal para el procesamiento de configuraciones y ejecutar consultas
    Maneja el flujo de ejecución con control de errores y logging
    """
    # Eliminar archivo de log local si existe
    if os.path.exists('etl_process.log'):
        try:
            os.remove('etl_process.log')
            print("Archivo de log local eliminado")
        except Exception as e:
            print(f"No se pudo eliminar el archivo de log local: {e}")
    
    start_time = datetime.datetime.now()

    # Parsear argumentos con manejo de errores
    parser = argparse.ArgumentParser(description='Procesar configuración y ejecutar consultas')
    parser.add_argument('seccion_img', type=str, help='Sección de configuración a procesar')

    try:
        args = parser.parse_args()
    except Exception as e:
        logging.error(f"Error parseando argumentos: {e}")
        sys.exit(1)

    # Configurar logging con el nuevo S3LogHandler
    try:
        configure_s3_logging(args.seccion_img, console_output=True)
        logging.info(f"Iniciando proceso ETL para sección: {args.seccion_img}")
    except Exception as e:
        print(f"Error configurando logging S3: {e}")
        sys.exit(1)

    # Usar las configuraciones de S3 desde s3_db_config.ini
    bucket = S3_BUCKET
    config_prefix = S3_PREFIX
    img_config_key = f'{config_prefix}img_config.ini'
    queries_config_key = f'{config_prefix}queries_config.ini'
    conexion_settings_key = f'{config_prefix}conexion_settings.ini'

    try:
        # Validar existencia de archivos de configuración
        archivos_config = listar_archivos_s3(bucket, config_prefix)

        if not archivos_config:
            logging.error(f"No se encontraron archivos de configuración en s3://{bucket}/{config_prefix}")
            sys.exit(1)

        # Verificar archivos específicos
        if img_config_key not in archivos_config:
            logging.error(f"No se encontró {img_config_key}")
            sys.exit(1)

        if queries_config_key not in archivos_config:
            logging.error(f"No se encontró {queries_config_key}")
            sys.exit(1)

        # Cargar configuración de img_config.ini desde S3
        img_config = cargar_configuracion(img_config_key)

        # Verificar que la sección existe
        if args.seccion_img not in img_config:
            logging.error(f"La sección {args.seccion_img} no existe en img_config.ini")
            sys.exit(1)

        # Obtener configuración de la sección
        db_parent = img_config[args.seccion_img].get('db_parent')
        tablas = [tabla.strip() for tabla in img_config[args.seccion_img].get('tablas', '').split(',')]

        logging.info(f"Procesando configuración para {args.seccion_img}")
        logging.info(f"Base de datos padre: {db_parent}")
        logging.info(f"Tablas a procesar: {tablas}")

        # Cargar configuración de queries_config.ini desde S3
        queries_config = cargar_configuracion(queries_config_key)

        # Cargar configuración de conexiones
        conexion_config = cargar_configuracion(conexion_settings_key)

        # Verificar que la base de datos padre existe en queries_config
        if db_parent not in queries_config:
            logging.error(f"La base de datos {db_parent} no existe en queries_config.ini")
            sys.exit(1)

        # Eliminar el resumen existente al inicio
        eliminar_resumen_existente(args.seccion_img)

        # Ejecutar procesamiento de configuración (ETL principal)
        procesar_configuracion(queries_config, img_config, args.seccion_img)

        logging.info("Proceso completado exitosamente")
    except Exception as e:
        logging.error(f"Error inesperado: {e}", exc_info=True)
        raise
    finally:
        end_time = datetime.datetime.now()
        duration = end_time - start_time
        logging.info(f"Tiempo total de ejecución: {duration}")
        # Asegurar que todos los logs se suban a S3
        logging.shutdown()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.error("Error en la ejecución principal", exc_info=True)
        sys.exit(1)
