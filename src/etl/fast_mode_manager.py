import logging
import datetime
import os
import gc
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
import io

import pandas as pd
import boto3
import jpype
import jay<PERSON><PERSON><PERSON><PERSON>
from concurrent.futures import ThreadPoolExecutor, as_completed
import tempfile
import pyarrow as pa
import pyarrow.parquet as pq

from .s3_manager import S3_OUTPUT_BUCKET, gestionar_archivos_s3
from .resumen_manager import crear_resumen_etl

def consolidar_fecha_individual(fecha_str, lista_dfs, s3_prefix, bucket, timestamp_base, db_parent, tabla_nombre):
    """
    Consolida todos los DataFrames de una fecha específica en un solo archivo.
    Optimización: Usa PyArrow para mejor rendimiento y compresión zstd.
    """
    try:
        if not lista_dfs:
            return None

        # Concatenar todos los DataFrames de la misma fecha
        df_consolidado = pd.concat(lista_dfs, ignore_index=True)
        total_filas_fecha = len(df_consolidado)

        logging.info(f"🔄 Consolidando {len(lista_dfs)} chunks para fecha {fecha_str}: {total_filas_fecha:,} filas")

        # Configurar ruta S3
        fecha_prefix = f"{s3_prefix}{fecha_str}/"

        # Limpiar partición una sola vez
        gestionar_archivos_s3(bucket, fecha_prefix, modo='limpiar', particiones_limpiadas=set())

        # Crear nombre de archivo consolidado (SIN sufijo de chunk)
        fecha_limpia = fecha_str.replace('/', '')
        s3_key = f"{fecha_prefix}{db_parent.upper()}-{tabla_nombre.upper()}_{timestamp_base}_{fecha_limpia}.parquet"

        # OPTIMIZACIÓN: Usar PyArrow con compresión zstd para mejor rendimiento
        try:
            # Convertir a PyArrow Table para mejor rendimiento
            table = pa.Table.from_pandas(df_consolidado)

            # Escribir con compresión zstd (mejor que snappy)
            parquet_buffer = io.BytesIO()
            pq.write_table(table, parquet_buffer, compression='zstd', compression_level=3)
            parquet_buffer.seek(0)

            # Subir archivo consolidado
            s3 = boto3.client('s3')
            s3.put_object(
                Bucket=bucket,
                Key=s3_key,
                Body=parquet_buffer.getvalue()
            )

            logging.info(f"✅ Archivo CONSOLIDADO subido: s3://{bucket}/{s3_key} ({total_filas_fecha:,} filas)")

            # Liberar memoria del DataFrame consolidado
            del df_consolidado, table
            gc.collect()

            return s3_key

        except Exception as e:
            logging.error(f"❌ Error al consolidar fecha {fecha_str}: {str(e)}")
            return None

    except Exception as e:
        logging.error(f"❌ Error general en consolidación de fecha {fecha_str}: {str(e)}")
        return None

class FastModeManager:
    """
    Gestor del modo rápido para conexiones JDBC a bases de datos.
    Proporciona métodos para ejecutar consultas usando JDBC para mejorar el rendimiento.
    """

    def __init__(self):
        # Iniciar JVM si no está iniciada
        if not jpype.isJVMStarted():
            # Obtener paths de los drivers
            mysql_driver_path = str(Path(__file__).parent / 'driver' / 'mysql-connector-j-8.2.0.jar')
            pg_driver_path = str(Path(__file__).parent / 'driver' / 'postgresql-42.6.0.jar')
            oracle_driver_path = str(Path(__file__).parent / 'driver' / 'oracle-23.5.jar')

            # Construir classpath con todos los drivers
            classpath = f"{mysql_driver_path}{os.pathsep}{pg_driver_path}{os.pathsep}{oracle_driver_path}"

            logging.info(f"Iniciando JVM con memoria optimizada y drivers: {classpath}")
            jpype.startJVM(jpype.getDefaultJVMPath(),
                         f"-Djava.class.path={classpath}",
                         "-Xms4096m",  # Aumentar memoria inicial a 4GB
                         "-Xmx16384m",  # Aumentar memoria máxima a 16GB
                         "-XX:+UseG1GC",  # Usar G1 Garbage Collector
                         "-XX:G1HeapRegionSize=32M",  # Optimizar tamaño de regiones G1GC (aumentado)
                         "-XX:MaxGCPauseMillis=500",  # Pausas de GC máximo 500ms
                         "-XX:+ParallelRefProcEnabled",  # Habilitar procesamiento paralelo de referencias
                         "-XX:+UseStringDeduplication",  # Optimizar uso de strings
                         "-XX:NewRatio=1",  # Ratio entre generación joven y vieja
                         "-XX:+DisableExplicitGC")  # Evitar llamadas explícitas a GC
        else:
            logging.info("JVM ya está iniciada")

    def _conectar_bd(self, db_type, db_config, query_info):
        """Establece conexión con la base de datos y devuelve la conexión y el cursor"""
        conn_start = time.time()

        try:
            # Verificar si el host en el secreto es diferente al que se está usando
            # Esto puede ocurrir si el secreto contiene un host específico para una base de datos
            # pero el sistema está usando un host genérico
            db_parent = query_info.get('db_parent')
            if 'host' in db_config and db_parent:
                # Intentar obtener el secreto directamente para verificar si hay un host específico
                try:
                    import boto3
                    import json

                    # Buscar el secreto en el archivo conexion_settings.ini en S3
                    try:
                        s3 = boto3.client('s3')
                        response = s3.get_object(
                            Bucket='prd-datalake-etl-configuration-637423440311',
                            Key='ETL_CONFIG/CONFIGS/conexion_settings.ini'
                        )
                        config_content = response['Body'].read().decode('utf-8')

                        import configparser
                        config = configparser.ConfigParser()
                        config.read_string(config_content)

                        # Buscar el secreto para el tipo de base de datos específico
                        secret_name = None
                        if db_type in config:
                            # Usar la sección específica del archivo de configuración
                            secret_name = config[db_type].get('secret_name')
                            region = config[db_type].get('region', 'us-east-1')
                            logging.info(f"Usando sección {db_type} con secreto {secret_name}")

                            if secret_name:
                                # Obtener el secreto
                                client = boto3.client('secretsmanager', region_name=region)
                                response = client.get_secret_value(SecretId=secret_name)
                                secret = response.get('SecretString')

                                if secret:
                                    secret_dict = json.loads(secret)

                                    # Verificar si el host en el secreto es diferente al que se está usando
                                    if 'MYSQL_HOST' in secret_dict and secret_dict['MYSQL_HOST'] != db_config['host']:
                                        # Guardar el host original para debugging
                                        original_host = db_config['host']

                                        # Modificar el host para usar el correcto del secreto
                                        db_config['host'] = secret_dict['MYSQL_HOST']

                                        # Verificar si hay una base de datos específica en la configuración
                                        if db_type in config and 'database' in config[db_type]:
                                            db_config['database'] = config[db_type]['database']
                                            logging.info(f"Usando base de datos de la configuración para {db_type}: {db_config['database']}")
                                        # Si no, usar la base de datos del secreto si está disponible
                                        elif 'MYSQL_DATABASE' in secret_dict and 'database' not in db_config:
                                            db_config['database'] = secret_dict['MYSQL_DATABASE']

                                        logging.info(f"PARCHE APLICADO: Host modificado para {db_parent}")
                                        logging.info(f"Host original: {original_host}")
                                        logging.info(f"Nuevo host: {db_config['host']}")
                                        if 'database' in db_config:
                                            logging.info(f"Base de datos: {db_config['database']}")
                    except Exception as e:
                        # Si hay un error al obtener el secreto, registrar el error
                        logging.warning(f"Error al obtener secreto para {db_type}: {e}")
                except Exception as e:
                    # Si hay un error en el proceso general, registrar el error
                    logging.warning(f"Error general al procesar secreto para {db_type}: {e}")
            # Determinar el tipo base de la base de datos (mysql o postgres)
            base_db_type = db_type.split('_')[0].lower()
            logging.info(f"Tipo base de base de datos: {base_db_type} (tipo completo: {db_type})")

            # Determinar el driver y URL JDBC adecuados
            if base_db_type == 'postgres' or base_db_type == 'postgresql':
                driver_class = "org.postgresql.Driver"
                driver_path = str(Path(__file__).parent.absolute() / 'driver' / 'postgresql-42.6.0.jar')
                logging.info(f"Usando driver PostgreSQL: {driver_path}")

                # Configurar parámetros JDBC optimizados para PostgreSQL
                jdbc_params = {
                    'useCursorFetch': 'true',
                    'defaultFetchSize': '50000',  # Aumentado de 10000 a 50000
                    'prepareThreshold': '5',
                    'tcpKeepAlive': 'true',
                    'reWriteBatchedInserts': 'true',
                    'binaryTransfer': 'true',
                    'socketTimeout': '3600',
                    'loginTimeout': '300',
                    'defaultRowFetchSize': '50000',
                    'readOnly': 'true',  # Solo lectura para mejor rendimiento
                    'ApplicationName': f"ETL_FastMode_{query_info.get('name', 'DEFAULT')}"
                }
                params_str = '&'.join([f"{k}={v}" for k, v in jdbc_params.items()])
                jdbc_url = f"jdbc:postgresql://{db_config['host']}:{db_config['port']}/{db_config['database']}?{params_str}"
                logging.info(f"Conectando a: {jdbc_url}")
                logging.info(f"USANDO MODO RÁPIDO POSTGRESQL para {query_info.get('name', 'DEFAULT')}")

            elif base_db_type == 'mysql':
                driver_class = "com.mysql.cj.jdbc.Driver"
                driver_path = str(Path(__file__).parent.absolute() / 'driver' / 'mysql-connector-j-8.2.0.jar')
                logging.info(f"Usando driver MySQL: {driver_path}")

                # Configurar parámetros JDBC optimizados para MySQL
                jdbc_params = {
                    'useServerPrepStmts': 'true',
                    'cachePrepStmts': 'true',
                    'rewriteBatchedStatements': 'true',
                    'useCompression': 'true',
                    'useCursorFetch': 'true',
                    'defaultFetchSize': '50000',  # Aumentado de 10000 a 50000
                    'zeroDateTimeBehavior': 'CONVERT_TO_NULL',
                    'allowPublicKeyRetrieval': 'true',
                    'useSSL': 'false',
                    'socketTimeout': '3600000',
                    'connectTimeout': '300000',
                    'useReadAheadInput': 'true',  # Lectura anticipada
                    'cacheResultSetMetadata': 'true'  # Cachear metadatos
                }
                params_str = '&'.join([f"{k}={v}" for k, v in jdbc_params.items()])
                # Construir la URL JDBC con o sin base de datos
                if 'database' in db_config:
                    jdbc_url = f"jdbc:mysql://{db_config['host']}:{db_config['port']}/{db_config['database']}?{params_str}"
                else:
                    jdbc_url = f"jdbc:mysql://{db_config['host']}:{db_config['port']}/?{params_str}"
                logging.info(f"Conectando a: {jdbc_url}")
                logging.info(f"USANDO MODO RÁPIDO MYSQL para {query_info.get('name', 'DEFAULT')} (tipo: {db_type})")

            elif base_db_type == 'oracle':
                driver_class = "oracle.jdbc.OracleDriver"
                driver_path = str(Path(__file__).parent.absolute() / 'driver' / 'oracle-23.5.jar')
                logging.info(f"Usando driver Oracle: {driver_path}")

                # Configurar parámetros JDBC ULTRA-OPTIMIZADOS para Oracle (basado en mejores prácticas de rendimiento)
                jdbc_params = {
                    # PARÁMETROS CRÍTICOS DE RENDIMIENTO - CONFIGURACIÓN AGRESIVA OPTIMIZADA
                    'defaultRowPrefetch': '50000',  # OPTIMIZADO: Balance perfecto memoria/velocidad
                    'oracle.jdbc.defaultRowPrefetch': '50000',  # OPTIMIZADO: Consistencia con defaultRowPrefetch

                    # PARÁMETROS CRÍTICOS PARA CURSOR FETCH (equivalente a MySQL useCursorFetch=true + defaultFetchSize=10000)
                    'useCursorFetch': 'true',  # CRÍTICO: Habilitar cursor fetch para grandes datasets
                    'defaultFetchSize': '50000',  # CRÍTICO: Tamaño de fetch optimizado (igual que MySQL exitoso)

                    # PARÁMETROS DE CACHÉ Y PREPARACIÓN (equivalente a MySQL cachePrepStmts=true + useServerPrepStmts=true)
                    'useServerPrepStmts': 'true',  # CRÍTICO: Usar prepared statements en servidor
                    'cachePrepStmts': 'true',  # CRÍTICO: Cachear prepared statements
                    'rewriteBatchedStatements': 'true',  # CRÍTICO: Reescribir statements en batch
                    'useCompression': 'true',  # CRÍTICO: Comprimir datos de red

                    # Timeouts optimizados pero no excesivos (basado en configuración MySQL exitosa)
                    'oracle.jdbc.ReadTimeout': '1800000',  # 30 minutos (igual que MySQL)
                    'oracle.net.CONNECT_TIMEOUT': '60000',  # 1 minuto (igual que MySQL)
                    'oracle.net.READ_TIMEOUT': '1800000',  # 30 minutos

                    # Caché y optimizaciones de memoria AGRESIVAS
                    'oracle.jdbc.implicitStatementCacheSize': '200',  # AUMENTADO: Más caché para mejor rendimiento
                    'oracle.jdbc.freeMemoryOnEnterImplicitCache': 'true',
                    'oracle.jdbc.useThreadLocalBufferCache': 'true',

                    # Optimizaciones de red y protocolo AGRESIVAS
                    'oracle.jdbc.useFetchSizeWithLongColumn': 'true',
                    'oracle.jdbc.processEscapes': 'false',  # Más rápido
                    'oracle.jdbc.enableQueryTimeouts': 'false',  # Evitar interrupciones

                    # Optimizaciones de red adicionales AGRESIVAS
                    'oracle.net.disableOob': 'true',  # Desactivar out-of-band breaks
                    'oracle.jdbc.TcpNoDelay': 'true',  # TCP_NODELAY para menor latencia

                    # Optimizaciones de batch y streaming AGRESIVAS
                    'oracle.jdbc.defaultExecuteBatch': '5000',  # AUMENTADO: Batch más grande
                    'oracle.jdbc.streamChunkSize': '32768',  # AUMENTADO: Chunks más grandes (32KB)
                }

                # Determinar el tipo de conexión Oracle (service_name o SID)
                if 'service_name' in db_config:
                    # Formato para service_name
                    base_url = f"jdbc:oracle:thin:@//{db_config['host']}:{db_config['port']}/{db_config['service_name']}"
                elif 'sid' in db_config:
                    # Formato para SID
                    base_url = f"jdbc:oracle:thin:@{db_config['host']}:{db_config['port']}:{db_config['sid']}"
                elif 'dsn' in db_config:
                    # Usar DSN directamente
                    base_url = f"jdbc:oracle:thin:@{db_config['dsn']}"
                elif 'database' in db_config:
                    # Usar 'database' como service_name (común en configuraciones de Oracle)
                    logging.info(f"Usando 'database' como service_name para Oracle: {db_config['database']}")
                    base_url = f"jdbc:oracle:thin:@//{db_config['host']}:{db_config['port']}/{db_config['database']}"
                else:
                    raise ValueError("La configuración de Oracle debe incluir 'service_name', 'sid', 'dsn' o 'database'")

                # Agregar parámetros a la URL
                params_str = '&'.join([f"{k}={v}" for k, v in jdbc_params.items()])
                jdbc_url = f"{base_url}?{params_str}" if params_str else base_url

                logging.info(f"Conectando a Oracle: {jdbc_url}")
                logging.info(f"USANDO MODO RÁPIDO ORACLE para {query_info.get('name', 'DEFAULT')} (tipo: {db_type})")

            else:
                raise ValueError(f"Tipo de base de datos no soportado: {db_type} (tipo base: {base_db_type})")

            # Verificar si la JVM ya está iniciada
            import jpype
            import os

            # Obtener rutas absolutas a los drivers
            pg_driver_path = str(Path(__file__).parent.absolute() / 'driver' / 'postgresql-42.6.0.jar')
            mysql_driver_path = str(Path(__file__).parent.absolute() / 'driver' / 'mysql-connector-j-8.2.0.jar')

            # Verificar que los archivos existen
            if not os.path.exists(pg_driver_path):
                logging.error(f"Driver PostgreSQL no encontrado en: {pg_driver_path}")
                raise FileNotFoundError(f"Driver PostgreSQL no encontrado en: {pg_driver_path}")
            if not os.path.exists(mysql_driver_path):
                logging.error(f"Driver MySQL no encontrado en: {mysql_driver_path}")
                raise FileNotFoundError(f"Driver MySQL no encontrado en: {mysql_driver_path}")

            # Construir classpath con ambos drivers
            classpath = f"{pg_driver_path}{os.pathsep}{mysql_driver_path}"
            logging.info(f"Classpath completo: {classpath}")

            # Conectar con el método alternativo para PostgreSQL
            if base_db_type == 'postgres' or base_db_type == 'postgresql':
                try:
                    import psycopg2
                    logging.info("Intentando conectar con psycopg2 directamente")
                    # Conectar con psycopg2 en lugar de JDBC
                    pg_conn = psycopg2.connect(
                        host=db_config['host'],
                        port=db_config['port'],
                        database=db_config['database'],
                        user=db_config['user'],
                        password=db_config['password']
                    )
                    logging.info("Conexión PostgreSQL establecida con psycopg2")

                    # Crear un wrapper para que se comporte como una conexión JDBC
                    class JDBCCursorWrapper:
                        def __init__(self, pg_cursor):
                            self.pg_cursor = pg_cursor
                            self.description = None
                            self.rowcount = 0

                        def execute(self, query):
                            self.pg_cursor.execute(query)
                            self.description = [(col.name,) for col in self.pg_cursor.description] if self.pg_cursor.description else None
                            return self

                        def fetchmany(self, size):
                            results = self.pg_cursor.fetchmany(size)
                            return results

                        def fetchone(self):
                            return self.pg_cursor.fetchone()

                        def close(self):
                            return self.pg_cursor.close()

                    class JDBCConnectionWrapper:
                        def __init__(self, pg_conn):
                            self.pg_conn = pg_conn

                        def cursor(self):
                            return JDBCCursorWrapper(self.pg_conn.cursor())

                        def close(self):
                            return self.pg_conn.close()

                        def commit(self):
                            return self.pg_conn.commit()

                    # Usar el wrapper para la conexión
                    conn = JDBCConnectionWrapper(pg_conn)
                    logging.info("Wrapper de conexión JDBC creado correctamente")

                    # Crear cursor y retornar
                    cursor = conn.cursor()
                    return conn, cursor

                except ImportError:
                    logging.warning("psycopg2 no está disponible, intentando con JDBC")
                except Exception as e:
                    logging.warning(f"Error al conectar con psycopg2: {str(e)}")

            # Si llegamos aquí o es MySQL, intentar con JDBC
            logging.info(f"Iniciando JVM con memoria optimizada y driver: {driver_path}")
            if not jpype.isJVMStarted():
                jpype.startJVM(jpype.getDefaultJVMPath(), "-Djava.class.path=" + classpath, "-Xms512m", "-Xmx1024m")

            # Cargar el driver correcto
            if base_db_type == 'postgres' or base_db_type == 'postgresql':
                try:
                    pg_driver = jpype.JClass("org.postgresql.Driver")
                    logging.info("Driver PostgreSQL cargado correctamente")
                except Exception as e:
                    logging.error(f"Error al cargar driver PostgreSQL: {str(e)}")
                    raise
            elif base_db_type == 'mysql':
                try:
                    mysql_driver = jpype.JClass("com.mysql.cj.jdbc.Driver")
                    logging.info("Driver MySQL cargado correctamente")
                except Exception as e:
                    logging.error(f"Error al cargar driver MySQL: {str(e)}")
                    raise
            elif base_db_type == 'oracle':
                try:
                    oracle_driver = jpype.JClass("oracle.jdbc.OracleDriver")
                    logging.info("Driver Oracle cargado correctamente")
                except Exception as e:
                    logging.error(f"Error al cargar driver Oracle: {str(e)}")
                    raise

            # Conectar con JDBC
            import jaydebeapi
            conn = jaydebeapi.connect(
                driver_class,
                jdbc_url,
                [db_config['user'], db_config['password']],
                driver_path
            )
            logging.info(f"Conexión {db_type} establecida con JDBC")
            cursor = conn.cursor()

            # CRÍTICO: Configurar arraysize del cursor para Oracle (esto SÍ afecta fetchmany)
            if base_db_type == 'oracle':
                cursor.arraysize = 50000  # OPTIMIZADO: Aumentado para mejor rendimiento (igual que defaultFetchSize)
                logging.info(f"OPTIMIZACIÓN ORACLE: cursor.arraysize configurado a {cursor.arraysize} (optimizado para máximo rendimiento)")

            return conn, cursor

        except Exception as e:
            logging.error(f"Error al inicializar JVM o conectar a la base de datos: {str(e)}")
            raise

    def optimizar_consulta(self, query: str, db_type: str, tabla_nombre: str) -> str:
        """
        Optimiza una consulta SQL según el tipo de base de datos

        Args:
            query (str): Consulta SQL original
            db_type (str): Tipo de base de datos (mysql, postgres, etc.)
            tabla_nombre (str): Nombre de la tabla principal

        Returns:
            str: Consulta SQL optimizada
        """
        base_db_type = db_type.split('_')[0].lower()

        if base_db_type.startswith('mysql'):
            # Optimizaciones para MySQL
            logging.info(f"Optimizando consulta MySQL para tabla {tabla_nombre}")

            # Añadir hint de tiempo máximo de ejecución
            if "SELECT" in query.upper():
                optimized_query = query.replace(
                    "SELECT",
                    "SELECT /*+ MAX_EXECUTION_TIME(3600000) */",
                    1  # Reemplazar solo la primera ocurrencia
                )
            else:
                optimized_query = query

            # Forzar uso de índice primario si es una consulta simple
            if f"FROM {tabla_nombre}" in optimized_query and " JOIN " not in optimized_query:
                optimized_query = optimized_query.replace(
                    f"FROM {tabla_nombre}",
                    f"FROM {tabla_nombre} FORCE INDEX (PRIMARY)"
                )

            # Añadir STRAIGHT_JOIN si hay múltiples tablas
            if " JOIN " in optimized_query and "STRAIGHT_JOIN" not in optimized_query:
                optimized_query = optimized_query.replace("SELECT ", "SELECT STRAIGHT_JOIN ")

            return optimized_query

        elif base_db_type.startswith('postgres'):
            # Optimizaciones para PostgreSQL
            logging.info(f"Optimizando consulta PostgreSQL para tabla {tabla_nombre}")

            # Crear prefijo con configuraciones optimizadas
            prefix = "SET LOCAL enable_seqscan TO off; "
            prefix += "SET LOCAL work_mem TO '256MB'; "

            # Ajustar parallel workers si hay operaciones costosas
            if "GROUP BY" in query or "ORDER BY" in query or "DISTINCT" in query:
                prefix += "SET LOCAL max_parallel_workers_per_gather TO 4; "

            return prefix + query

        elif base_db_type.startswith('oracle'):
            # Optimizaciones CONSERVADORAS para Oracle (evitar hints agresivos que pueden causar cuelgues)
            logging.info(f"Optimizando consulta Oracle para tabla {tabla_nombre} (modo conservador)")

            # Crear consulta optimizada sin prefijos de configuración de sesión
            # (la configuración de sesión se maneja por separado en el método _conectar_bd)
            optimized_query = query

            # CAMBIO: Usar hints más conservadores para evitar cuelgues
            # Solo añadir FIRST_ROWS sin paralelismo agresivo
            if "SELECT" in optimized_query.upper() and not "/*+" in optimized_query:
                optimized_query = optimized_query.replace(
                    "SELECT",
                    "SELECT /*+ FIRST_ROWS(10000) */",
                    1  # Reemplazar solo la primera ocurrencia
                )
                logging.info(f"Hint FIRST_ROWS aplicado a consulta Oracle")

            # NO añadir hints de paralelismo ni caché de resultados por ahora
            # para evitar problemas de rendimiento que causen cuelgues

            logging.info(f"Consulta Oracle optimizada (conservadora): {optimized_query[:200]}...")
            return optimized_query

        # Sin optimización para otros tipos de BD
        return query

    def ejecutar_modo_rapido(self, query_info: Dict[str, Any], seccion_img: str = None) -> Dict:
        """
        Ejecuta una consulta en modo rápido, procesando los resultados en chunks

        Args:
            query_info (Dict): Información de la consulta a ejecutar
            seccion_img (str, optional): Sección de imagen para generar resumen

        Returns:
            Dict: Resultados del procesamiento
        """
        resultados = {
            'registros_totales': 0,
            'archivos_procesados': [],
            'resumen_generado': False
        }

        start_time = time.time()

        try:
            # Extraer información básica
            query = query_info['query'].strip()
            if query.endswith(';'):
                query = query[:-1]

            db_type = query_info['db_type'].lower()
            # Determinar el tipo base de la base de datos (mysql o postgres)
            base_db_type = db_type.split('_')[0].lower()
            logging.info(f"Tipo base de base de datos en ejecutar_modo_rapido: {base_db_type} (tipo completo: {db_type})")

            db_config = query_info['db_config']
            tabla_nombre = query_info.get('name', 'DEFAULT')
            db_parent = query_info.get('db_parent', 'DEFAULT')

            # Obtener filtros de fecha
            rango_columna = query_info.get('rango_columna')
            rango_inicio = query_info.get('rango_inicio')
            rango_fin = query_info.get('rango_fin')

            # Aplicar filtros de fecha a la consulta si están definidos
            if all([rango_columna, rango_inicio, rango_fin]):
                logging.info(f"Aplicando filtros de fecha: {rango_columna} >= '{rango_inicio}' AND {rango_columna} < '{rango_fin}'")

                # Envolver la consulta en un subselect para permitir filtrar por alias
                if base_db_type == 'oracle':
                    query = f"SELECT * FROM (\n{query}\n) sub"
                else:
                    query = f"SELECT * FROM (\n{query}\n) AS sub"

                # Formatear fechas según el tipo de base de datos
                if base_db_type == 'postgres':
                    rango_inicio_fmt = f"'{rango_inicio}'::timestamp" if rango_inicio != 'CURRENT_DATE' else "CURRENT_DATE"
                    rango_fin_fmt = f"'{rango_fin}'::timestamp" if rango_fin != 'CURRENT_DATE' else "CURRENT_DATE"
                elif base_db_type == 'oracle':
                    # Para Oracle, necesitamos que el rango sea día siguiente para consultas correctas
                    # Usamos una solución que funciona para todos los casos
                    from datetime import datetime, timedelta

                    # Función para calcular el día siguiente para rango_fin
                    def obtener_dia_siguiente(fecha_str):
                        try:
                            # Si es una fecha YYYY-MM-DD, calcular día siguiente
                            fecha = datetime.strptime(fecha_str, '%Y-%m-%d')
                            fecha_siguiente = fecha + timedelta(days=1)
                            return fecha_siguiente.strftime('%Y-%m-%d')
                        except:
                            # Si no es una fecha válida, devolver la original
                            return fecha_str

                    # Para rango_inicio usamos la fecha normal
                    if rango_inicio == 'CURRENT_DATE':
                        fecha_hoy = datetime.now().strftime('%Y-%m-%d')
                        rango_inicio_fmt = f"TO_DATE('{fecha_hoy}', 'YYYY-MM-DD')"
                    else:
                        rango_inicio_fmt = f"TO_DATE('{rango_inicio}', 'YYYY-MM-DD')"
                    
                    # Para rango_fin usamos SIEMPRE el día siguiente (crucial para que la consulta funcione)
                    if rango_fin == 'CURRENT_DATE':
                        fecha_manana = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
                        rango_fin_fmt = f"TO_DATE('{fecha_manana}', 'YYYY-MM-DD')"
                    else:
                        # Calcular el día siguiente de la fecha especificada
                        fecha_siguiente = obtener_dia_siguiente(rango_fin)
                        rango_fin_fmt = f"TO_DATE('{fecha_siguiente}', 'YYYY-MM-DD')"
                    
                    # Información para debugging
                    logging.info(f"Oracle - rango_inicio: {rango_inicio} -> rango_inicio_fmt: {rango_inicio_fmt}")
                    logging.info(f"Oracle - rango_fin: {rango_fin} -> rango_fin_fmt: {rango_fin_fmt}")
                else:
                    # Para MySQL, manejar CURRENT_DATE de forma especial
                    rango_inicio_fmt = 'CURDATE()' if rango_inicio == 'CURRENT_DATE' else f"'{rango_inicio}'"
                    rango_fin_fmt = 'CURDATE()' if rango_fin == 'CURRENT_DATE' else f"'{rango_fin}'"

                # Agregar filtros de fecha usando el alias del subselect
                query = f"{query} WHERE sub.{rango_columna} >= {rango_inicio_fmt} AND sub.{rango_columna} < {rango_fin_fmt}"
                logging.info(f"Query con filtros de fecha aplicados: {query[:300]}..." if len(query) > 300 else query)

            # Optimizar la consulta SQL según el tipo de base de datos
            query_optimizada = self.optimizar_consulta(query, db_type, tabla_nombre)
            logging.info(f"Consulta optimizada: {query_optimizada[:200]}..." if len(query_optimizada) > 200 else query_optimizada)

            # Conectar a la base de datos
            conn, cursor = self._conectar_bd(db_type, db_config, query_info)

            # Medir tiempo de conexión
            conn_time = time.time() - start_time
            logging.info(f"Conexión establecida en {conn_time:.2f} segundos")

            try:
                # Configurar timeouts y parámetros de sesión
                if base_db_type == 'postgres' or base_db_type == 'postgresql':
                    cursor.execute("SET statement_timeout = '3600s'")
                    # Configurar otros parámetros específicos de PostgreSQL
                    cursor.execute("SET work_mem = '256MB'")
                    cursor.execute("SET maintenance_work_mem = '256MB'")
                elif base_db_type == 'mysql':  # mysql o variantes (mysql_bimprod, etc.)
                    cursor.execute("SET SESSION NET_READ_TIMEOUT=3600")
                    cursor.execute("SET SESSION NET_WRITE_TIMEOUT=3600")
                    cursor.execute("SET SESSION WAIT_TIMEOUT=3600")
                elif base_db_type == 'oracle':
                    # Configurar parámetros OPTIMIZADOS de Oracle para máximo rendimiento
                    logging.info(f"🔍 ORACLE DEBUG: Configurando parámetros de sesión OPTIMIZADOS...")
                    try:
                        # Configuraciones básicas de formato
                        cursor.execute("ALTER SESSION SET NLS_DATE_FORMAT = 'YYYY-MM-DD HH24:MI:SS'")
                        cursor.execute("ALTER SESSION SET NLS_TIMESTAMP_FORMAT = 'YYYY-MM-DD HH24:MI:SS.FF'")

                        # OPTIMIZACIONES CRÍTICAS DE RENDIMIENTO
                        cursor.execute("ALTER SESSION SET optimizer_mode = 'FIRST_ROWS'")  # Optimizado para primeras filas
                        cursor.execute("ALTER SESSION SET optimizer_dynamic_sampling = 4")  # AUMENTADO: Mejor sampling
                        cursor.execute("ALTER SESSION SET QUERY_REWRITE_ENABLED = FALSE")  # Desactivar reescritura de queries

                        # NUEVAS OPTIMIZACIONES AGRESIVAS
                        cursor.execute("ALTER SESSION SET PARALLEL_DEGREE_POLICY = 'AUTO'")  # Paralelismo automático
                        cursor.execute("ALTER SESSION SET PARALLEL_MIN_TIME_THRESHOLD = '10'")  # Umbral bajo para paralelismo
                        cursor.execute("ALTER SESSION SET DB_FILE_MULTIBLOCK_READ_COUNT = 128")  # Lectura en bloques grandes
                        cursor.execute("ALTER SESSION SET SORT_AREA_SIZE = 67108864")  # 64MB para ordenamiento
                        cursor.execute("ALTER SESSION SET HASH_AREA_SIZE = 67108864")  # 64MB para hash joins

                        logging.info(f"✅ ORACLE DEBUG: Parámetros de sesión OPTIMIZADOS configurados")
                    except Exception as e:
                        logging.warning(f"⚠️ ORACLE DEBUG: Error configurando sesión (continuando): {str(e)}")

                # OPTIMIZACIÓN: Eliminar COUNT query costoso - se calculará durante el procesamiento
                count_start = time.time()
                total_rows = "calculando..."
                count_time = 0
                logging.info(f"OPTIMIZACIÓN: Saltando COUNT query costoso - total se calculará durante procesamiento")

                # Medir tiempo de ejecución de la query principal
                query_start = time.time()

                # Configurar timeouts como en app.py
                if base_db_type == 'mysql':
                    cursor.execute("SET SESSION NET_READ_TIMEOUT=3600")  # 1 hora
                    cursor.execute("SET SESSION NET_WRITE_TIMEOUT=3600")  # 1 hora
                    cursor.execute("SET SESSION WAIT_TIMEOUT=3600")  # 1 hora

                # Ejecutar query optimizada con logging detallado
                logging.info(f"🔍 ORACLE DEBUG: Iniciando ejecución de consulta...")
                logging.info(f"🔍 ORACLE DEBUG: Query a ejecutar: {query_optimizada[:500]}...")

                try:
                    cursor.execute(query_optimizada)
                    logging.info(f"✅ ORACLE DEBUG: Consulta ejecutada exitosamente")
                except Exception as e:
                    logging.error(f"❌ ORACLE DEBUG: Error al ejecutar consulta: {str(e)}")
                    raise

                # Obtener nombres de columnas y convertirlos a strings de Python
                logging.info(f"🔍 ORACLE DEBUG: Obteniendo descripción de columnas...")
                try:
                    if cursor.description is None:
                        logging.error(f"❌ ORACLE DEBUG: cursor.description es None - la consulta no devolvió resultados")
                        raise ValueError("La consulta no devolvió descripción de columnas")

                    columnas = [str(desc[0]) for desc in cursor.description]
                    logging.info(f"✅ ORACLE DEBUG: {len(columnas)} columnas encontradas: {columnas[:5]}...")
                except Exception as e:
                    logging.error(f"❌ ORACLE DEBUG: Error al obtener columnas: {str(e)}")
                    raise

                # Procesar resultados en chunks optimizados
                # Para Oracle, usar chunks optimizados para máximo rendimiento
                if base_db_type == 'oracle':
                    chunk_size = 50000  # OPTIMIZADO: Aumentado para mejor rendimiento (igual que arraysize)
                    logging.info(f"🔍 ORACLE DEBUG: Usando chunk_size OPTIMIZADO: {chunk_size}")
                else:
                    chunk_size = 100000  # Procesar 100k filas a la vez para otros DB

                # Inicializar variables
                total_rows = 0
                archivos_subidos = []
                particiones_limpiadas = set()

                # OPTIMIZACIÓN: Acumular datos por fecha para generar UN SOLO archivo por día
                datos_por_fecha = {}  # {fecha_str: [df1, df2, df3, ...]}

                # Configurar bucket y prefijo base
                bucket = S3_OUTPUT_BUCKET

                # Usar la misma estructura que en etl_runner.py, sin prefijo landing/
                s3_prefix = f"{db_parent.upper()}/{tabla_nombre.upper()}/"

                # Timestamp para nombre de archivo base - Usar importación correcta
                from datetime import datetime as dt
                timestamp_base = dt.now().strftime("%Y%m%d-%H%M%S")

                # Verificar si hay columna de rango para particionar por fecha
                rango_columna = query_info.get('rango_columna')

                logging.info(f"🔍 ORACLE DEBUG: Iniciando bucle de fetchmany con chunk_size={chunk_size}")
                chunk_counter = 0

                while True:
                    chunk_counter += 1
                    logging.info(f"🔍 ORACLE DEBUG: Intentando fetchmany chunk #{chunk_counter}...")

                    try:
                        # Obtener siguiente chunk de datos
                        resultados = cursor.fetchmany(chunk_size)
                        logging.info(f"✅ ORACLE DEBUG: fetchmany completado, {len(resultados) if resultados else 0} filas obtenidas")

                        if not resultados:
                            logging.info(f"🔍 ORACLE DEBUG: No más resultados, saliendo del bucle")
                            break

                        # Crear DataFrame del chunk
                        logging.info(f"🔍 ORACLE DEBUG: Creando DataFrame con {len(resultados)} filas...")
                        df_chunk = pd.DataFrame(resultados, columns=columnas)
                        logging.info(f"✅ ORACLE DEBUG: DataFrame creado exitosamente")

                    except Exception as e:
                        logging.error(f"❌ ORACLE DEBUG: Error en fetchmany chunk #{chunk_counter}: {str(e)}")
                        raise

                    # Procesar tipos de datos específicos según el tipo de base de datos
                    if base_db_type == 'postgres' or base_db_type == 'postgresql':
                        for col in df_chunk.columns:
                            # Manejar tipos JSON/JSONB
                            if df_chunk[col].dtype == 'object':
                                # Intentar convertir strings JSON a diccionarios Python si es posible
                                try:
                                    # Verificar si la primera fila no nula parece JSON
                                    sample = df_chunk[col].dropna().iloc[0] if not df_chunk[col].dropna().empty else None
                                    if sample and isinstance(sample, str) and (sample.startswith('{') or sample.startswith('[')):
                                        try:
                                            import json
                                            # Convertir a string primero para manejar posibles objetos JSON de PostgreSQL
                                            df_chunk[col] = df_chunk[col].apply(lambda x: json.dumps(x) if x is not None else None)
                                        except:
                                            # Si falla, mantener como string
                                            df_chunk[col] = df_chunk[col].astype(str)
                                    else:
                                        # Para otros tipos object, convertir a string
                                        df_chunk[col] = df_chunk[col].astype(str)
                                except:
                                    # Si hay algún error, convertir a string
                                    df_chunk[col] = df_chunk[col].astype(str)
                    elif base_db_type == 'oracle':
                        # Procesar tipos de datos específicos de Oracle preservando valores nulos
                        for col in df_chunk.columns:
                            # Manejar tipos específicos de Oracle (CLOB, BLOB, etc.)
                            if df_chunk[col].dtype == 'object':
                                # Para Oracle, convertir tipos object a string PRESERVANDO valores nulos
                                try:
                                    # Verificar si hay datos JSON-like en Oracle (aunque es menos común)
                                    sample = df_chunk[col].dropna().iloc[0] if not df_chunk[col].dropna().empty else None
                                    if sample and isinstance(sample, str) and (sample.startswith('{') or sample.startswith('[')):
                                        try:
                                            import json
                                            # Convertir a string para manejar posibles objetos JSON de Oracle
                                            df_chunk[col] = df_chunk[col].apply(lambda x: json.dumps(x) if x is not None else None)
                                        except:
                                            # Si falla, convertir preservando nulos
                                            df_chunk[col] = df_chunk[col].apply(lambda x: str(x) if x is not None else None)
                                    else:
                                        # Para otros tipos object en Oracle, convertir a string PRESERVANDO NULOS
                                        df_chunk[col] = df_chunk[col].apply(lambda x: str(x) if x is not None else None)
                                except:
                                    # Si hay algún error, convertir preservando nulos
                                    df_chunk[col] = df_chunk[col].apply(lambda x: str(x) if x is not None else None)

                    chunk_rows = len(df_chunk)
                    total_rows += chunk_rows

                    # Buscar la columna de rango de forma insensible a mayúsculas/minúsculas (solo log en primer chunk)
                    rango_columna_real = None
                    if rango_columna:
                        # Crear un diccionario de mapeo de columnas (minúsculas -> nombre real)
                        columnas_map = {col.lower(): col for col in df_chunk.columns}
                        rango_columna_real = columnas_map.get(rango_columna.lower())

                    # Solo mostrar información de columnas en el primer chunk
                    if total_rows == chunk_rows:  # Primer chunk
                        logging.info(f"📊 Columnas encontradas: {len(df_chunk.columns)} columnas")
                        if rango_columna:
                            logging.info(f"📅 rango_columna configurada: '{rango_columna}' -> encontrada: '{rango_columna_real}'")

                    # Particionar por fecha si hay columna de rango
                    if rango_columna_real:
                        from .data_transformer import particionar_por_fecha
                        chunks_por_fecha = particionar_por_fecha(df_chunk, rango_columna_real)

                        # OPTIMIZACIÓN: Acumular chunks por fecha en lugar de subir inmediatamente
                        for fecha_str, df_fecha in chunks_por_fecha.items():
                            if len(df_fecha) > 0:
                                # Limpiar y convertir tipos de datos PRESERVANDO valores nulos
                                for col in df_fecha.columns:
                                    if df_fecha[col].dtype == 'object':
                                        # Convertir a string preservando valores nulos (None -> None, no "None")
                                        df_fecha[col] = df_fecha[col].apply(lambda x: str(x) if x is not None else None)

                                # Agregar la columna 'data_lake_last_modified'
                                df_fecha['data_lake_last_modified'] = pd.Timestamp.now()

                                # Acumular en el diccionario por fecha
                                if fecha_str not in datos_por_fecha:
                                    datos_por_fecha[fecha_str] = []
                                datos_por_fecha[fecha_str].append(df_fecha.copy())

                                logging.debug(f"Acumulando {len(df_fecha)} filas para fecha: {fecha_str}")
                    else:
                        # Subir el chunk a S3 (sin particionamiento por fecha)
                        if chunk_rows > 0:
                            # Crear un timestamp único para cada chunk
                            chunk_suffix = f"_{total_rows}" if chunk_rows == chunk_size else ""
                            s3_key = f"{s3_prefix}{db_parent.upper()}-{tabla_nombre.upper()}_{timestamp_base}{chunk_suffix}.parquet"

                            # Limpiar y convertir tipos de datos antes de guardar en parquet
                            try:
                                # Asegurar que todas las columnas sean de tipos compatibles con parquet
                                for col in df_chunk.columns:
                                    # Convertir explícitamente columnas object a string PRESERVANDO valores nulos
                                    if df_chunk[col].dtype == 'object':
                                        # Convertir a string preservando valores nulos (None -> None, no "None")
                                        df_chunk[col] = df_chunk[col].apply(lambda x: str(x) if x is not None else None)

                                # Agregar la columna 'data_lake_last_modified' con la marca de tiempo actual
                                df_chunk['data_lake_last_modified'] = pd.Timestamp.now()

                                # Subir archivo a S3 con manejo de errores mejorado
                                parquet_buffer = io.BytesIO()
                                df_chunk.to_parquet(parquet_buffer, engine='pyarrow', compression='snappy', index=False)
                                parquet_buffer.seek(0)

                                s3 = boto3.client('s3')
                                s3.put_object(
                                    Bucket=bucket,
                                    Key=s3_key,
                                    Body=parquet_buffer.getvalue()
                                )

                                logging.info(f"Archivo subido: s3://{bucket}/{s3_key}")
                                archivos_subidos.append(s3_key)
                            except Exception as e:
                                logging.error(f"Error al convertir a parquet: {str(e)}")
                                # Intentar guardar como CSV si falla parquet
                                csv_key = s3_key.replace('.parquet', '.csv')
                                csv_buffer = io.BytesIO()
                                df_chunk.to_csv(csv_buffer, index=False)
                                csv_buffer.seek(0)

                                s3 = boto3.client('s3')
                                s3.put_object(
                                    Bucket=bucket,
                                    Key=csv_key,
                                    Body=csv_buffer.getvalue()
                                )

                                logging.info(f"Archivo CSV subido como alternativa: s3://{bucket}/{csv_key}")
                                archivos_subidos.append(csv_key)

                    logging.info(f"Procesadas {total_rows} filas...")

                    # Liberar memoria
                    del df_chunk
                    gc.collect()

                # 🚀 OPTIMIZACIÓN PARALELA: Consolidar archivos en paralelo con PyArrow + zstd
                logging.info(f"🚀 Iniciando consolidación PARALELA de {len(datos_por_fecha)} fechas...")
                consolidacion_start = time.time()

                # Determinar número óptimo de workers (máximo 4 para no saturar)
                max_workers = min(4, len(datos_por_fecha), os.cpu_count() or 1)
                logging.info(f"🔧 Usando {max_workers} workers paralelos para consolidación")

                # Ejecutar consolidación en paralelo
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # Enviar todas las tareas de consolidación
                    future_to_fecha = {}
                    for fecha_str, lista_dfs in datos_por_fecha.items():
                        if lista_dfs:
                            future = executor.submit(
                                consolidar_fecha_individual,
                                fecha_str, lista_dfs, s3_prefix, bucket,
                                timestamp_base, db_parent, tabla_nombre
                            )
                            future_to_fecha[future] = fecha_str

                    # Recoger resultados conforme van completándose
                    for future in as_completed(future_to_fecha):
                        fecha_str = future_to_fecha[future]
                        try:
                            s3_key = future.result()
                            if s3_key:
                                archivos_subidos.append(s3_key)
                                logging.info(f"✅ Consolidación completada para fecha {fecha_str}")
                            else:
                                logging.warning(f"⚠️ Consolidación falló para fecha {fecha_str}")
                        except Exception as e:
                            logging.error(f"❌ Error en consolidación paralela para fecha {fecha_str}: {str(e)}")

                consolidacion_time = time.time() - consolidacion_start
                logging.info(f"🎯 Consolidación paralela completada en {consolidacion_time:.2f} segundos")

                # Liberar memoria de datos acumulados
                del datos_por_fecha
                gc.collect()

                query_time = time.time() - query_start
                total_time = time.time() - start_time
                logging.info(f"\n📊 REPORTE DE RENDIMIENTO OPTIMIZADO:")
                logging.info(f"⚡ Conexión: {conn_time:.2f} segundos")
                if count_time > 0:
                    logging.info(f"📊 Count query: {count_time:.2f} segundos")
                logging.info(f"🔍 Query principal: {query_time:.2f} segundos")
                logging.info(f"🚀 Consolidación paralela: {consolidacion_time:.2f} segundos")
                logging.info(f"⏱️ Tiempo total: {total_time:.2f} segundos")
                logging.info(f"📁 Archivos consolidados: {len(archivos_subidos)}")

                # Calcular mejora de rendimiento estimada
                archivos_originales_estimados = sum(len(lista_dfs) for lista_dfs in datos_por_fecha.values()) if 'datos_por_fecha' in locals() else 0
                if archivos_originales_estimados > len(archivos_subidos):
                    reduccion_archivos = ((archivos_originales_estimados - len(archivos_subidos)) / archivos_originales_estimados) * 100
                    logging.info(f"🎯 Reducción de archivos: {reduccion_archivos:.1f}% ({archivos_originales_estimados} → {len(archivos_subidos)})")

                # Generar archivo de resumen si se proporciona seccion_img
                resumen_generado = False
                if seccion_img and archivos_subidos:
                    try:
                        logging.info(f"Generando archivo de resumen para {db_parent.upper()}/{tabla_nombre}")
                        crear_resumen_etl(
                            archivos_procesados=archivos_subidos,
                            db_parent=db_parent,
                            tabla_nombre=tabla_nombre,
                            seccion_img=seccion_img
                        )
                        logging.info("Archivo de resumen generado exitosamente")
                        resumen_generado = True
                    except Exception as e:
                        logging.error(f"Error al generar archivo de resumen: {str(e)}")

                resultados['registros_totales'] = total_rows
                resultados['archivos_procesados'] = archivos_subidos
                resultados['resumen_generado'] = resumen_generado

                logging.info(f"✅ MODO RÁPIDO COMPLETADO EXITOSAMENTE")
                logging.info(f"✅ Registros procesados: {total_rows:,}")
                logging.info(f"✅ Archivos generados: {len(archivos_subidos)}")
                logging.info(f"✅ Resumen generado: {'Sí' if resumen_generado else 'No'}")

                return resultados

            except Exception as e:
                error_str = str(e)
                if "list indices must be integers or slices, not str" in error_str:
                    # Este es un error conocido que no afecta la funcionalidad principal
                    logging.warning(f"Se produjo un error conocido que no afecta la funcionalidad. Continuando... Detalle: {error_str}")
                    # IMPORTANTE: No retornar error, sino continuar con los resultados exitosos
                    # ya que este error no afecta el procesamiento de datos
                    logging.info(f"Procesamiento completado exitosamente a pesar del error menor")
                    resultados['registros_totales'] = total_rows if 'total_rows' in locals() else 0
                    resultados['archivos_procesados'] = archivos_subidos if 'archivos_subidos' in locals() else []
                    resultados['resumen_generado'] = resumen_generado if 'resumen_generado' in locals() else False
                    return resultados
                else:
                    # Para otros errores, mantener el nivel ERROR
                    logging.error(f"Error en modo rápido: {error_str}")
                    return {'success': False, 'error': error_str}
            finally:
                cursor.close()
                conn.close()
                logging.info("Conexión cerrada")
        except Exception as e:
            logging.error(f"Error en modo rápido: {str(e)}")
            return {'success': False, 'error': str(e)}
