import logging
import pandas as pd
from io import BytesIO
import boto3
from .s3_manager import S3_OUTPUT_BUCKET

def extraer_info_archivo(ruta_archivo: str):
    """
    Extrae información relevante de la ruta de un archivo.
    """
    try:
        partes = ruta_archivo.split('/')
        if len(partes) >= 4:
            return {
                'año': partes[-4],
                'mes': partes[-3],
                'dia': partes[-2],
                'nombre_archivo': partes[-1]
            }
    except Exception as e:
        logging.error(f"Error extrayendo información del archivo {ruta_archivo}: {str(e)}")
    return None

def crear_resumen_etl(archivos_procesados, db_parent: str, tabla_nombre: str, seccion_img: str):
    """
    Agrega datos al archivo de resumen en formato parquet.
    Si el archivo existe, lee los datos existentes y agrega los nuevos.
    """
    try:
        logging.info(f"Iniciando creación de resumen ETL para {tabla_nombre}")
        s3_client = boto3.client('s3')
        resumen_key = f"RESUMEN/{seccion_img}/RESUMEN_{seccion_img}.parquet"

        logging.info(f"Procesando {len(archivos_procesados)} archivos para el resumen")
        nuevos_datos = []
        for archivo in archivos_procesados:
            info = extraer_info_archivo(archivo)
            if info:
                nuevos_datos.append({
                    'db_parent': db_parent.upper(),
                    'tabla_nombre': tabla_nombre,
                    'año': info['año'],
                    'mes': info['mes'],
                    'dia': info['dia'],
                    'estado': 'cargado',
                    'nombre_archivo': info['nombre_archivo']
                })

        if nuevos_datos:
            logging.info(f"Creando DataFrame con {len(nuevos_datos)} registros nuevos")
            df_nuevos = pd.DataFrame(nuevos_datos)

            df_existente = None
            try:
                logging.info(f"Verificando archivo de resumen existente: {resumen_key}")
                response = s3_client.get_object(Bucket=S3_OUTPUT_BUCKET, Key=resumen_key)
                buffer_lectura = BytesIO(response['Body'].read())
                df_existente = pd.read_parquet(buffer_lectura)
                logging.info(f"Leídos {len(df_existente)} registros existentes del archivo de resumen")
            except s3_client.exceptions.NoSuchKey:
                logging.info("No existe archivo de resumen previo, se creará uno nuevo")
            except Exception as e:
                logging.error(f"Error leyendo archivo existente: {str(e)}")
                raise

            if df_existente is not None:
                df_final = pd.concat([df_existente, df_nuevos], ignore_index=True)
                logging.info(f"Agregando {len(df_nuevos)} registros nuevos a los {len(df_existente)} existentes")
            else:
                df_final = df_nuevos
                logging.info(f"Creando nuevo archivo con {len(df_nuevos)} registros")

            logging.info("Generando archivo parquet en memoria...")
            buffer_escritura = BytesIO()
            df_final.to_parquet(buffer_escritura, index=False)
            buffer_escritura.seek(0)

            logging.info(f"Subiendo archivo de resumen a S3: {S3_OUTPUT_BUCKET}/{resumen_key}")
            s3_client.upload_fileobj(buffer_escritura, S3_OUTPUT_BUCKET, resumen_key)
            logging.info(f"✅ Archivo de resumen actualizado exitosamente: {S3_OUTPUT_BUCKET}/{resumen_key}")
            logging.info(f"✅ Total de registros en el resumen: {len(df_final)}")
        else:
            logging.warning("No hay nuevos datos para agregar al archivo de resumen")

        logging.info(f"✅ Creación de resumen ETL completada para {tabla_nombre}")

    except Exception as e:
        logging.error(f"Error al actualizar archivo de resumen: {str(e)}")
        raise

def eliminar_resumen_existente(seccion_img: str):
    """
    Elimina el archivo de resumen existente si existe.
    """
    try:
        s3_client = boto3.client('s3')
        resumen_key = f"RESUMEN/{seccion_img}/RESUMEN_{seccion_img}.parquet"

        try:
            s3_client.head_object(Bucket=S3_OUTPUT_BUCKET, Key=resumen_key)
            logging.info(f"Eliminando archivo de resumen existente: {S3_OUTPUT_BUCKET}/{resumen_key}")
            s3_client.delete_object(Bucket=S3_OUTPUT_BUCKET, Key=resumen_key)
        except s3_client.exceptions.ClientError as e:
            if e.response['Error']['Code'] == '404':
                logging.info(f"No existe archivo de resumen previo para {seccion_img}")
            else:
                raise
    except Exception as e:
        logging.error(f"Error al eliminar archivo de resumen existente: {str(e)}")
        raise
