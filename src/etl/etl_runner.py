import logging
import time
import gc
import datetime
import sys
import psutil
import math
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

import pandas as pd

# Imports locales
from .db_manager import DBManager
from .query_builder import construir_query
from .config_loader import cargar_rango_config
from .data_transformer import (
    limpiar_dataframe,
    inferir_tipos_columnas,
    forzar_tipos_dataframe,
    dividir_dataframe,
    particionar_por_fecha
)
from .rate_limiter import ChunkRateLimiter
from .s3_manager import (
    S3_OUTPUT_BUCKET,
    gestionar_archivos_s3,
    upload_df_chunk_to_s3
)
from .resumen_manager import crear_resumen_etl

# Importar el gestor de modo rápido (importación condicional)
try:
    import jpype
    import jaydebeapi
    from .fast_mode_manager import FastModeManager
    FAST_MODE_AVAILABLE = True
    logging.info("Modo rápido disponible (JDBC).")
except ImportError:
    FAST_MODE_AVAILABLE = False
    logging.warning("Modo rápido no disponible. Instala jpype y jaydebeapi para habilitarlo.")

def ejecutar_query_wrapper(query_info: dict) -> dict:
    """Wrapper para ejecutar queries con control de memoria y rate limiting"""
    rate_limiter = query_info.get('rate_limiter') or ChunkRateLimiter()
    max_retries = 3
    retry_delay = 5

    try:
        query = query_info['query'].strip()
        if query.endswith(';'):
            query = query[:-1]

        connection = query_info['connection']
        tabla_nombre = query_info.get('name', 'DEFAULT')
        db_parent = query_info.get('db_parent', 'DEFAULT')
        seccion_img = query_info.get('seccion_img', None)

        # Verificar si se usa modo rápido
        modo_rapido = query_info.get('modo_rapido', False)
        if modo_rapido and FAST_MODE_AVAILABLE:
            logging.info(f"Activando modo rápido para {tabla_nombre}")
            try:
                # Inicializar el gestor de modo rápido
                fast_mode = FastModeManager()

                # Cargar configuración de rango para el modo rápido
                from .config_loader import cargar_rango_config, cargar_configuracion
                rango_config = cargar_rango_config(tabla_nombre)
                rango_columna_config = rango_config.get('rango_columna', '')

                # Usar la configuración de rango del archivo si está disponible,
                # sino usar la que viene en query_info
                rango_columna_final = rango_columna_config or query_info.get('rango_columna', '')

                # Cargar la configuración completa de la tabla para obtener rango_inicio y rango_fin
                config = cargar_configuracion('queries_config.ini')
                tabla_config = config[tabla_nombre] if tabla_nombre in config else {}

                # Procesar filtros de fecha para el modo rápido (igual que en modo estándar)
                from .query_builder import procesar_fecha
                rango_inicio = procesar_fecha(tabla_config.get("rango_inicio", "").strip("'"))
                rango_fin = procesar_fecha(tabla_config.get("rango_fin", "").strip("'"))

                logging.info(f"Configuración de rango para {tabla_nombre}: rango_columna='{rango_columna_final}', rango_inicio='{rango_inicio}', rango_fin='{rango_fin}'")

                # Ejecutar en modo rápido
                return fast_mode.ejecutar_modo_rapido({
                    'query': query,
                    'db_type': query_info.get('db_type'),
                    'db_config': query_info.get('db_config'),
                    'name': tabla_nombre,
                    'db_parent': db_parent,
                    'rango_columna': rango_columna_final,
                    'rango_inicio': rango_inicio,
                    'rango_fin': rango_fin
                }, seccion_img)
            except Exception as e:
                logging.error(f"Error al ejecutar en modo rápido: {str(e)}. Continuando en modo estándar.")
                # Continuar en modo estándar si falla el modo rápido
        elif modo_rapido and not FAST_MODE_AVAILABLE:
            logging.warning(f"Modo rápido solicitado para {tabla_nombre} pero no está disponible. Usando modo estándar.")

        particiones_limpiadas = set()
        tipos_columnas = None

        fecha_actual = datetime.datetime.now()
        base_path = (
            f"{db_parent.upper()}/{tabla_nombre.upper()}/"
            f"{fecha_actual.year:04d}/{fecha_actual.month:02d}/{fecha_actual.day:02d}"
        )

        # Generar un timestamp base único para toda la ejecución
        timestamp_base = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")

        def generar_s3_key(offset: int, chunk_size: int, sub_idx: int = 0) -> str:
            # Usar el timestamp base en lugar de generar uno nuevo cada vez
            chunk_num = offset // chunk_size
            if sub_idx > 0:
                return f"{base_path}/{db_parent.upper()}-{tabla_nombre.upper()}_{timestamp_base}_chunk_{chunk_num}_part_{sub_idx}.parquet"
            return f"{base_path}/{db_parent.upper()}-{tabla_nombre.upper()}_{timestamp_base}_chunk_{chunk_num}.parquet"

        def procesar_chunk(chunk_info):
            nonlocal tipos_columnas

            # Obtener el valor de modo_rapido del query_info
            modo_rapido = query_info.get('modo_rapido', False)

            with rate_limiter.semaphore:
                rate_limiter.check_memory_and_wait()
                offset, chunk_size = chunk_info
                resultados = None
                registros_totales = 0
                archivos_subidos = []

                try:
                    mem_antes = psutil.Process().memory_info().rss / (1024 * 1024)
                    logging.info(f"Memoria antes de chunk {offset}: {mem_antes:.2f}MB")

                    for intento in range(max_retries):
                        try:
                            # Usar sintaxis específica para cada tipo de base de datos
                            if query_info.get('db_type', '').lower() == 'oracle':
                                # Para Oracle, usar ROWNUM o ROW_NUMBER() OVER()
                                query_chunk = f"""
                                SELECT * FROM (
                                    SELECT a.*, ROWNUM rnum FROM (
                                        {query}
                                    ) a WHERE ROWNUM <= {offset + chunk_size}
                                ) WHERE rnum > {offset}
                                """
                            else:
                                # Para MySQL y PostgreSQL, usar LIMIT y OFFSET
                                query_chunk = f"{query} LIMIT {chunk_size} OFFSET {offset}"

                            logging.info(f"Procesando chunk: OFFSET={offset}, LIMIT={chunk_size}, intento={intento+1}/{max_retries}")

                            with connection.get_cursor() as cursor:
                                # Primero ejecutamos la consulta para obtener la descripción de las columnas
                                cursor.execute(query_chunk)

                                # Obtenemos la descripción de las columnas antes de fetchall
                                if cursor.description is None:
                                    logging.warning(f"La consulta no devolvió descripción de columnas: {query_chunk}")
                                    return {'success': True, 'offset': offset, 'registros': 0}

                                columnas = [desc[0] for desc in cursor.description]

                                # Usar el procesador adaptativo de chunks en lugar de fetchall()
                                from .adaptive_chunk_processor import AdaptiveChunkProcessor
                                processor = AdaptiveChunkProcessor(cursor)
                                resultados = processor.fetchall_in_chunks()

                                # Registrar estadísticas del procesamiento adaptativo
                                stats = processor.get_stats()
                                logging.info(f"Estadísticas de procesamiento adaptativo: {stats}")

                                if not resultados:
                                    logging.info(f"No se encontraron registros para el chunk (offset={offset}, limit={chunk_size})")
                                    return {'success': True, 'offset': offset, 'registros': 0}

                                df_chunk = pd.DataFrame(resultados, columns=columnas)
                                df_chunk = limpiar_dataframe(df_chunk)

                                # Inferir tipos si no se ha hecho
                                if tipos_columnas is None:
                                    tipos_columnas = inferir_tipos_columnas(df_chunk)
                                    logging.info("Tipos de columnas inferidos:")
                                    for col, t in tipos_columnas.items():
                                        logging.info(f"  {col}: {t}")

                                # Forzar tipos
                                df_chunk = forzar_tipos_dataframe(df_chunk, tipos_columnas)

                                # Cargar configuración de rango
                                from .config_loader import cargar_rango_config
                                rango_config = cargar_rango_config(tabla_nombre)
                                rango_columna = rango_config.get('rango_columna', '')

                                # Dividir por tamaño
                                sub_chunks = dividir_dataframe(df_chunk, tamano_objetivo_mb=50.0)

                                for idx, sub_df in enumerate(sub_chunks):
                                    try:
                                        sub_df = forzar_tipos_dataframe(sub_df, tipos_columnas)

                                        # Particionar por fecha
                                        # En ambos modos, particionar por fecha para respetar las subcarpetas
                                        particiones = particionar_por_fecha(sub_df, rango_columna)

                                        # Crear un diccionario para rastrear qué registros ya se han procesado
                                        # Solo se usa en modo estándar para evitar duplicados
                                        registros_procesados = set() if not modo_rapido else None

                                        for particion, df_particion in particiones.items():
                                            if df_particion.empty:
                                                continue

                                            df_particion = forzar_tipos_dataframe(df_particion, tipos_columnas)

                                            # En modo estándar, verificar si hay duplicados
                                            if not modo_rapido:
                                                # Crear claves únicas para cada registro basadas en transfer_id y txn_sequence_number
                                                if 'transfer_id' in df_particion.columns and 'txn_sequence_number' in df_particion.columns:
                                                    # Filtrar registros ya procesados
                                                    claves_registros = set(
                                                        df_particion.apply(
                                                            lambda row: f"{row['transfer_id']}_{row['txn_sequence_number']}",
                                                            axis=1
                                                        )
                                                    )

                                                    # Encontrar registros nuevos (no procesados anteriormente)
                                                    nuevas_claves = claves_registros - registros_procesados

                                                    if len(nuevas_claves) < len(claves_registros):
                                                        logging.info(f"Evitando {len(claves_registros) - len(nuevas_claves)} registros duplicados en partición {particion}")

                                                        # Filtrar solo los registros no procesados
                                                        mask = df_particion.apply(
                                                            lambda row: f"{row['transfer_id']}_{row['txn_sequence_number']}" in nuevas_claves,
                                                            axis=1
                                                        )
                                                        df_particion = df_particion[mask]

                                                        # Si después del filtrado no quedan registros, continuar con la siguiente partición
                                                        if df_particion.empty:
                                                            continue

                                                    # Actualizar el conjunto de registros procesados
                                                    registros_procesados.update(nuevas_claves)

                                            if particion == 'default':
                                                s3_key = generar_s3_key(offset, chunk_size, sub_idx=idx)
                                            else:
                                                s3_key = (
                                                    f"{db_parent.upper()}/{tabla_nombre.upper()}/"
                                                    f"{particion}/"
                                                    f"{db_parent.upper()}-{tabla_nombre.upper()}_{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}"
                                                    f"_chunk_{offset//chunk_size}"
                                                    f"{'_part_' + str(idx) if idx > 0 else ''}"
                                                    f".parquet"
                                                )

                                            carpeta_destino = "/".join(s3_key.split('/')[:-1])
                                            logging.info(f"Verificando limpieza de carpeta: {S3_OUTPUT_BUCKET}/{carpeta_destino}")
                                            gestionar_archivos_s3(S3_OUTPUT_BUCKET, carpeta_destino, modo='limpiar', particiones_limpiadas=particiones_limpiadas)

                                            upload_df_chunk_to_s3(df_particion, S3_OUTPUT_BUCKET, s3_key)
                                            logging.info(f"Chunk subido exitosamente a s3://{S3_OUTPUT_BUCKET}/{s3_key}")

                                            archivos_subidos.append(s3_key)
                                            registros_totales += len(df_particion)

                                    finally:
                                        del sub_df
                                        gc.collect()

                            mem_despues = psutil.Process().memory_info().rss / (1024 * 1024)
                            logging.info(f"Memoria después de chunk {offset}: {mem_despues:.2f}MB")

                            return {
                                'success': True,
                                'offset': offset,
                                'registros': registros_totales,
                                'archivos': archivos_subidos
                            }

                        except Exception as e:
                            logging.warning(f"Error en intento {intento + 1}, reintentando en {retry_delay}s: {e}")
                            if intento < max_retries - 1:
                                time.sleep(retry_delay)
                            else:
                                raise

                except Exception as e:
                    logging.error(f"Error procesando chunk {offset}: {str(e)}", exc_info=True)
                    return {'success': False, 'offset': offset, 'error': str(e)}
                finally:
                    if resultados is not None:
                        del resultados
                    if 'df_chunk' in locals():
                        del df_chunk
                    gc.collect()

        # Obtener total de registros
        # Para Oracle, no usar "AS" para alias de tablas
        if query_info.get('db_type', '').lower() == 'oracle':
            count_query = f"SELECT COUNT(*) as total FROM ({query}) total_count"
        else:
            count_query = f"SELECT COUNT(*) as total FROM ({query}) AS total_count"
        logging.info(f"Ejecutando count query: {count_query}")

        total = 0
        with connection.get_cursor() as cursor:
            for intento in range(max_retries):
                try:
                    cursor.execute(count_query)
                    result = cursor.fetchone()
                    if result is not None:
                        try:
                            total = result['total']
                        except (TypeError, KeyError):
                            total = result[0]
                    break
                except Exception as e:
                    if intento < max_retries - 1:
                        logging.warning(f"Error en count query, intento {intento + 1}: {e}")
                        time.sleep(retry_delay)
                    else:
                        logging.error(f"No se pudo obtener el conteo total: {e}")

        logging.info(f"Total de registros encontrados: {total:,}")

        if total == 0:
            logging.info("No hay registros para procesar")
            return None

        mem = psutil.virtual_memory()
        chunk_size = 200000 if mem.available > 8 * 1024**3 else 100000

        # En modo estándar, procesar todos los registros en un solo chunk para evitar duplicación
        # Para Oracle, también usar un solo chunk debido a problemas de rendimiento con ROWNUM y offsets grandes
        if not modo_rapido or query_info.get('db_type', '').lower() == 'oracle':
            if query_info.get('db_type', '').lower() == 'oracle':
                logging.info(f"Oracle detectado: procesando todos los registros en un solo chunk para evitar problemas de rendimiento con ROWNUM")
            else:
                logging.info(f"Modo estándar: procesando todos los registros en un solo chunk para evitar duplicación")
            chunks_info = [(0, total)]
        else:
            # En modo rápido, dividir en chunks como antes (solo para MySQL y PostgreSQL)
            chunks_info = [(i, chunk_size) for i in range(0, total, chunk_size)]

        logging.info(f"Chunks a procesar: {len(chunks_info)}")
        for chunk_info in chunks_info:
            logging.info(f"  - Chunk: offset={chunk_info[0]}, size={chunk_info[1]}")

        total_registros = 0
        archivos_procesados = []

        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = {executor.submit(procesar_chunk, chunk_info): chunk_info for chunk_info in chunks_info}

            for future in as_completed(futures):
                try:
                    resultado = future.result()
                    if resultado and resultado.get('success'):
                        total_registros += resultado.get('registros', 0)
                        if 'archivos' in resultado:
                            archivos_procesados.extend(resultado['archivos'])
                except Exception as e:
                    chunk_info = futures[future]
                    logging.error(f"Error en chunk {chunk_info}: {str(e)}")

        logging.info(f"Procesamiento completado. Total registros: {total_registros:,}")
        logging.info(f"Total archivos generados: {len(archivos_procesados)}")

        return {
            'registros_totales': total_registros,
            'archivos_procesados': archivos_procesados
        }

    except Exception as e:
        logging.error(f"Error en ejecutar_query_wrapper: {e}", exc_info=True)
        raise

def parsear_valor_booleano(valor_str):
    """
    Parsea un valor booleano desde un string, manejando comentarios

    Args:
        valor_str (str): String con el valor booleano

    Returns:
        bool: Valor booleano parseado
    """
    if not valor_str:
        return False

    # Eliminar cualquier comentario (texto después de #)
    if '#' in valor_str:
        valor_str = valor_str.split('#')[0].strip()

    # Normalizar el valor
    valor_str = valor_str.lower().strip()

    # Evaluar valores booleanos comunes
    if valor_str in ('true', '1', 'yes', 'y', 'on', 't'):
        return True
    elif valor_str in ('false', '0', 'no', 'n', 'off', 'f', ''):
        return False
    else:
        # Valor no reconocido, devolver falso por defecto
        logging.warning(f"Valor booleano no reconocido: '{valor_str}', usando 'false' por defecto")
        return False

def guardar_resultados(tabla: str, resultados: dict, db_parent: str, seccion_img: str = None):
    """
    Guarda los resultados del procesamiento y opcionalmente genera el archivo de resumen
    """
    try:
        print(f"\n=== Iniciando guardado de resultados para tabla: {tabla} ===")
        logging.info(f"Total registros procesados: {resultados.get('registros_totales', 0):,}")
        logging.info(f"Total archivos generados: {len(resultados.get('archivos_procesados', []))}")

        # Solo generar resumen si no se ha generado ya en modo rápido
        if seccion_img and resultados.get('archivos_procesados') and not resultados.get('resumen_generado', False):
            logging.info(f"Generando archivo de resumen para {tabla}...")
            crear_resumen_etl(
                archivos_procesados=resultados['archivos_procesados'],
                db_parent=db_parent,
                tabla_nombre=tabla,
                seccion_img=seccion_img
            )
            logging.info(f"Archivo de resumen generado exitosamente para {tabla}")
        else:
            if resultados.get('resumen_generado', False):
                logging.info(f"Archivo de resumen ya fue generado en modo rápido para {tabla}")
            else:
                logging.info(f"No se requiere generar archivo de resumen para {tabla}")

        print(f"=== Guardado de resultados completado para tabla: {tabla} ===")
        logging.info(f"Guardado de resultados completado exitosamente para {tabla}")

    except Exception as e:
        logging.error(f"Error guardando resultados para {tabla}: {str(e)}")
        print(f"ERROR guardando resultados para {tabla}: {str(e)}")
        raise

def procesar_configuracion(config, config_ejecucion, seccion_img: str):
    """Procesa la configuración y ejecuta las consultas en bloques"""
    print("\n=== Iniciando procesamiento de configuración ===")
    connection_pool = DBManager()
    rate_limiter = ChunkRateLimiter(max_concurrent=2, delay_seconds=2, memory_threshold_mb=800)

    tablas_img = [tabla.strip().upper() for tabla in config_ejecucion[seccion_img].get('tablas', '').split(',')]
    secciones_tablas = []

    logging.info(f"Secciones disponibles en queries_config.ini: {config.sections()}")
    logging.info(f"Tablas a buscar: {tablas_img}")

    for tabla in tablas_img:
        logging.info(f"Buscando tabla: {tabla}")
        if tabla in config.sections():
            secciones_tablas.append(tabla)
            logging.info(f"Tabla encontrada: {tabla}")
        else:
            logging.error(f"No se encontró la tabla: {tabla}")

    print("\n=== Modo ESPECÍFICO - Tablas seleccionadas ===")
    print(f"Consultas a procesar: {', '.join(secciones_tablas)}")

    if not secciones_tablas:
        logging.error("No se encontraron tablas para procesar.")
        logging.error(f"Tablas buscadas: {tablas_img}")
        logging.error(f"Secciones disponibles: {config.sections()}")
        return

    for tabla in secciones_tablas:
        print(f"\n=== Procesando tabla: {tabla} ===")
        logging.info(f"Procesando consulta: {tabla}")
        sql_path = config[tabla].get('sql_path')
        if not sql_path:
            logging.warning(f"No se encontró ruta SQL para {tabla}")
            logging.debug(f"Configuración de tabla {tabla}: {dict(config[tabla])}")
            continue

        db_parent = config[tabla].get('db_parent')
        if not db_parent:
            logging.warning(f"No se encontró configuración de base de datos para {tabla}")
            logging.debug(f"Configuración de tabla {tabla}: {config[tabla]}")
            continue

        try:
            db_type = config[db_parent]['db_type'].lower()

            # Construir la sección de conexión
            conn_section = f"{db_type}_connection"

            # Si la sección no existe, usar la sección base (mysql_connection o postgres_connection)
            base_db_type = db_type.split('_')[0].lower()
            base_conn_section = f"{base_db_type}_connection"

            if conn_section not in config_ejecucion:
                logging.info(f"Sección {conn_section} no encontrada, usando {base_conn_section}")
                conn_section = base_conn_section

            # Buscar la sección específica para el tipo de base de datos
            if db_type in config_ejecucion:
                # Usar la sección específica
                conn_section = db_type
                logging.info(f"Usando sección específica {conn_section} para {db_type}")

                # Para mysql_azulito, forzar el uso del modo rápido
                if db_type == 'mysql_azulito':
                    config[tabla]['modo_rapido'] = 'true'
                    logging.info(f"Forzando modo rápido para {tabla} con tipo {db_type}")

            # Obtener el secreto y la región de la sección de conexión
            secret_name = config_ejecucion[conn_section].get('secret_name')
            region = config_ejecucion[conn_section].get('region')
            logging.info(f"Usando sección {conn_section} con secreto {secret_name} para {db_type}")


            if not secret_name or not region:
                raise ValueError(f"Falta configuración de AWS Secrets Manager para {conn_section}")

            from ..utils.aws_secrets import get_secret
            credentials = get_secret(secret_name, region)

            # Crear la configuración de la base de datos
            db_config = {
                'host': credentials['host'],
                'user': credentials['user'],
                'password': credentials['password'],
                'port': int(credentials['port']),
            }

            # Verificar si hay una base de datos específica en la sección del tipo de base de datos
            if db_type in config_ejecucion and 'database' in config_ejecucion[db_type]:
                db_config['database'] = config_ejecucion[db_type]['database']
                logging.info(f"Usando base de datos de la sección {db_type}: {db_config['database']}")
            # Si no, usar la base de datos del secreto si está disponible
            elif credentials.get('database'):
                db_config['database'] = credentials['database']
                logging.info(f"Usando base de datos del secreto: {db_config['database']}")
            # Si no, usar la base de datos del config
            else:
                db_config['database'] = config[db_parent]['db_name']
                logging.info(f"Usando base de datos del config: {db_config['database']}")

            # Verificar si se debe forzar el modo rápido
            if db_type in config_ejecucion and 'force_fast_mode' in config_ejecucion[db_type]:
                if config_ejecucion[db_type].getboolean('force_fast_mode'):
                    config[tabla]['modo_rapido'] = 'true'
                    logging.info(f"Forzando modo rápido para {tabla} según configuración de {db_type}")

            connection = connection_pool.get_connection(
                db_type=db_type,
                config=db_config,
                db_name=db_parent
            )
            logging.info(f"Conexión establecida exitosamente para {db_parent}")

            rango_columna = config[tabla].get("rango_columna")
            from .query_builder import procesar_fecha
            rango_inicio = procesar_fecha(config[tabla].get("rango_inicio", "").strip("'"))
            rango_fin = procesar_fecha(config[tabla].get("rango_fin", "").strip("'"))

            query = construir_query(
                sql_path,
                rango_columna=rango_columna,
                rango_inicio=rango_inicio,
                rango_fin=rango_fin,
                schema_name=config[db_parent].get('schema_name'),
                db_type=db_type
            )

            resultados = ejecutar_query_wrapper({
                'name': tabla,
                'query': query,
                'connection': connection,
                'db_parent': db_parent,
                'db_type': db_type,
                'db_config': db_config,
                'rango_columna': rango_columna,
                'rate_limiter': rate_limiter,
                'modo_rapido': parsear_valor_booleano(config[tabla].get('modo_rapido', 'false')),
                'seccion_img': seccion_img
            })

            if resultados:
                guardar_resultados(tabla, resultados, db_parent, seccion_img)
                print(f"\n🎉 PROCESAMIENTO COMPLETADO EXITOSAMENTE PARA {tabla}")
                logging.info(f"🎉 Tabla {tabla} procesada completamente - PROCESO FINALIZADO")

        except Exception as e:
            logging.error(f"Error procesando tabla {tabla}: {e}", exc_info=True)
            continue
