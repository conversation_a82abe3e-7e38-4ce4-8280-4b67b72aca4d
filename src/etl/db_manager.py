import boto3
import os
import psycopg2
import psycopg2.extras
import mysql.connector
import importlib
from pathlib import Path
from configparser import ConfigParser
from ..utils.aws_secrets import get_secret

# Importar oracledb de manera condicional para no romper si no está instalado
try:
    import oracledb
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False

def get_project_root():
    """Obtiene la ruta raíz del proyecto"""
    current_file = Path(__file__).resolve()
    return current_file.parent.parent.parent

def load_db_config():
    """Carga la configuración desde S3 usando los parámetros de s3_db_config.ini"""
    # 1. Leer configuración de S3
    s3_config = ConfigParser()
    s3_config.read('config/s3_db_config.ini')

    # 2. Descargar conexion_settings.ini desde S3
    s3 = boto3.client('s3')
    bucket = s3_config['CONFIGS_DB']['bucket']
    key = s3_config['CONFIGS_DB']['prefix'] + 'conexion_settings.ini'

    try:
        response = s3.get_object(Bucket=bucket, Key=key)
        config_content = response['Body'].read().decode('utf-8')

        # 3. Parsear configuración
        config = ConfigParser()
        config.read_string(config_content)

        db_configs = {
            'mysql': get_secret(
                config['mysql']['secret_name'],
                config['mysql']['region']
            ),
            'postgres': get_secret(
                config['postgres']['secret_name'],
                config['postgres']['region']
            )
        }

        # Agregar configuración de Oracle si existe en el archivo de configuración
        if 'oracle' in config:
            db_configs['oracle'] = get_secret(
                config['oracle']['secret_name'],
                config['oracle']['region']
            )

        return db_configs

    except s3.exceptions.NoSuchKey:
        raise RuntimeError(f"El archivo {key} no se encontró en el bucket {bucket}. Verifica que el archivo exista y que la ruta sea correcta.")
    except Exception as e:
        raise RuntimeError(f"Error al obtener configuración desde S3: {str(e)}")

class ConnectionWrapper:
    def __init__(self, connection, db_type='mysql'):
        """
        Inicializa un wrapper para conexiones de base de datos

        Args:
            connection: Objeto de conexión a la base de datos
            db_type: Tipo de base de datos ('mysql', 'postgres', 'oracle')
        """
        self.connection = connection
        self.db_type = db_type.lower()
        self.is_postgres = self.db_type == 'postgres'
        self.is_oracle = self.db_type == 'oracle'

    def get_cursor(self):
        """
        Obtiene un cursor para la conexión actual

        Returns:
            Un cursor configurado según el tipo de base de datos
        """
        if self.is_postgres:
            return self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        elif self.is_oracle:
            # Para Oracle, configuramos el cursor para devolver resultados como diccionarios
            cursor = self.connection.cursor()
            # Configurar el cursor para devolver resultados como diccionarios
            cursor.outputtypehandler = self._output_type_handler
            return cursor
        # Para MySQL y otros
        return self.connection.cursor(dictionary=True)

    def _output_type_handler(self, cursor, name, defaultType, size, precision, scale):
        """
        Manejador de tipos para Oracle que permite devolver resultados como diccionarios
        """
        if defaultType == oracledb.DB_TYPE_CLOB:
            return cursor.var(oracledb.DB_TYPE_LONG, arraysize=cursor.arraysize)
        if defaultType == oracledb.DB_TYPE_BLOB:
            return cursor.var(oracledb.DB_TYPE_LONG_RAW, arraysize=cursor.arraysize)
        if defaultType == oracledb.DB_TYPE_NCLOB:
            return cursor.var(oracledb.DB_TYPE_LONG, arraysize=cursor.arraysize)
        return None

    def close(self):
        self.connection.close()

    def commit(self):
        self.connection.commit()

    def rollback(self):
        self.connection.rollback()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

class DBManager:
    def __init__(self):
        self.config = load_db_config()
        self.mysql_conn = None
        self.postgres_conn = None
        self.oracle_conn = None

    def get_connection(self, db_type, config, db_name):
        """
        Obtiene una conexión a la base de datos según el tipo especificado

        Args:
            db_type (str): Tipo de base de datos ('mysql', 'postgres' o 'oracle')
            config (dict): Configuración de conexión
            db_name (str): Nombre de la base de datos

        Returns:
            ConnectionWrapper: Objeto wrapper que proporciona una interfaz consistente para todos los tipos de conexión
        """
        # Extraer el tipo base (mysql, postgres, oracle) del tipo completo
        base_db_type = db_type.split('_')[0].lower()

        if base_db_type == 'mysql':
            try:
                connection = mysql.connector.connect(**config)
                return ConnectionWrapper(connection, db_type='mysql')
            except Exception as e:
                raise RuntimeError(f"Error al conectar con MySQL ({db_name}, tipo: {db_type}): {str(e)}")
        elif base_db_type == 'postgres':
            try:
                connection = psycopg2.connect(**config)
                return ConnectionWrapper(connection, db_type='postgres')
            except Exception as e:
                raise RuntimeError(f"Error al conectar con PostgreSQL ({db_name}, tipo: {db_type}): {str(e)}")
        elif base_db_type == 'oracle':
            if not ORACLE_AVAILABLE:
                raise RuntimeError("El módulo oracledb no está instalado. Instálalo con 'pip install oracledb'")

            try:
                # Configurar la conexión Oracle
                # Nota: Oracle usa diferentes parámetros de conexión
                oracle_config = {
                    'user': config.get('user'),
                    'password': config.get('password'),
                    'dsn': config.get('dsn', None)
                }

                # Si no hay DSN, construirlo a partir de host, port y service_name/sid
                if not oracle_config['dsn'] and 'host' in config and 'port' in config:
                    # Determinar si usar service_name o sid
                    if 'service_name' in config:
                        oracle_config['dsn'] = f"{config['host']}:{config['port']}/{config['service_name']}"
                    elif 'sid' in config:
                        # Para SID, el formato es ligeramente diferente
                        oracle_config['dsn'] = f"{config['host']}:{config['port']}:{config['sid']}"
                    # Si hay database pero no service_name ni sid, usar database como service_name
                    elif 'database' in config:
                        oracle_config['dsn'] = f"{config['host']}:{config['port']}/{config['database']}"
                        print(f"Usando database '{config['database']}' como service_name para Oracle")
                    else:
                        raise ValueError("La configuración de Oracle debe incluir 'service_name', 'sid' o 'database'")

                # Eliminar None values
                oracle_config = {k: v for k, v in oracle_config.items() if v is not None}

                # Conectar a Oracle
                connection = oracledb.connect(**oracle_config)
                return ConnectionWrapper(connection, db_type='oracle')
            except Exception as e:
                raise RuntimeError(f"Error al conectar con Oracle ({db_name}, tipo: {db_type}): {str(e)}")
        else:
            raise ValueError(f"Tipo de base de datos no soportado: {db_type} (tipo base: {base_db_type})")

    def get_mysql_connection(self):
        """Obtiene una conexión a MySQL usando credenciales de AWS Secrets Manager"""
        if not self.mysql_conn or not self.mysql_conn.connection.is_connected():
            try:
                # Crear una copia de la configuración y remover la base de datos si existe
                mysql_config = self.config['mysql'].copy()
                mysql_config.pop('database', None)  # Remover database si existe

                connection = mysql.connector.connect(**mysql_config)
                self.mysql_conn = ConnectionWrapper(connection, db_type='mysql')
            except Exception as e:
                raise RuntimeError(f"Error al conectar con MySQL: {str(e)}")
        return self.mysql_conn

    def get_postgres_connection(self):
        """Obtiene una conexión a PostgreSQL usando credenciales de AWS Secrets Manager"""
        if not self.postgres_conn or self.postgres_conn.connection.closed:
            try:
                connection = psycopg2.connect(**self.config['postgres'])
                self.postgres_conn = ConnectionWrapper(connection, db_type='postgres')
            except Exception as e:
                raise RuntimeError(f"Error al conectar con PostgreSQL: {str(e)}")
        return self.postgres_conn

    def get_oracle_connection(self):
        """Obtiene una conexión a Oracle usando credenciales de AWS Secrets Manager"""
        if not ORACLE_AVAILABLE:
            raise RuntimeError("El módulo oracledb no está instalado. Instálalo con 'pip install oracledb'")

        if not self.oracle_conn or self.oracle_conn.connection.ping() != 0:
            try:
                # Verificar que la configuración de Oracle existe
                if 'oracle' not in self.config:
                    raise RuntimeError("No se encontró configuración para Oracle en AWS Secrets Manager")

                # Crear una copia de la configuración
                oracle_config = self.config['oracle'].copy()

                # Configurar la conexión Oracle
                connection_params = {
                    'user': oracle_config.get('user'),
                    'password': oracle_config.get('password'),
                    'dsn': oracle_config.get('dsn', None)
                }

                # Si no hay DSN, construirlo a partir de host, port y service_name/sid
                if not connection_params['dsn'] and 'host' in oracle_config and 'port' in oracle_config:
                    # Determinar si usar service_name o sid
                    if 'service_name' in oracle_config:
                        connection_params['dsn'] = f"{oracle_config['host']}:{oracle_config['port']}/{oracle_config['service_name']}"
                    elif 'sid' in oracle_config:
                        # Para SID, el formato es ligeramente diferente
                        connection_params['dsn'] = f"{oracle_config['host']}:{oracle_config['port']}:{oracle_config['sid']}"
                    # Si hay database pero no service_name ni sid, usar database como service_name
                    elif 'database' in oracle_config:
                        connection_params['dsn'] = f"{oracle_config['host']}:{oracle_config['port']}/{oracle_config['database']}"
                        print(f"Usando database '{oracle_config['database']}' como service_name para Oracle")
                    else:
                        raise ValueError("La configuración de Oracle debe incluir 'service_name', 'sid' o 'database'")

                # Eliminar None values
                connection_params = {k: v for k, v in connection_params.items() if v is not None}

                # Conectar a Oracle
                connection = oracledb.connect(**connection_params)
                self.oracle_conn = ConnectionWrapper(connection, db_type='oracle')
            except Exception as e:
                raise RuntimeError(f"Error al conectar con Oracle: {str(e)}")
        return self.oracle_conn

    def close_connections(self):
        """Cierra todas las conexiones activas"""
        if self.mysql_conn:
            self.mysql_conn.close()
        if self.postgres_conn:
            self.postgres_conn.close()
        if self.oracle_conn:
            self.oracle_conn.close()
