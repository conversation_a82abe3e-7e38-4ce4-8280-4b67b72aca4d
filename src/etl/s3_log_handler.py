import logging
import boto3
from io import StringIO
import datetime
import configparser
import os
from typing import Optional

class S3LogHandler(logging.Handler):
    """
    Handler personalizado que acumula logs en memoria y los sube a S3.
    Los logs se organizan por sección IMG y se suben automáticamente al cerrar
    el logger o cuando se llama a flush().
    """

    def __init__(self, bucket_name: str, seccion_img: str, level: int = logging.INFO):
        """
        Inicializa el handler con el bucket S3 y la sección IMG.
        
        Args:
            bucket_name (str): Nombre del bucket S3 para logs
            seccion_img (str): Sección IMG (ej: IMG_FLOW_09)
            level (int): Nivel de logging (default: INFO)
        """
        super().__init__(level)
        self.bucket_name = bucket_name
        self.seccion_img = seccion_img
        self.s3_client = boto3.client('s3')
        self.log_buffer = StringIO()
        self.start_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Configurar el formato por defecto
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.setFormatter(formatter)

    def emit(self, record: logging.LogRecord) -> None:
        """
        Acumula cada mensaje de log en el buffer de memoria.
        
        Args:
            record (logging.LogRecord): Registro de log a procesar
        """
        try:
            msg = self.format(record)
            self.log_buffer.write(msg + '\n')
        except Exception as e:
            self.handleError(record)

    def flush(self) -> None:
        """
        Sube el contenido actual del buffer a S3 y limpia el buffer.
        Si hay error en la subida, lo registra pero no interrumpe el proceso.
        """
        if self.log_buffer.tell() > 0:
            self.log_buffer.seek(0)
            
            # Construir la clave S3 con el formato: IMG_FLOW_XX/etl_process_YYYYMMDD_HHMMSS.log
            s3_key = f"{self.seccion_img}/etl_process_{self.start_time}.log"
            
            try:
                # Subir el contenido del buffer a S3
                self.s3_client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Body=self.log_buffer.getvalue().encode('utf-8')
                )
                # Usar otro logger para no crear un ciclo infinito
                logging.getLogger('root').debug(
                    f"Log subido exitosamente a s3://{self.bucket_name}/{s3_key}"
                )
            except Exception as e:
                # Registrar error usando otro logger para evitar recursión
                logging.getLogger('root').error(
                    f"Error subiendo log a S3: {e}", 
                    exc_info=True
                )
            finally:
                # Limpiar el buffer actual y crear uno nuevo
                self.log_buffer.close()
                self.log_buffer = StringIO()

    def close(self) -> None:
        """
        Asegura que todos los logs pendientes se suban a S3 antes de cerrar.
        """
        try:
            self.flush()
        finally:
            self.log_buffer.close()
            super().close()

def configure_s3_logging(seccion_img: str, console_output: bool = True) -> None:
    """
    Configura el sistema de logging para usar S3LogHandler.
    
    Args:
        seccion_img (str): Sección IMG para organizar los logs
        console_output (bool): Si True, también muestra logs en consola
    """
    # Leer configuración del bucket desde s3_log_config.ini local
    try:
        config = configparser.ConfigParser()
        config_path = os.path.join('logs', 's3_log_config.ini')
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"No se encontró el archivo de configuración: {config_path}")
            
        config.read(config_path)
        bucket_name = config['CONFIGS_LOG']['bucket']
    except Exception as e:
        raise RuntimeError(f"Error cargando configuración de logs S3: {e}")

    # Crear y configurar el handler de S3
    s3_handler = S3LogHandler(bucket_name=bucket_name, seccion_img=seccion_img)
    
    # Configurar el logger root
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # Limpiar handlers existentes
    for handler in root_logger.handlers[:]: 
        root_logger.removeHandler(handler)
        # Asegurar que los handlers de tipo FileHandler se cierren correctamente
        if hasattr(handler, 'close'):
            handler.close()
    
    # Desactivar la creación automática de archivos de log
    logging.lastResort = None
    
    # Agregar el handler de S3
    root_logger.addHandler(s3_handler)
    
    # Opcionalmente agregar output a consola
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        )
        root_logger.addHandler(console_handler)
