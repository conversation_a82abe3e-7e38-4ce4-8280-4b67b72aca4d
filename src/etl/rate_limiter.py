import threading
import time
import gc
import psutil
from collections import deque
import logging

class ChunkRateLimiter:
    """
    Controla la tasa de procesamiento de chunks y el uso de memoria
    """
    def __init__(self, max_concurrent=2, delay_seconds=2, memory_threshold_mb=800):
        self.semaphore = threading.Semaphore(max_concurrent)
        self.delay = delay_seconds
        self.memory_threshold_mb = memory_threshold_mb
        self.last_execution = 0
        self.lock = threading.Lock()
        self.memory_history = deque(maxlen=5)
        self.gc_threshold = 0.9

    def check_memory_and_wait(self):
        """
        Verifica el uso de memoria y espera si es necesario.
        """
        with self.lock:
            current_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            self.memory_history.append(current_memory)

            if len(self.memory_history) >= 2:
                memory_trend = self.memory_history[-1] - self.memory_history[0]
            else:
                memory_trend = 0

            if memory_trend > 100:
                logging.warning(f"Tendencia de memoria alta detectada: +{memory_trend:.2f}MB")
                gc.collect()
                time.sleep(self.delay * 2)
                return True

            if current_memory > self.memory_threshold_mb * self.gc_threshold:
                logging.warning(f"Memoria cerca del umbral: {current_memory:.2f}MB")
                gc.collect()

            if current_memory > self.memory_threshold_mb:
                logging.warning(f"Memoria alta detectada: {current_memory:.2f}MB. Esperando...")
                gc.collect()
                time.sleep(self.delay * 2)
                return True

            current_time = time.time()
            if self.last_execution > 0:
                time_since_last = current_time - self.last_execution
                if time_since_last < self.delay:
                    time.sleep(self.delay - time_since_last)
            self.last_execution = time.time()
            return False

    def get_memory_stats(self):
        if not self.memory_history:
            return None
        return {
            'current': self.memory_history[-1],
            'avg': sum(self.memory_history) / len(self.memory_history),
            'max': max(self.memory_history),
            'min': min(self.memory_history),
            'trend': self.memory_history[-1] - self.memory_history[0] if len(self.memory_history) >= 2 else 0
        }
