import json
import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError

def get_secret(secret_name: str, region_name: str) -> dict:
    """
    Obtiene el secreto desde AWS Secrets Manager.
    Valida si el secreto pertenece a MySQL, PostgreSQL u Oracle y lo formatea correctamente.
    """
    client = boto3.client('secretsmanager', region_name=region_name)

    try:
        response = client.get_secret_value(SecretId=secret_name)
        secret = response.get('SecretString')

        if not secret:
            raise ValueError(f"El secreto '{secret_name}' no contiene datos en formato de cadena")

        secret_dict = json.loads(secret)

        # Formateo para MySQL
        if any(key.startswith("MYSQL_") for key in secret_dict):
            # Guardar el host original para debugging
            original_host = secret_dict["MYSQL_HOST"]
            print(f"Host original del secreto {secret_name}: {original_host}")

            return {
                "host": original_host,  # Usar el host original del secreto
                "port": secret_dict.get("MYSQL_PORT", 3306),
                "user": secret_dict["MYSQL_USERNAME"],
                "password": secret_dict["MYSQL_PASSWORD"],
                "database": secret_dict.get("MYSQL_DATABASE")
            }

        # Formateo para PostgreSQL
        elif any(key.startswith("POSTGRESQL_") for key in secret_dict):
            return {
                "host": secret_dict["POSTGRESQL_HOST"],
                "port": secret_dict.get("POSTGRESQL_PORT", 5432),
                "user": secret_dict["POSTGRESQL_USERNAME"],
                "password": secret_dict["POSTGRESQL_PASSWORD"],
                "dbname": secret_dict.get("POSTGRESQL_DATABASE")
            }

        # Formateo para Oracle
        elif any(key.startswith("ORACLE_") for key in secret_dict) or "ORACLE_HOST" in secret_dict:
            # Si no hay service_name o sid, pero hay database, usar database como service_name
            service_name = secret_dict.get("ORACLE_SERVICE_NAME", "")
            sid = secret_dict.get("ORACLE_SID", "")
            database = secret_dict.get("ORACLE_DATABASE", "")

            # Si no hay service_name ni sid, pero hay database, usar database como service_name
            if not service_name and not sid and database:
                service_name = database
                print(f"Usando ORACLE_DATABASE '{database}' como service_name para Oracle")

            return {
                "host": secret_dict.get("ORACLE_HOST", ""),
                "port": secret_dict.get("ORACLE_PORT", 1521),
                "user": secret_dict.get("ORACLE_USERNAME", ""),
                "password": secret_dict.get("ORACLE_PASSWORD", ""),
                "service_name": service_name,
                "sid": sid
            }

        # Intento de manejo genérico si no coincide con los formatos anteriores
        elif "host" in secret_dict or "HOST" in secret_dict:
            # Intentar mapear claves comunes
            return {
                "host": secret_dict.get("host", secret_dict.get("HOST", "")),
                "port": secret_dict.get("port", secret_dict.get("PORT", 1521)),
                "user": secret_dict.get("user", secret_dict.get("username", secret_dict.get("USERNAME", ""))),
                "password": secret_dict.get("password", secret_dict.get("PASSWORD", "")),
                "service_name": secret_dict.get("service_name", secret_dict.get("SERVICE_NAME", "")),
                "sid": secret_dict.get("sid", secret_dict.get("SID", ""))
            }

        else:
            # Si no podemos determinar el formato, devolvemos el diccionario tal cual
            print(f"Advertencia: Formato no reconocido para el secreto '{secret_name}'. Claves disponibles: {list(secret_dict.keys())}")
            return secret_dict

    except (NoCredentialsError, PartialCredentialsError) as e:
        raise RuntimeError("Error de credenciales AWS") from e
    except Exception as e:
        raise RuntimeError(f"Error al obtener secreto: {str(e)}") from e
