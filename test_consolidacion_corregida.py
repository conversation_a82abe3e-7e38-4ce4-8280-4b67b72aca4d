#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para probar la consolidación con la corrección de normalización
"""

import os
import sys
import pandas as pd
import boto3
import logging
from datetime import datetime

# Configurar ruta para imports
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Suprimir warnings
import warnings
warnings.filterwarnings('ignore')

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Import del sistema de consolidación
from app_raw_consolidado import (
    leer_archivo_resumen,
    agrupar_archivos_por_tabla,
    consolidar_archivos_tabla_incremental
)

def test_consolidacion_corregida():
    """Prueba la consolidación con la corrección aplicada"""
    
    print("\n" + "="*80)
    print("🧪 PRUEBA DE CONSOLIDACIÓN CON CORRECCIÓN APLICADA")
    print("="*80)
    
    tabla_especifica = "MTX_WALLET_ORA"
    
    print(f"📋 Tabla a procesar: {tabla_especifica}")
    print(f"⏰ Iniciando prueba: {datetime.now()}")
    
    try:
        print(f"\n🔄 Ejecutando consolidación...")
        resultado = consolidar_tabla_especifica(tabla_especifica)
        
        if resultado:
            print(f"✅ Consolidación exitosa!")
            print(f"📊 Resultado: {resultado}")
            
            # Ahora vamos a comparar nuevamente para ver si se arregló
            print(f"\n🔍 Verificando si la corrección funcionó...")
            verificar_datos_despues_correccion()
            
        else:
            print(f"❌ Consolidación falló")
            
    except Exception as e:
        print(f"❌ Error durante la consolidación: {str(e)}")
        import traceback
        traceback.print_exc()

def verificar_datos_despues_correccion():
    """Verifica que los datos estén correctos después de la corrección"""
    
    from comparar_origen_consolidado_especifico import cargar_archivo_s3
    
    print(f"\n📥 Cargando archivos para verificación...")
    
    # Cargar datos
    bucket_bronze = "prd-datalake-bronze-zone-637423440311"
    bucket_silver = "prd-datalake-silver-zone-637423440311"
    
    archivo_origen = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250530-164129_chunk_0.parquet"
    archivo_consolidado = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado.parquet"
    
    df_origen = cargar_archivo_s3(bucket_bronze, archivo_origen, "ORIGEN")
    df_consolidado = cargar_archivo_s3(bucket_silver, archivo_consolidado, "CONSOLIDADO")
    
    if df_origen is not None and df_consolidado is not None:
        print(f"\n✅ Ambos archivos cargados correctamente")
        
        # Verificar algunos campos clave que antes se perdían
        print(f"\n🔍 VERIFICANDO CAMPOS QUE ANTES SE PERDÍAN:")
        
        campos_criticos = ['PAYMENT_METHOD_TYPE_ID', 'USER_GRADE', 'USER_TYPE']
        
        for campo in campos_criticos:
            if campo in df_origen.columns and campo in df_consolidado.columns:
                # Verificar que los valores no estén vacíos
                valores_origen = df_origen[campo].dropna()
                valores_consolidado = df_consolidado[campo].dropna()
                
                no_vacios_origen = (valores_origen != '').sum()
                no_vacios_consolidado = (valores_consolidado != '').sum()
                
                print(f"   📊 {campo}:")
                print(f"      Origen: {no_vacios_origen} valores no vacíos de {len(valores_origen)}")
                print(f"      Consolidado: {no_vacios_consolidado} valores no vacíos de {len(valores_consolidado)}")
                
                if no_vacios_consolidado > 0:
                    print(f"      ✅ Campo preservado correctamente")
                    
                    # Mostrar muestra de valores
                    muestra_consolidado = valores_consolidado[valores_consolidado != ''].head(3).tolist()
                    print(f"      📋 Muestra de valores: {muestra_consolidado}")
                else:
                    print(f"      ❌ Campo aún se está perdiendo")
        
        # Verificar WALLET_NUMBER que es la clave primaria
        if 'WALLET_NUMBER' in df_consolidado.columns:
            wallet_unicos = df_consolidado['WALLET_NUMBER'].nunique()
            print(f"\n🔑 WALLET_NUMBER únicos en consolidado: {wallet_unicos}")
            if wallet_unicos == 80:
                print(f"   ✅ Todas las claves primarias preservadas")
            else:
                print(f"   ❌ Se perdieron claves primarias")
    else:
        print(f"❌ No se pudieron cargar los archivos para verificación")

if __name__ == "__main__":
    test_consolidacion_corregida()
