#!/usr/bin/env python3
"""
Test final con estructura realista de MTX_WALLET_ORA
Simula el escenario real Bronze → Silver con preservación completa de estructura
"""

import pandas as pd
import numpy as np
import sys
import os

# Agregar el directorio actual al path para importar la función
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar la función corregida
from app_raw_consolidado_puro import alinear_tipos_datos_puro

def test_mtx_wallet_realista():
    """Test con estructura realista de MTX_WALLET_ORA"""
    
    print("🎯 TEST FINAL - ESTRUCTURA REALISTA MTX_WALLET_ORA")
    print("=" * 65)
    
    # Simular datos del Bronze Zone (origen) - estructura real
    df_bronze = pd.DataFrame({
        'TRANSACTION_ID': ['TXN_12345', 'TXN_12346', 'TXN_12347', 'TXN_12348'],  # object
        'USER_ID': [1001, 1002, np.nan, 1004],  # float64 con NaN
        'ISSUER_ID': [np.nan, 'BANK_002', np.nan, 'BANK_004'],  # object con NaN 
        'AMOUNT': [150.75, 200.50, 75.25, 300.00],  # float64
        'CURRENCY': ['USD', 'EUR', 'USD', 'GBP'],  # object
        'TRANSACTION_DATE': pd.to_datetime([
            '2024-01-15 14:30:22', 
            '2024-01-15 15:45:18', 
            '2024-01-15 16:20:45', 
            '2024-01-15 17:35:12'
        ]),  # datetime64[ns]
        'STATUS': ['COMPLETED', 'PENDING', 'FAILED', 'COMPLETED'],  # object
        'IS_VALID': [True, False, np.nan, True],  # object con boolean y NaN
        'FEES': [5.50, 7.25, 0.0, np.nan],  # float64 con NaN
        'MERCHANT_CATEGORY': pd.Categorical(['RETAIL', 'FOOD', 'RETAIL', 'TRAVEL']),  # category
    })
    
    # Simular datos del Silver Zone (consolidado existente) - tipos diferentes
    df_silver_existente = pd.DataFrame({
        'TRANSACTION_ID': [12340, 12341, 12342],  # int64 en lugar de object
        'USER_ID': ['1000', '1001', '1002'],  # object en lugar de float64
        'ISSUER_ID': [1.0, 2.0, 3.0],  # float64 en lugar de object
        'AMOUNT': ['100.25', '125.50', '200.75'],  # object en lugar de float64
        'CURRENCY': [1, 2, 3],  # int64 en lugar de object
        'TRANSACTION_DATE': [
            '2024-01-14 10:15:30', 
            '2024-01-14 11:20:45', 
            '2024-01-14 12:35:10'
        ],  # object en lugar de datetime64[ns]
        'STATUS': [1, 2, 3],  # int64 en lugar de object
        'IS_VALID': ['True', 'False', 'True'],  # object string en lugar de boolean
        'FEES': ['2.50', '3.75', '1.25'],  # object en lugar de float64
        'MERCHANT_CATEGORY': ['A', 'B', 'C'],  # object en lugar de category
    })
    
    print("📊 ESTRUCTURA BRONZE ZONE (ORIGEN - debe preservarse):")
    print("-" * 65)
    for col in df_bronze.columns:
        dtype = df_bronze[col].dtype
        nan_count = df_bronze[col].isna().sum()
        sample_val = str(df_bronze[col].iloc[0]) if not pd.isna(df_bronze[col].iloc[0]) else 'NaN'
        print(f"  {col:20} | {str(dtype):15} | NaN:{nan_count} | Ej: {sample_val}")
    
    print(f"\n📊 ESTRUCTURA SILVER ZONE ACTUAL (CONSOLIDADO):")
    print("-" * 65)
    for col in df_silver_existente.columns:
        dtype = df_silver_existente[col].dtype
        nan_count = df_silver_existente[col].isna().sum()
        sample_val = str(df_silver_existente[col].iloc[0])
        print(f"  {col:20} | {str(dtype):15} | NaN:{nan_count} | Ej: {sample_val}")
    
    # Aplicar alineación
    print(f"\n🔧 APLICANDO ALINEACIÓN AUTOMÁTICA...")
    df_silver_aligned, df_bronze_aligned = alinear_tipos_datos_puro(df_silver_existente, df_bronze)
    
    print(f"\n📊 ESTRUCTURA DESPUÉS DE ALINEACIÓN:")
    print("-" * 65)
    print("SILVER ZONE ALINEADO (debe adoptar estructura del Bronze):")
    for col in df_silver_aligned.columns:
        dtype = df_silver_aligned[col].dtype
        nan_count = df_silver_aligned[col].isna().sum()
        sample_val = str(df_silver_aligned[col].iloc[0]) if not pd.isna(df_silver_aligned[col].iloc[0]) else 'NaN'
        print(f"  {col:20} | {str(dtype):15} | NaN:{nan_count} | Ej: {sample_val}")
    
    # Verificación crítica
    print(f"\n🎯 VERIFICACIÓN CRÍTICA - ¿ESTRUCTURA PRESERVADA?")
    print("=" * 65)
    
    estructura_correcta = True
    for col in df_bronze.columns:
        tipo_bronze = df_bronze[col].dtype
        tipo_silver_final = df_silver_aligned[col].dtype
        
        # Verificar preservación de tipo
        if pd.api.types.is_datetime64_any_dtype(tipo_bronze) and pd.api.types.is_datetime64_any_dtype(tipo_silver_final):
            print(f"✅ {col:20} | {tipo_bronze} → {tipo_silver_final} (datetime preservado)")
        elif str(tipo_bronze) == str(tipo_silver_final):
            print(f"✅ {col:20} | {tipo_bronze} (exacto)")
        else:
            print(f"❌ {col:20} | BRONZE: {tipo_bronze} ≠ SILVER: {tipo_silver_final}")
            estructura_correcta = False
    
    # Verificar preservación de NaN
    print(f"\n🔍 VERIFICACIÓN DE NaN:")
    print("-" * 65)
    nan_preservados = True
    for col in df_bronze.columns:
        if df_silver_aligned[col].dtype == 'object':
            string_nan_count = (df_silver_aligned[col] == 'nan').sum()
            if string_nan_count > 0:
                print(f"❌ {col:20} | {string_nan_count} conversiones NaN → 'nan'")
                nan_preservados = False
            else:
                print(f"✅ {col:20} | Sin conversiones NaN → 'nan'")
    
    # Simulación del concat final
    print(f"\n🔗 SIMULACIÓN CONCAT FINAL (Silver + Bronze):")
    print("-" * 65)
    
    try:
        df_final = pd.concat([df_silver_aligned, df_bronze_aligned], ignore_index=True)
        
        print(f"✅ Concatenación exitosa: {len(df_final)} filas totales")
        print(f"   Silver Zone original: {len(df_silver_existente)} filas")
        print(f"   Bronze Zone nuevo:    {len(df_bronze)} filas")
        
        # Verificar estructura final
        print(f"\nEstructura final unificada:")
        for col in df_final.columns:
            dtype = df_final[col].dtype
            nan_count = df_final[col].isna().sum()
            string_nan = (df_final[col] == 'nan').sum() if dtype == 'object' else 0
            print(f"  {col:20} | {str(dtype):15} | NaN:{nan_count:2} | 'nan':{string_nan}")
        
        concat_ok = True
        
    except Exception as e:
        print(f"❌ Error en concatenación: {e}")
        concat_ok = False
    
    # Resultado final
    print(f"\n{'='*65}")
    print(f"📊 RESULTADO FINAL DEL TEST:")
    print(f"  🏗️  Estructura preservada:     {'✅ SÍ' if estructura_correcta else '❌ NO'}")
    print(f"  🔢 NaN preservados:           {'✅ SÍ' if nan_preservados else '❌ NO'}")
    print(f"  🔗 Concatenación compatible:  {'✅ SÍ' if concat_ok else '❌ NO'}")
    
    if estructura_correcta and nan_preservados and concat_ok:
        print(f"\n🎉 ¡ÉXITO COMPLETO!")
        print(f"   ✅ La función es 100% genérica")
        print(f"   ✅ Funciona automáticamente para cualquier tabla") 
        print(f"   ✅ Bronze Zone y Silver Zone 'como dos gotas de agua' 💧💧")
        print(f"   ✅ Lista para usar en producción")
        return True
    else:
        print(f"\n❌ Necesita correcciones adicionales")
        return False

if __name__ == "__main__":
    test_mtx_wallet_realista()
