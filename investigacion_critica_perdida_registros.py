#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INVESTIGACIÓN CRÍTICA: Pérdida de registros únicos
==================================================
Como Data Engineer, NO podemos perder registros que no son duplicados
"""

import pandas as pd
import boto3
from io import BytesIO
from datetime import datetime

print("🚨 INVESTIGACIÓN CRÍTICA - PÉRDIDA DE REGISTROS ÚNICOS")
print("=" * 70)
print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Configuración
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"
ARCHIVO_ORIGEN = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-080250_chunk_0.parquet"
ARCHIVO_CONSOLIDADO = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
CLAVE_PRIMARIA = "WALLET_NUMBER"

def leer_parquet_s3(bucket, key, nombre):
    """Lee un archivo parquet desde S3."""
    try:
        print(f"📥 Leyendo {nombre}...")
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        parquet_content = response['Body'].read()
        df = pd.read_parquet(BytesIO(parquet_content))
        print(f"   ✅ {len(df)} registros, {len(df.columns)} columnas")
        return df
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return pd.DataFrame()

try:
    # 1. Leer archivos
    df_origen = leer_parquet_s3(S3_BRONZE_BUCKET, ARCHIVO_ORIGEN, "ARCHIVO ORIGEN")
    df_consolidado = leer_parquet_s3(S3_SILVER_BUCKET, ARCHIVO_CONSOLIDADO, "ARCHIVO CONSOLIDADO")

    if df_origen.empty or df_consolidado.empty:
        print("❌ No se pudieron leer los archivos")
        exit(1)

    # 2. VERIFICACIÓN CRÍTICA: ¿Son realmente únicos en origen?
    print(f"\n🔍 VERIFICACIÓN DE UNICIDAD EN ORIGEN:")
    total_origen = len(df_origen)
    unicos_origen = df_origen[CLAVE_PRIMARIA].nunique()
    print(f"   📊 Total registros origen: {total_origen}")
    print(f"   📊 Valores únicos {CLAVE_PRIMARIA}: {unicos_origen}")
    print(f"   📊 ¿Hay duplicados en origen?: {'SÍ' if total_origen > unicos_origen else 'NO'}")

    if total_origen > unicos_origen:
        duplicados_origen = total_origen - unicos_origen
        print(f"   ⚠️  DUPLICADOS EN ORIGEN: {duplicados_origen}")
        conteos = df_origen[CLAVE_PRIMARIA].value_counts()
        duplicados_valores = conteos[conteos > 1]
        print(f"   📋 {CLAVE_PRIMARIA} duplicados:")
        for wallet, cantidad in duplicados_valores.head(10).items():
            print(f"       {wallet}: {cantidad} registros")
    else:
        print(f"   ✅ ORIGEN ES COMPLETAMENTE ÚNICO")

    # 3. VERIFICACIÓN CRÍTICA: ¿Qué registros únicos se perdieron?
    print(f"\n🔍 ANÁLISIS DE REGISTROS PERDIDOS:")
    origen_set = set(df_origen[CLAVE_PRIMARIA])
    consolidado_set = set(df_consolidado[CLAVE_PRIMARIA])
    
    perdidos = origen_set - consolidado_set
    ganados = consolidado_set - origen_set
    
    print(f"   📊 {CLAVE_PRIMARIA} en origen: {len(origen_set)}")
    print(f"   📊 {CLAVE_PRIMARIA} en consolidado: {len(consolidado_set)}")
    print(f"   📊 {CLAVE_PRIMARIA} perdidos: {len(perdidos)}")
    print(f"   📊 {CLAVE_PRIMARIA} nuevos en consolidado: {len(ganados)}")

    # 4. MOSTRAR REGISTROS PERDIDOS ESPECÍFICOS
    if len(perdidos) > 0:
        print(f"\n❌ REGISTROS ÚNICOS PERDIDOS ({len(perdidos)} total):")
        perdidos_lista = sorted(list(perdidos))
        
        for i, wallet_perdido in enumerate(perdidos_lista[:15]):  # Mostrar 15 ejemplos
            registro_perdido = df_origen[df_origen[CLAVE_PRIMARIA] == wallet_perdido].iloc[0]
            print(f"       {i+1:2d}. {wallet_perdido}")
            print(f"           DATA_LAKE_PARTITION_DATE: {registro_perdido.get('DATA_LAKE_PARTITION_DATE', 'N/A')}")
            print(f"           CREATED_ON: {registro_perdido.get('CREATED_ON', 'N/A')}")
            print(f"           MODIFIED_ON: {registro_perdido.get('MODIFIED_ON', 'N/A')}")
        
        if len(perdidos_lista) > 15:
            print(f"       ... y {len(perdidos_lista)-15} más")

    # 5. VERIFICAR SI EL CONSOLIDADO TIENE DATOS HISTÓRICOS CONFLICTIVOS
    print(f"\n🔍 ANÁLISIS DEL CONSOLIDADO HISTÓRICO:")
    
    if 'DATA_LAKE_PARTITION_DATE' in df_consolidado.columns:
        fechas_consolidado = df_consolidado['DATA_LAKE_PARTITION_DATE'].value_counts().sort_index()
        print(f"   📅 Fechas en consolidado:")
        for fecha, cantidad in fechas_consolidado.items():
            print(f"       {fecha}: {cantidad} registros")
    
    # 6. BUSCAR SI LOS REGISTROS PERDIDOS EXISTEN EN EL CONSOLIDADO CON OTRAS FECHAS
    print(f"\n🔍 ¿LOS REGISTROS PERDIDOS EXISTEN EN CONSOLIDADO CON OTRAS FECHAS?")
    
    if len(perdidos) > 0:
        # Verificar si algún WALLET_NUMBER perdido aparece en consolidado
        # (esto no debería pasar si consolidado tiene valores únicos)
        perdidos_en_consolidado = consolidado_set.intersection(perdidos)
        
        if len(perdidos_en_consolidado) > 0:
            print(f"   ⚠️  CONFLICTO: {len(perdidos_en_consolidado)} registros aparecen como perdidos Y presentes")
        else:
            print(f"   ✅ No hay conflictos: los perdidos realmente no están en consolidado")

    # 7. RECOMENDACIONES CRÍTICAS
    print(f"\n🚨 DIAGNÓSTICO COMO DATA ENGINEER:")
    
    if len(perdidos) > 0 and total_origen == unicos_origen:
        print(f"   ❌ PROBLEMA CRÍTICO IDENTIFICADO:")
        print(f"       • El archivo origen tiene {unicos_origen} registros ÚNICOS")
        print(f"       • El consolidado tiene solo {len(consolidado_set)} registros")
        print(f"       • Se están perdiendo {len(perdidos)} registros únicos legítimos")
        print(f"       • ESTO NO DEBE SUCEDER - No son duplicados")
        
        print(f"\n   🔧 ACCIÓN REQUERIDA:")
        print(f"       1. INVESTIGAR por qué el sistema de consolidación está")
        print(f"          eliminando registros únicos")
        print(f"       2. REVISAR la lógica de merge incremental")
        print(f"       3. CORREGIR el algoritmo para preservar todos los registros únicos")
        print(f"       4. VALIDAR que la clave primaria esté correctamente configurada")

except Exception as e:
    print(f"❌ Error en investigación: {str(e)}")
    import traceback
    traceback.print_exc()

print(f"\n⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("🚨 COMO DATA ENGINEER: La pérdida de registros únicos es INACEPTABLE")
