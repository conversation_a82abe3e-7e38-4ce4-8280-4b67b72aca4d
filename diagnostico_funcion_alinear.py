#!/usr/bin/env python3
"""
Script para diagnosticar el problema específico en la función alinear_tipos_datos_puro()
que introduce NaN durante la consolidación.
"""

import pandas as pd
import numpy as np
import boto3
import logging
from typing import Tuple, List, Dict, Set

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def conectar_s3():
    """Conecta a S3 con configuración por defecto"""
    try:
        s3 = boto3.client('s3')
        return s3
    except Exception as e:
        logging.error(f"Error conectando a S3: {e}")
        return None

def cargar_archivo_s3(bucket: str, key: str) -> pd.DataFrame:
    """Carga un archivo parquet desde S3"""
    try:
        s3 = conectar_s3()
        if s3 is None:
            return pd.DataFrame()
        
        obj = s3.get_object(Bucket=bucket, Key=key)
        df = pd.read_parquet(obj['Body'])
        logging.info(f"✅ Cargado: {key} - {len(df)} registros, {len(df.columns)} columnas")
        return df
    except Exception as e:
        logging.error(f"❌ Error cargando {key}: {e}")
        return pd.DataFrame()

def simular_alinear_tipos_datos_problema(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Simulación de la función problemática alinear_tipos_datos_puro()
    para identificar exactamente dónde se introducen los NaN
    """
    logging.info("🔍 === SIMULANDO FUNCIÓN ALINEAR_TIPOS_DATOS_PURO ===")
    
    if df_existente.empty or df_nuevos.empty:
        return df_existente, df_nuevos
    
    # Contar NaN ANTES del procesamiento
    nan_existente_antes = df_existente.isna().sum().sum()
    nan_nuevos_antes = df_nuevos.isna().sum().sum()
    logging.info(f"📊 NaN ANTES - Existente: {nan_existente_antes}, Nuevos: {nan_nuevos_antes}")
    
    # PASO 1: Obtener columnas comunes
    columnas_comunes = set(df_existente.columns) & set(df_nuevos.columns)
    logging.info(f"🔗 Columnas comunes: {len(columnas_comunes)}")
    
    df_existente_copia = df_existente.copy()
    df_nuevos_copia = df_nuevos.copy()
    
    # PASO 2: Alinear tipos de columnas comunes
    for columna in columnas_comunes:
        tipo_existente = df_existente_copia[columna].dtype
        tipo_nuevo = df_nuevos_copia[columna].dtype
        
        if tipo_existente != tipo_nuevo:
            logging.info(f"🔄 Alineando {columna}: {tipo_existente} -> {tipo_nuevo} -> string")
            try:
                df_existente_copia[columna] = df_existente_copia[columna].astype(str)
                df_nuevos_copia[columna] = df_nuevos_copia[columna].astype(str)
            except Exception as e:
                logging.warning(f"⚠️ Error alineando {columna}: {e}")
    
    # Contar NaN DESPUÉS de alineación de tipos
    nan_existente_tipos = df_existente_copia.isna().sum().sum()
    nan_nuevos_tipos = df_nuevos_copia.isna().sum().sum()
    logging.info(f"📊 NaN DESPUÉS de alinear tipos - Existente: {nan_existente_tipos}, Nuevos: {nan_nuevos_tipos}")
    
    # PASO 3: Identificar columnas faltantes (AQUÍ ESTÁ EL PROBLEMA)
    columnas_solo_existente = set(df_existente.columns) - set(df_nuevos.columns)
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    
    logging.info(f"📝 Columnas solo en existente: {columnas_solo_existente}")
    logging.info(f"📝 Columnas solo en nuevos: {columnas_solo_nuevos}")
    
    # PASO 4: Agregar columnas faltantes con valores por defecto (PROBLEMA AQUÍ)
    for columna in columnas_solo_nuevos:
        logging.info(f"➕ Agregando columna '{columna}' al existente con valor por defecto")
        df_existente_copia[columna] = ''  # ESTE ES EL PROBLEMA
        
    for columna in columnas_solo_existente:
        logging.info(f"➕ Agregando columna '{columna}' al nuevo con valor por defecto")
        df_nuevos_copia[columna] = ''  # ESTE ES EL PROBLEMA
    
    # Contar NaN FINAL
    nan_existente_final = df_existente_copia.isna().sum().sum()
    nan_nuevos_final = df_nuevos_copia.isna().sum().sum()
    logging.info(f"📊 NaN FINAL - Existente: {nan_existente_final}, Nuevos: {nan_nuevos_final}")
    
    # ANÁLISIS DETALLADO DE CAMBIOS
    cambio_existente = nan_existente_final - nan_existente_antes
    cambio_nuevos = nan_nuevos_final - nan_nuevos_antes
    logging.info(f"🔺 CAMBIO NaN - Existente: +{cambio_existente}, Nuevos: +{cambio_nuevos}")
    
    return df_existente_copia, df_nuevos_copia

def simular_alinear_tipos_datos_corregido(df_existente: pd.DataFrame, df_nuevos: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Versión CORREGIDA de la función alinear_tipos_datos_puro()
    que NO introduce NaN innecesarios
    """
    logging.info("🛠️ === SIMULANDO FUNCIÓN CORREGIDA ===")
    
    if df_existente.empty or df_nuevos.empty:
        return df_existente, df_nuevos
    
    # Contar NaN ANTES del procesamiento
    nan_existente_antes = df_existente.isna().sum().sum()
    nan_nuevos_antes = df_nuevos.isna().sum().sum()
    logging.info(f"📊 NaN ANTES - Existente: {nan_existente_antes}, Nuevos: {nan_nuevos_antes}")
    
    columnas_comunes = set(df_existente.columns) & set(df_nuevos.columns)
    df_existente_copia = df_existente.copy()
    df_nuevos_copia = df_nuevos.copy()
    
    # Alinear tipos de columnas comunes
    for columna in columnas_comunes:
        tipo_existente = df_existente_copia[columna].dtype
        tipo_nuevo = df_nuevos_copia[columna].dtype
        
        if tipo_existente != tipo_nuevo:
            logging.info(f"🔄 Alineando {columna}: {tipo_existente} -> {tipo_nuevo} -> string")
            try:
                df_existente_copia[columna] = df_existente_copia[columna].astype(str)
                df_nuevos_copia[columna] = df_nuevos_copia[columna].astype(str)
            except Exception as e:
                logging.warning(f"⚠️ Error alineando {columna}: {e}")
    
    # CORRECCIÓN: Agregar columnas faltantes con valores APROPIADOS según el tipo de columna
    columnas_solo_existente = set(df_existente.columns) - set(df_nuevos.columns)
    columnas_solo_nuevos = set(df_nuevos.columns) - set(df_existente.columns)
    
    for columna in columnas_solo_nuevos:
        logging.info(f"➕ Agregando columna '{columna}' al existente")
        # CORRECCIÓN: Usar None en lugar de '' para preservar tipos de datos
        df_existente_copia[columna] = None
        
    for columna in columnas_solo_existente:
        logging.info(f"➕ Agregando columna '{columna}' al nuevo")
        # CORRECCIÓN: Usar None en lugar de '' para preservar tipos de datos
        df_nuevos_copia[columna] = None
    
    # Contar NaN FINAL
    nan_existente_final = df_existente_copia.isna().sum().sum()
    nan_nuevos_final = df_nuevos_copia.isna().sum().sum()
    logging.info(f"📊 NaN FINAL - Existente: {nan_existente_final}, Nuevos: {nan_nuevos_final}")
    
    return df_existente_copia, df_nuevos_copia

def main():
    """Función principal para diagnosticar el problema"""
    logging.info("🚀 INICIANDO DIAGNÓSTICO DE FUNCIÓN ALINEAR_TIPOS_DATOS_PURO")
    
    # Cargar archivos de Bronze Zone (originales)
    bucket_bronze = 'prd-datalake-bronze-zone-637423440311'
    archivo1 = 'PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040917_chunk_0.parquet'
    archivo2 = 'PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2025/06/02/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-040552_chunk_0.parquet'
    
    logging.info("📥 Cargando archivos de Bronze Zone...")
    df1 = cargar_archivo_s3(bucket_bronze, archivo1)
    df2 = cargar_archivo_s3(bucket_bronze, archivo2)
    
    if df1.empty or df2.empty:
        logging.error("❌ No se pudieron cargar los archivos necesarios")
        return
    
    # SIMULACIÓN DEL PROBLEMA
    logging.info("\n" + "="*80)
    logging.info("🔴 SIMULANDO FUNCIÓN PROBLEMÁTICA")
    df1_problema, df2_problema = simular_alinear_tipos_datos_problema(df1, df2)
    
    # SIMULACIÓN DE LA CORRECCIÓN
    logging.info("\n" + "="*80)
    logging.info("🟢 SIMULANDO FUNCIÓN CORREGIDA")
    df1_corregido, df2_corregido = simular_alinear_tipos_datos_corregido(df1, df2)
    
    # COMPARACIÓN FINAL
    logging.info("\n" + "="*80)
    logging.info("📋 RESUMEN COMPARATIVO")
    logging.info(f"Original DF1 NaN: {df1.isna().sum().sum()}")
    logging.info(f"Original DF2 NaN: {df2.isna().sum().sum()}")
    logging.info(f"Problema DF1 NaN: {df1_problema.isna().sum().sum()}")
    logging.info(f"Problema DF2 NaN: {df2_problema.isna().sum().sum()}")
    logging.info(f"Corregido DF1 NaN: {df1_corregido.isna().sum().sum()}")
    logging.info(f"Corregido DF2 NaN: {df2_corregido.isna().sum().sum()}")
    
    # Análisis por columnas
    logging.info("\n📊 ANÁLISIS POR COLUMNAS:")
    for col in df1.columns:
        orig1_nan = df1[col].isna().sum()
        prob1_nan = df1_problema[col].isna().sum()
        corr1_nan = df1_corregido[col].isna().sum()
        
        if prob1_nan != orig1_nan:
            logging.info(f"   {col}: Original={orig1_nan}, Problema={prob1_nan}, Corregido={corr1_nan}")

if __name__ == "__main__":
    main()
