#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para verificar la estructura de la tabla MTX_WALLET_ORA consolidada
"""

import pandas as pd
import boto3
import logging
from botocore.exceptions import ClientError

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def verificar_mtx_wallet_consolidado():
    """
    Verifica la estructura de la tabla MTX_WALLET_ORA en su ubicación real
    """
    # Ubicación exacta del archivo consolidado
    bucket = "prd-datalake-silver-zone-637423440311"
    key = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado.parquet"
    s3_path = f"s3://{bucket}/{key}"
    
    logging.info(f"🔍 Verificando tabla MTX_WALLET_ORA en: {s3_path}")
    
    try:
        # Inicializar cliente S3
        s3_client = boto3.client('s3')
        
        # Verificar si el archivo existe
        try:
            response = s3_client.head_object(Bucket=bucket, Key=key)
            file_size = response['ContentLength']
            last_modified = response['LastModified']
            
            logging.info(f"✅ Archivo encontrado:")
            logging.info(f"   📁 Tamaño: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            logging.info(f"   📅 Última modificación: {last_modified}")
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                logging.error(f"❌ Archivo no encontrado: {s3_path}")
                return
            else:
                raise
        
        # Leer muestra del archivo Parquet
        logging.info("📋 Leyendo estructura del archivo...")
        
        try:
            # Leer solo las primeras filas para obtener la estructura
            df_sample = pd.read_parquet(s3_path, engine='pyarrow')
            
            logging.info(f"✅ Archivo leído exitosamente:")
            logging.info(f"   📊 Filas: {len(df_sample):,}")
            logging.info(f"   📋 Columnas: {len(df_sample.columns)}")
            logging.info("")
            
            # Mostrar información detallada de las columnas
            logging.info("📋 ESTRUCTURA DE COLUMNAS:")
            logging.info("=" * 60)
            
            for i, col in enumerate(df_sample.columns, 1):
                dtype = str(df_sample[col].dtype)
                non_null = df_sample[col].count()
                null_count = len(df_sample) - non_null
                
                # Mostrar valores únicos si hay pocos
                unique_count = df_sample[col].nunique()
                
                logging.info(f"{i:2d}. {col}")
                logging.info(f"    Tipo: {dtype}")
                logging.info(f"    No nulos: {non_null:,} | Nulos: {null_count:,}")
                logging.info(f"    Valores únicos: {unique_count:,}")
                
                # Si es una columna que podría ser clave primaria, mostrar muestra
                if any(keyword in col.upper() for keyword in ['ID', 'NUMBER', 'CODE', 'KEY']):
                    sample_values = df_sample[col].dropna().head(3).tolist()
                    logging.info(f"    Muestra: {sample_values}")
                
                logging.info("")
            
            # Buscar columnas que podrían ser clave primaria
            logging.info("🔍 ANÁLISIS DE POSIBLES CLAVES PRIMARIAS:")
            logging.info("=" * 60)
            
            posibles_claves = []
            
            for col in df_sample.columns:
                col_upper = col.upper()
                
                # Criterios para clave primaria
                es_candidata = False
                razon = []
                
                # 1. Nombre sugiere clave primaria
                if any(keyword in col_upper for keyword in ['ID', 'NUMBER', 'CODE', 'KEY']):
                    es_candidata = True
                    razon.append("nombre sugiere clave")
                
                # 2. Todos los valores son únicos
                if df_sample[col].nunique() == len(df_sample) and df_sample[col].count() == len(df_sample):
                    es_candidata = True
                    razon.append("valores únicos")
                
                # 3. No tiene nulos
                if df_sample[col].count() == len(df_sample):
                    razon.append("sin nulos")
                
                if es_candidata:
                    posibles_claves.append({
                        'columna': col,
                        'razones': razon,
                        'unicos': df_sample[col].nunique(),
                        'total': len(df_sample),
                        'nulos': len(df_sample) - df_sample[col].count()
                    })
            
            if posibles_claves:
                logging.info("🎯 Columnas candidatas a clave primaria:")
                for i, clave in enumerate(posibles_claves, 1):
                    logging.info(f"{i}. {clave['columna']}")
                    logging.info(f"   Razones: {', '.join(clave['razones'])}")
                    logging.info(f"   Únicos: {clave['unicos']:,}/{clave['total']:,}")
                    logging.info(f"   Nulos: {clave['nulos']:,}")
                    logging.info("")
            else:
                logging.warning("⚠️ No se encontraron columnas candidatas obvias para clave primaria")
            
            # Verificar si existe WALLET_NUMBER (la configurada actualmente)
            logging.info("🔍 VERIFICACIÓN DE CONFIGURACIÓN ACTUAL:")
            logging.info("=" * 60)
            
            if 'WALLET_NUMBER' in df_sample.columns:
                wallet_col = df_sample['WALLET_NUMBER']
                logging.info("✅ WALLET_NUMBER encontrada en los datos:")
                logging.info(f"   Tipo: {wallet_col.dtype}")
                logging.info(f"   Únicos: {wallet_col.nunique():,}/{len(df_sample):,}")
                logging.info(f"   Nulos: {len(df_sample) - wallet_col.count():,}")
                logging.info(f"   Muestra: {wallet_col.dropna().head(5).tolist()}")
                
                if wallet_col.nunique() == len(df_sample) and wallet_col.count() == len(df_sample):
                    logging.info("✅ WALLET_NUMBER es una clave primaria válida")
                else:
                    logging.warning("⚠️ WALLET_NUMBER NO cumple criterios de clave primaria")
            else:
                logging.error("❌ WALLET_NUMBER NO encontrada en los datos")
                logging.info("💡 Columnas disponibles que contienen 'WALLET':")
                wallet_cols = [col for col in df_sample.columns if 'WALLET' in col.upper()]
                if wallet_cols:
                    for col in wallet_cols:
                        logging.info(f"   - {col}")
                else:
                    logging.info("   (ninguna)")
        
        except Exception as e:
            logging.error(f"❌ Error leyendo archivo Parquet: {str(e)}")
            return
    
    except Exception as e:
        logging.error(f"❌ Error general: {str(e)}")
        return

if __name__ == "__main__":
    verificar_mtx_wallet_consolidado()
