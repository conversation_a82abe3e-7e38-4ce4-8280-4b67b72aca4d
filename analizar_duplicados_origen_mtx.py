#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Análisis de duplicados en archivo origen MTX_WALLET_ORA
=======================================================

Identifica por qué se están perdiendo 39 registros durante la consolidación
"""

import sys
import pandas as pd
import boto3
import logging
from io import BytesIO
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Configurar ruta para imports
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

# Configuración S3
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
ARCHIVO_ORIGEN = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-080250_chunk_0.parquet"
CLAVE_PRIMARIA = "WALLET_NUMBER"

def leer_parquet_desde_s3(bucket: str, key: str) -> pd.DataFrame:
    """Lee un archivo parquet desde S3."""
    try:
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        parquet_content = response['Body'].read()
        df = pd.read_parquet(BytesIO(parquet_content))
        return df
    except Exception as e:
        print(f"   ❌ Error leyendo archivo: {str(e)}")
        return pd.DataFrame()

def analizar_duplicados_detallado():
    """Analiza en detalle los duplicados en el archivo origen."""
    
    print("🔍 ANÁLISIS DETALLADO DE DUPLICADOS - MTX_WALLET_ORA")
    print("=" * 70)
    print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Leer archivo origen
    print(f"\n📥 PASO 1: Leer archivo origen...")
    print(f"   📍 Bucket: {S3_BRONZE_BUCKET}")
    print(f"   📍 Archivo: {ARCHIVO_ORIGEN}")
    
    df_origen = leer_parquet_desde_s3(S3_BRONZE_BUCKET, ARCHIVO_ORIGEN)
    
    if df_origen.empty:
        print(f"❌ No se pudo leer el archivo origen")
        return
    
    print(f"   ✅ Archivo leído exitosamente")
    print(f"   📊 Total registros: {len(df_origen)}")
    print(f"   📊 Total columnas: {len(df_origen.columns)}")
    
    # 2. Análisis de clave primaria
    print(f"\n📋 PASO 2: Análisis de clave primaria '{CLAVE_PRIMARIA}'...")
    
    total_registros = len(df_origen)
    valores_unicos = df_origen[CLAVE_PRIMARIA].nunique()
    duplicados = total_registros - valores_unicos
    
    print(f"   📊 Total registros: {total_registros}")
    print(f"   📊 Valores únicos de {CLAVE_PRIMARIA}: {valores_unicos}")
    print(f"   📊 Registros duplicados: {duplicados}")
    print(f"   📊 Porcentaje de duplicación: {(duplicados/total_registros)*100:.1f}%")
    
    if duplicados == 0:
        print(f"   ✅ NO HAY DUPLICADOS - El archivo origen es completamente único")
        return
    
    # 3. Identificar valores duplicados específicos
    print(f"\n🔍 PASO 3: Identificar valores duplicados específicos...")
    
    # Contar ocurrencias de cada WALLET_NUMBER
    conteos = df_origen[CLAVE_PRIMARIA].value_counts()
    duplicados_valores = conteos[conteos > 1]
    
    print(f"   📊 Total {CLAVE_PRIMARIA} con duplicados: {len(duplicados_valores)}")
    print(f"   📊 Máximo duplicados por {CLAVE_PRIMARIA}: {duplicados_valores.max()}")
    
    print(f"\n   📋 TOP 10 VALORES MÁS DUPLICADOS:")
    for wallet_num, cantidad in duplicados_valores.head(10).items():
        print(f"       {wallet_num}: {cantidad} registros")
    
    # 4. Análisis de campos de fecha para deduplicación
    print(f"\n📅 PASO 4: Análisis de campos de fecha...")
    
    campos_fecha = [col for col in df_origen.columns if any(palabra in col.upper() for palabra in ['DATE', 'MODIFIED', 'CREATED', 'PARTITION'])]
    print(f"   📋 Campos de fecha encontrados: {campos_fecha}")
    
    if 'DATA_LAKE_PARTITION_DATE' in df_origen.columns:
        print(f"\n   🎯 CAMPO CRÍTICO: DATA_LAKE_PARTITION_DATE")
        
        partition_dates = df_origen['DATA_LAKE_PARTITION_DATE'].unique()
        print(f"       📊 Valores únicos: {len(partition_dates)}")
        print(f"       📊 Rango de fechas: {partition_dates}")
        
        # Analizar algunos ejemplos de duplicados
        if len(duplicados_valores) > 0:
            print(f"\n   🔬 EJEMPLO DE REGISTROS DUPLICADOS:")
            primer_duplicado = duplicados_valores.index[0]
            registros_ejemplo = df_origen[df_origen[CLAVE_PRIMARIA] == primer_duplicado]
            
            print(f"       🎯 WALLET_NUMBER: {primer_duplicado}")
            print(f"       📊 Cantidad de registros: {len(registros_ejemplo)}")
            
            for idx, (_, row) in enumerate(registros_ejemplo.iterrows()):
                print(f"         Registro {idx+1}:")
                print(f"           DATA_LAKE_PARTITION_DATE: {row.get('DATA_LAKE_PARTITION_DATE', 'N/A')}")
                print(f"           MODIFIED_ON: {row.get('MODIFIED_ON', 'N/A')}")
                print(f"           CREATED_ON: {row.get('CREATED_ON', 'N/A')}")
    
    # 5. Simular proceso de deduplicación
    print(f"\n🧪 PASO 5: Simular proceso de deduplicación...")
    
    if 'DATA_LAKE_PARTITION_DATE' in df_origen.columns:
        # Ordenar por fecha más reciente y eliminar duplicados
        df_dedup = df_origen.sort_values('DATA_LAKE_PARTITION_DATE', ascending=False)
        df_dedup = df_dedup.drop_duplicates(subset=[CLAVE_PRIMARIA], keep='first')
        
        registros_finales = len(df_dedup)
        eliminados = total_registros - registros_finales
        
        print(f"   📊 Registros después de deduplicación: {registros_finales}")
        print(f"   📊 Registros eliminados: {eliminados}")
        print(f"   📊 Coincide con pérdida reportada: {'✅ SÍ' if eliminados == 39 else '❌ NO'}")
        
        # Identificar qué WALLET_NUMBER se perdieron
        wallet_originales = set(df_origen[CLAVE_PRIMARIA])
        wallet_dedup = set(df_dedup[CLAVE_PRIMARIA])
        wallet_perdidos = wallet_originales - wallet_dedup
        
        if len(wallet_perdidos) > 0:
            print(f"\n   ❌ WALLET_NUMBER que se perderían:")
            for i, wallet in enumerate(sorted(wallet_perdidos)[:10]):  # Mostrar solo 10 ejemplos
                print(f"       {i+1}. {wallet}")
            if len(wallet_perdidos) > 10:
                print(f"       ... y {len(wallet_perdidos)-10} más")
        else:
            print(f"   ✅ NO se perdería ningún WALLET_NUMBER único")
    
    # 6. Análisis de diferencias entre registros duplicados
    print(f"\n🔬 PASO 6: Análisis de diferencias entre registros duplicados...")
    
    if len(duplicados_valores) > 0:
        primer_duplicado = duplicados_valores.index[0]
        registros_dup = df_origen[df_origen[CLAVE_PRIMARIA] == primer_duplicado]
        
        print(f"   🎯 Analizando diferencias para {CLAVE_PRIMARIA}: {primer_duplicado}")
        
        # Identificar columnas que varían entre duplicados
        columnas_diferentes = []
        for col in df_origen.columns:
            if registros_dup[col].nunique() > 1:
                columnas_diferentes.append(col)
        
        print(f"   📊 Columnas que varían entre duplicados: {len(columnas_diferentes)}")
        print(f"   📋 Columnas diferentes: {columnas_diferentes[:10]}")  # Mostrar solo 10
        
        if len(columnas_diferentes) > 0:
            print(f"\n   🔍 DETALLES DE DIFERENCIAS:")
            for col in columnas_diferentes[:5]:  # Mostrar solo 5 ejemplos
                valores_unicos = registros_dup[col].unique()
                print(f"       {col}: {valores_unicos}")
    
    print(f"\n✅ ANÁLISIS COMPLETADO")
    print(f"⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    analizar_duplicados_detallado()
