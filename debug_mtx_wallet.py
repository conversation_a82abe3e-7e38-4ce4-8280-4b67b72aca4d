#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para debuggear la estructura de MTX_WALLET_ORA
"""

import boto3
import pandas as pd
import logging
from io import BytesIO
from botocore.exceptions import ClientError
import os

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def buscar_archivos_mtx_wallet():
    """
    Busca archivos de MTX_WALLET_ORA en S3 para verificar su estructura
    """
    
    # Configuración S3
    s3_client = boto3.client('s3')
    bucket = "prd-datalake-silver-zone-637423440311"
    prefix = "PDP_PROD10_MAINDB/MTX_WALLET_ORA/"
    
    logging.info(f"🔍 Buscando archivos MTX_WALLET_ORA en s3://{bucket}/{prefix}")
    
    try:
        # Listar archivos en S3
        response = s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix=prefix,
            MaxKeys=10  # Solo los primeros 10 archivos
        )
        
        if 'Contents' not in response:
            logging.warning(f"❌ No se encontraron archivos en s3://{bucket}/{prefix}")
            return
        
        archivos = response['Contents']
        logging.info(f"✅ Encontrados {len(archivos)} archivos")
        
        # Mostrar algunos archivos encontrados
        for i, archivo in enumerate(archivos[:5]):
            key = archivo['Key']
            size = archivo['Size']
            logging.info(f"  {i+1}. {key} ({size} bytes)")
        
        # Intentar leer el primer archivo Parquet válido
        for archivo in archivos:
            key = archivo['Key']
            if key.endswith('.parquet') and archivo['Size'] > 0:
                logging.info(f"📖 Intentando leer: {key}")
                try:
                    # Leer archivo desde S3
                    obj = s3_client.get_object(Bucket=bucket, Key=key)
                    df = pd.read_parquet(BytesIO(obj['Body'].read()))
                    
                    logging.info(f"✅ Archivo leído exitosamente:")
                    logging.info(f"   📊 Filas: {len(df)}")
                    logging.info(f"   📋 Columnas: {len(df.columns)}")
                    logging.info(f"   🏷️  Nombres de columnas:")
                    
                    for i, col in enumerate(df.columns):
                        logging.info(f"      {i+1:2d}. {col}")
                    
                    # Verificar si WALLET_NUMBER existe
                    if 'WALLET_NUMBER' in df.columns:
                        logging.info(f"✅ WALLET_NUMBER encontrado!")
                        # Mostrar algunos valores de ejemplo
                        sample_values = df['WALLET_NUMBER'].head(5).tolist()
                        logging.info(f"   Valores de ejemplo: {sample_values}")
                        
                        # Verificar unicidad
                        total_rows = len(df)
                        unique_values = df['WALLET_NUMBER'].nunique()
                        logging.info(f"   Unicidad: {unique_values}/{total_rows} ({unique_values/total_rows:.1%})")
                    else:
                        logging.error(f"❌ WALLET_NUMBER NO encontrado!")
                        logging.info("💡 Columnas disponibles que podrían ser clave primaria:")
                        for col in df.columns:
                            if any(keyword in col.upper() for keyword in ['ID', 'KEY', 'NUMBER', 'CODE']):
                                unique_vals = df[col].nunique()
                                total_vals = len(df)
                                pct = unique_vals/total_vals if total_vals > 0 else 0
                                logging.info(f"      {col}: {unique_vals}/{total_vals} únicos ({pct:.1%})")
                    
                    # Mostrar una muestra de datos
                    logging.info(f"📋 Muestra de datos (primeras 3 filas):")
                    for idx, row in df.head(3).iterrows():
                        logging.info(f"   Fila {idx}:")
                        for col in df.columns[:5]:  # Solo primeras 5 columnas
                            logging.info(f"      {col}: {row[col]}")
                        if len(df.columns) > 5:
                            logging.info(f"      ... (+{len(df.columns)-5} columnas más)")
                    
                    return df.columns.tolist()
                    
                except Exception as e:
                    logging.error(f"❌ Error leyendo {key}: {str(e)}")
                    continue
        
        logging.warning("❌ No se pudo leer ningún archivo Parquet válido")
        
    except Exception as e:
        logging.error(f"❌ Error buscando archivos: {str(e)}")

def verificar_configuracion():
    """
    Verifica la configuración actual de MTX_WALLET_ORA
    """
    logging.info("🔧 Verificando configuración actual...")
    
    # Importar función de detección
    from tabla_config_simple import TABLA_KEYS_CONFIG
    
    if 'MTX_WALLET_ORA' in TABLA_KEYS_CONFIG:
        claves = TABLA_KEYS_CONFIG['MTX_WALLET_ORA']
        logging.info(f"✅ Configuración encontrada: {claves}")
    else:
        logging.warning("❌ MTX_WALLET_ORA no está configurado")
    
    logging.info(f"📋 Todas las tablas configuradas:")
    for tabla, claves in TABLA_KEYS_CONFIG.items():
        logging.info(f"   {tabla}: {claves}")

if __name__ == "__main__":
    logging.info("🚀 Iniciando debug de MTX_WALLET_ORA...")
    
    # Verificar configuración
    verificar_configuracion()
    
    # Buscar y analizar archivos
    columnas = buscar_archivos_mtx_wallet()
    
    logging.info("✅ Debug completado")
