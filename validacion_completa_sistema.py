#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import sys
import os

# Agregar el directorio actual al path para poder importar
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')

def test_normalizacion_tipos():
    """
    Prueba la función normalizar_tipos_datos con diferentes casos de prueba.
    """
    
    # Importar la función desde el módulo local
    from app_raw_consolidado import normalizar_tipos_datos
    
    print("=== VALIDACIÓN COMPLETA DEL SISTEMA DE NORMALIZACIÓN ===\n")
    
    # Test 1: Datos categóricos que NO deben convertirse a timestamp
    print("1. TEST: Datos categóricos protegidos")
    test_categoricos = {
        'payment_method': ['WALLET', 'WALLET', 'WALLET', 'WALLET'],
        'user_grade': ['OPT', 'OPT', 'OPT', 'OPT'],
        'user_type': ['OPERATOR', 'ADMIN', 'USER', 'OPERATOR'],
        'status': ['ACTIVE', 'INACTIVE', 'PENDING', 'ACTIVE']
    }
    df_cat = pd.DataFrame(test_categoricos)
    df_cat_norm = normalizar_tipos_datos(df_cat.copy())
    
    for col in df_cat.columns:
        original = df_cat[col].tolist()
        normalizado = df_cat_norm[col].tolist()
        if original == normalizado:
            print(f"   ✓ {col}: Valores categóricos preservados correctamente")
        else:
            print(f"   ✗ {col}: PROBLEMA - Valores cambiados:")
            for i, (orig, norm) in enumerate(zip(original, normalizado)):
                if orig != norm:
                    print(f"     Fila {i}: '{orig}' → '{norm}'")
    
    print()
    
    # Test 2: Timestamps reales que SÍ deben convertirse
    print("2. TEST: Timestamps reales")
    test_timestamps = {
        'timestamp_iso': ['2023-01-01T10:30:00', '2023-01-02T11:45:00', '2023-01-03T09:15:00', '2023-01-04T14:20:00'],
        'datetime_completo': ['2023-01-01 10:30:00', '2023-01-02 11:45:00', '2023-01-03 09:15:00', '2023-01-04 14:20:00'],
        'fecha_simple': ['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04']
    }
    df_ts = pd.DataFrame(test_timestamps)
    df_ts_norm = normalizar_tipos_datos(df_ts.copy())
    
    for col in df_ts.columns:
        original = df_ts[col].tolist()
        normalizado = df_ts_norm[col].tolist()
        if original != normalizado:
            print(f"   ✓ {col}: Timestamps convertidos (esperado)")
            print(f"     Ejemplo: '{original[0]}' → '{normalizado[0]}'")
        else:
            print(f"   ? {col}: No hubo conversión de timestamp")
    
    print()
    
    # Test 3: Caso mixto problemático (como WALLET_NUMBER)
    print("3. TEST: Caso mixto (IDs con formato alfanumérico)")
    test_mixto = {
        'wallet_number': ['102IND03B', '103USA04C', '104MEX05D', '105EUR06E'],
        'transaction_id': ['TXN123ABC', 'TXN456DEF', 'TXN789GHI', 'TXN012JKL']
    }
    df_mix = pd.DataFrame(test_mixto)
    df_mix_norm = normalizar_tipos_datos(df_mix.copy())
    
    for col in df_mix.columns:
        original = df_mix[col].tolist()
        normalizado = df_mix_norm[col].tolist()
        if original == normalizado:
            print(f"   ✓ {col}: IDs alfanuméricos preservados correctamente")
        else:
            print(f"   ✗ {col}: PROBLEMA - IDs alterados:")
            for i, (orig, norm) in enumerate(zip(original, normalizado)):
                if orig != norm:
                    print(f"     Fila {i}: '{orig}' → '{norm}'")
    
    print()
    
    # Test 4: Valores 'None' - manejo selectivo
    print("4. TEST: Manejo selectivo de valores 'None'")
    
    # Caso A: 'None' como datos válidos (minoría)
    test_none_valido = {
        'columna_con_none_valido': ['Value1', 'Value2', 'None', 'Value3', 'Value4']
    }
    df_none_val = pd.DataFrame(test_none_valido)
    df_none_val_norm = normalizar_tipos_datos(df_none_val.copy())
    
    if 'None' in df_none_val_norm['columna_con_none_valido'].values:
        print("   ✓ 'None' como datos válidos: Preservado correctamente (minoría)")
    else:
        print("   ✗ 'None' como datos válidos: Incorrectamente eliminado")
    
    # Caso B: 'None' como NULL real (mayoría)
    test_none_null = {
        'columna_con_none_null': ['None', 'None', 'None', 'Value1', 'None']
    }
    df_none_null = pd.DataFrame(test_none_null)
    df_none_null_norm = normalizar_tipos_datos(df_none_null.copy())
    
    none_count_after = (df_none_null_norm['columna_con_none_null'] == 'None').sum()
    if none_count_after == 0:
        print("   ✓ 'None' como NULL real: Correctamente reemplazado por cadena vacía (mayoría)")
    else:
        print(f"   ? 'None' como NULL real: {none_count_after} valores 'None' permanecen")
    
    print()
    
    # Test 5: Casos edge problemáticos identificados anteriormente
    print("5. TEST: Casos problemáticos específicos identificados")
    test_problematicos = {
        'payment_method_type_id': ['WALLET', 'CARD', 'WALLET', 'BANK'],
        'mixed_with_wallet': ['WALLET', 'OTHER', 'WALLET', 'SOMETHING'],
        'user_statuses': ['OPERATOR', 'USER', 'ADMIN', 'OPERATOR']
    }
    df_prob = pd.DataFrame(test_problematicos)
    df_prob_norm = normalizar_tipos_datos(df_prob.copy())
    
    problemas_encontrados = False
    for col in df_prob.columns:
        original = df_prob[col].tolist()
        normalizado = df_prob_norm[col].tolist()
        if original != normalizado:
            problemas_encontrados = True
            print(f"   ✗ {col}: PROBLEMA detectado:")
            for i, (orig, norm) in enumerate(zip(original, normalizado)):
                if orig != norm:
                    print(f"     Fila {i}: '{orig}' → '{norm}'")
        else:
            print(f"   ✓ {col}: Valores preservados correctamente")
    
    if not problemas_encontrados:
        print("   ✓ Todos los casos problemáticos específicos resueltos")
    
    print()
    print("=== RESUMEN DE VALIDACIÓN ===")
    print("La validación completa ha terminado.")
    print("Revise los resultados arriba para identificar cualquier problema restante.")

if __name__ == "__main__":
    test_normalizacion_tipos()
