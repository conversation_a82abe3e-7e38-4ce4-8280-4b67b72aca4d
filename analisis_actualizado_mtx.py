#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Análisis actualizado: Archivo origen actual vs consolidado
"""

import pandas as pd
import boto3
from io import BytesIO
from datetime import datetime

print("🔍 ANÁLISIS ACTUALIZADO - <PERSON>IGEN vs CONSOLIDADO")
print("=" * 60)
print(f"⏰ Inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Configuración
S3_BRONZE_BUCKET = "prd-datalake-bronze-zone-637423440311"
S3_SILVER_BUCKET = "prd-datalake-silver-zone-637423440311"
ARCHIVO_ORIGEN = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/2001/01/01/PDP_PROD10_MAINDBBUS-MTX_WALLET_ORA_20250602-080250_chunk_0.parquet"
ARCHIVO_CONSOLIDADO = "PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"
CLAVE_PRIMARIA = "WALLET_NUMBER"

def leer_parquet_s3(bucket, key, nombre):
    """Lee un archivo parquet desde S3."""
    try:
        print(f"\n📥 Leyendo {nombre}...")
        print(f"   📍 Bucket: {bucket}")
        print(f"   📍 Archivo: {key}")
        
        s3_client = boto3.client('s3')
        response = s3_client.get_object(Bucket=bucket, Key=key)
        parquet_content = response['Body'].read()
        df = pd.read_parquet(BytesIO(parquet_content))
        
        print(f"   ✅ Leído exitosamente: {len(df)} registros, {len(df.columns)} columnas")
        return df
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return pd.DataFrame()

# 1. Leer archivos
df_origen = leer_parquet_s3(S3_BRONZE_BUCKET, ARCHIVO_ORIGEN, "ARCHIVO ORIGEN")
df_consolidado = leer_parquet_s3(S3_SILVER_BUCKET, ARCHIVO_CONSOLIDADO, "ARCHIVO CONSOLIDADO")

if df_origen.empty or df_consolidado.empty:
    print("\n❌ No se pudieron leer los archivos")
    exit(1)

# 2. Análisis comparativo
print(f"\n📊 COMPARACIÓN GENERAL:")
print(f"   📍 Archivo origen:")
print(f"       Total registros: {len(df_origen)}")
print(f"       Valores únicos {CLAVE_PRIMARIA}: {df_origen[CLAVE_PRIMARIA].nunique()}")
print(f"   📍 Archivo consolidado:")
print(f"       Total registros: {len(df_consolidado)}")
print(f"       Valores únicos {CLAVE_PRIMARIA}: {df_consolidado[CLAVE_PRIMARIA].nunique()}")

# 3. Verificar pérdida de datos
origen_unicos = set(df_origen[CLAVE_PRIMARIA].unique())
consolidado_unicos = set(df_consolidado[CLAVE_PRIMARIA].unique())

perdidos = origen_unicos - consolidado_unicos
ganados = consolidado_unicos - origen_unicos

print(f"\n🔍 ANÁLISIS DE DIFERENCIAS:")
print(f"   📊 WALLET_NUMBER en origen: {len(origen_unicos)}")
print(f"   📊 WALLET_NUMBER en consolidado: {len(consolidado_unicos)}")
print(f"   📊 WALLET_NUMBER perdidos: {len(perdidos)}")
print(f"   📊 WALLET_NUMBER nuevos en consolidado: {len(ganados)}")

if len(perdidos) > 0:
    print(f"\n❌ WALLET_NUMBER PERDIDOS:")
    for i, wallet in enumerate(sorted(perdidos)[:10]):
        print(f"       {i+1}. {wallet}")
    if len(perdidos) > 10:
        print(f"       ... y {len(perdidos)-10} más")

if len(ganados) > 0:
    print(f"\n➕ WALLET_NUMBER NUEVOS EN CONSOLIDADO:")
    for i, wallet in enumerate(sorted(ganados)[:10]):
        print(f"       {i+1}. {wallet}")
    if len(ganados) > 10:
        print(f"       ... y {len(ganados)-10} más")

# 4. Verificar duplicados en origen actual
print(f"\n🔍 VERIFICACIÓN DE DUPLICADOS EN ORIGEN ACTUAL:")
duplicados_origen = len(df_origen) - df_origen[CLAVE_PRIMARIA].nunique()
print(f"   📊 Registros duplicados en origen: {duplicados_origen}")

if duplicados_origen > 0:
    print(f"   ⚠️  HAY DUPLICADOS EN EL ARCHIVO ORIGEN ACTUAL")
    conteos = df_origen[CLAVE_PRIMARIA].value_counts()
    duplicados_valores = conteos[conteos > 1]
    
    print(f"   📊 Valores con duplicados: {len(duplicados_valores)}")
    print(f"   📋 TOP 5 WALLET_NUMBER duplicados:")
    for wallet_num, cantidad in duplicados_valores.head(5).items():
        print(f"       {wallet_num}: {cantidad} registros")
else:
    print(f"   ✅ NO HAY DUPLICADOS EN EL ARCHIVO ORIGEN ACTUAL")

# 5. Verificar duplicados en consolidado
print(f"\n🔍 VERIFICACIÓN DE DUPLICADOS EN CONSOLIDADO:")
duplicados_consolidado = len(df_consolidado) - df_consolidado[CLAVE_PRIMARIA].nunique()
print(f"   📊 Registros duplicados en consolidado: {duplicados_consolidado}")

if duplicados_consolidado > 0:
    print(f"   ⚠️  HAY DUPLICADOS EN EL ARCHIVO CONSOLIDADO")
else:
    print(f"   ✅ NO HAY DUPLICADOS EN EL ARCHIVO CONSOLIDADO")

# 6. Conclusiones
print(f"\n📋 CONCLUSIONES:")

if len(df_origen) == len(df_consolidado) and len(perdidos) == 0:
    print(f"   ✅ PERFECTO: No hay pérdida de datos")
    print(f"   ✅ El archivo origen y consolidado tienen la misma información")
elif len(perdidos) == 0 and len(ganados) > 0:
    print(f"   ✅ BUENO: No se perdieron datos del origen")
    print(f"   📈 El consolidado tiene {len(ganados)} registros adicionales (de otros archivos)")
elif len(perdidos) > 0:
    print(f"   ❌ PROBLEMA: Se perdieron {len(perdidos)} WALLET_NUMBER únicos")
    print(f"   🔍 ESTO REQUIERE INVESTIGACIÓN")
else:
    print(f"   🤔 SITUACIÓN MIXTA: Se perdieron {len(perdidos)} y se ganaron {len(ganados)}")

print(f"\n✅ ANÁLISIS COMPLETADO")
print(f"⏰ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
