#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para explorar qué archivos existen realmente en S3
"""

import boto3
import logging
from collections import defaultdict

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s: %(message)s')

# Configuración S3
S3_LANDING_BUCKET = "prd-datalake-bronze-zone-637423440311"

def explorar_s3_completo():
    """Explora todo el contenido de S3 para encontrar archivos parquet"""
    
    print("\n" + "="*80)
    print("🔍 EXPLORANDO S3: Buscando archivos Parquet reales")
    print("="*80)
    
    s3_client = boto3.client('s3')
    
    try:
        # Buscar todos los archivos parquet
        archivos_por_carpeta = defaultdict(list)
        total_archivos = 0
        
        print(f"📁 Bucket: {S3_LANDING_BUCKET}")
        print(f"🔍 Buscando archivos .parquet...")
        
        paginator = s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=S3_LANDING_BUCKET):
            if 'Contents' in page:
                for obj in page['Contents']:
                    key = obj['Key']
                    if key.endswith('.parquet'):
                        # Extraer carpeta principal
                        carpeta_principal = key.split('/')[0] if '/' in key else 'ROOT'
                        archivos_por_carpeta[carpeta_principal].append(key)
                        total_archivos += 1
        
        print(f"📊 Total archivos .parquet encontrados: {total_archivos}")
        print(f"📁 Carpetas principales: {len(archivos_por_carpeta)}")
        
        # Mostrar estructura por carpetas
        print(f"\n📂 ESTRUCTURA DE CARPETAS:")
        for carpeta, archivos in sorted(archivos_por_carpeta.items()):
            print(f"   📁 {carpeta}/ ({len(archivos)} archivos)")
            
            # Mostrar algunos archivos de ejemplo
            if len(archivos) <= 5:
                for archivo in archivos:
                    print(f"      📄 {archivo}")
            else:
                # Mostrar primeros 3 y últimos 2
                for archivo in archivos[:3]:
                    print(f"      📄 {archivo}")
                print(f"      ... ({len(archivos)-5} archivos más)")
                for archivo in archivos[-2:]:
                    print(f"      📄 {archivo}")
        
        # Buscar patrones específicos que podrían ser nuestras tablas
        print(f"\n🔍 BUSCANDO PATRONES ESPECÍFICOS:")
        
        patrones_busqueda = [
            'MTX', 'WALLET', 'USER', 'KYC', 'AUTH', 'CHANNEL', 'ISSUER', 'ORA',
            'PROFILE', 'DETAILS', 'HISTORY', 'CATEGORIES', 'GRADES', 'ACCOUNTS',
            'IDENTIFIER'
        ]
        
        archivos_relevantes = defaultdict(list)
        
        for carpeta, archivos in archivos_por_carpeta.items():
            for patron in patrones_busqueda:
                if patron.upper() in carpeta.upper():
                    archivos_relevantes[patron].extend(archivos)
        
        if archivos_relevantes:
            print(f"   📋 Archivos que contienen patrones conocidos:")
            for patron, archivos in archivos_relevantes.items():
                if archivos:
                    print(f"      🔑 {patron}: {len(archivos)} archivos")
                    for archivo in archivos[:3]:  # Mostrar primeros 3
                        print(f"         📄 {archivo}")
                    if len(archivos) > 3:
                        print(f"         ... y {len(archivos)-3} más")
        
        # Buscar archivos consolidados existentes
        print(f"\n🔍 BUSCANDO ARCHIVOS CONSOLIDADOS:")
        archivos_consolidados = []
        
        for carpeta, archivos in archivos_por_carpeta.items():
            for archivo in archivos:
                if 'consolidado' in archivo.lower():
                    archivos_consolidados.append(archivo)
        
        if archivos_consolidados:
            print(f"   📋 Archivos consolidados encontrados:")
            for archivo in archivos_consolidados:
                print(f"      📄 {archivo}")
        else:
            print(f"   ❌ No se encontraron archivos consolidados")
        
        # Sugerencias
        print(f"\n💡 SUGERENCIAS:")
        if not archivos_relevantes:
            print(f"   🔍 No se encontraron archivos con patrones conocidos")
            print(f"   💡 Revisar manualmente las carpetas principales:")
            for carpeta in sorted(archivos_por_carpeta.keys())[:10]:
                print(f"      📁 {carpeta}/")
        else:
            print(f"   ✅ Se encontraron {sum(len(a) for a in archivos_relevantes.values())} archivos relevantes")
            print(f"   💡 Verificar si estos archivos contienen las columnas configuradas")
        
    except Exception as e:
        print(f"❌ Error explorando S3: {str(e)}")
        print("💡 Verificar credenciales de AWS y permisos de acceso")
    
    print(f"\n" + "="*80)

if __name__ == "__main__":
    explorar_s3_completo()
