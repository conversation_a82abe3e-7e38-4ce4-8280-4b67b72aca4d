#!/usr/bin/env python3
"""
Validación exhaustiva de integridad estructural para alinear_tipos_datos_puro()
Verifica que origen y destino mantengan estructura idéntica "como dos gotas de agua"
"""

import pandas as pd
import numpy as np
import sys
import os
from typing import Dict, List, Tuple, Any

# Agregar el directorio actual al path para importar funciones
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar la función a validar
from app_raw_consolidado_puro import alinear_tipos_datos_puro

class ValidadorIntegridadEstructural:
    """Validador exhaustivo de integridad estructural entre DataFrames"""
    
    def __init__(self):
        self.errores = []
        self.warnings = []
        self.detalles = []
    
    def analizar_estructura_completa(self, df_original: pd.DataFrame, df_procesado: pd.DataFrame, 
                                   nombre_df: str) -> Dict[str, Any]:
        """Analiza la estructura completa de un DataFrame"""
        
        estructura = {
            'nombre': nombre_df,
            'filas': len(df_procesado),
            'columnas': list(df_procesado.columns),
            'tipos_datos': dict(df_procesado.dtypes),
            'nulos_por_columna': dict(df_procesado.isnull().sum()),
            'nulos_totales': df_procesado.isnull().sum().sum(),
            'memoria_uso': df_procesado.memory_usage(deep=True).sum(),
            'valores_unicos_por_columna': {col: df_procesado[col].nunique() for col in df_procesado.columns}
        }
        
        # Análisis específico de valores especiales por columna
        for col in df_procesado.columns:
            serie = df_procesado[col]
            
            # Detectar conversiones problemáticas de NaN
            if serie.dtype == 'object':
                string_nan_count = (serie == 'nan').sum()
                string_none_count = (serie == 'None').sum()
                
                if string_nan_count > 0:
                    self.errores.append(f"❌ {nombre_df}[{col}]: {string_nan_count} valores 'nan' como string")
                
                if string_none_count > 0:
                    self.warnings.append(f"⚠️ {nombre_df}[{col}]: {string_none_count} valores 'None' como string")
        
        return estructura
    
    def comparar_estructuras_origen_procesado(self, df_orig: pd.DataFrame, df_proc: pd.DataFrame, 
                                            nombre: str) -> bool:
        """Compara estructura entre DataFrame original y procesado"""
        
        print(f"\n🔍 VALIDANDO ESTRUCTURA: {nombre}")
        print("-" * 50)
        
        estructura_orig = self.analizar_estructura_completa(df_orig, df_orig, f"{nombre}_original")
        estructura_proc = self.analizar_estructura_completa(df_orig, df_proc, f"{nombre}_procesado")
        
        # 1. VALIDAR NÚMERO DE FILAS (debe ser idéntico)
        if estructura_orig['filas'] != estructura_proc['filas']:
            self.errores.append(f"❌ {nombre}: Filas cambiaron {estructura_orig['filas']} → {estructura_proc['filas']}")
        else:
            self.detalles.append(f"✓ {nombre}: Filas preservadas ({estructura_proc['filas']})")
        
        # 2. VALIDAR COLUMNAS (deben ser idénticas)
        cols_orig = set(estructura_orig['columnas'])
        cols_proc = set(estructura_proc['columnas'])
        
        if cols_orig != cols_proc:
            cols_perdidas = cols_orig - cols_proc
            cols_nuevas = cols_proc - cols_orig
            
            if cols_perdidas:
                self.errores.append(f"❌ {nombre}: Columnas perdidas: {list(cols_perdidas)}")
            if cols_nuevas:
                self.errores.append(f"❌ {nombre}: Columnas añadidas: {list(cols_nuevas)}")
        else:
            self.detalles.append(f"✓ {nombre}: Columnas preservadas ({len(cols_proc)})")
        
        # 3. VALIDAR INTEGRIDAD DE VALORES POR COLUMNA
        for col in cols_orig.intersection(cols_proc):
            self._validar_integridad_columna(df_orig[col], df_proc[col], f"{nombre}[{col}]")
        
        return len(self.errores) == 0
    
    def _validar_integridad_columna(self, serie_orig: pd.Series, serie_proc: pd.Series, nombre_col: str):
        """Valida la integridad de una columna específica"""
        
        # 1. Validar número de valores
        if len(serie_orig) != len(serie_proc):
            self.errores.append(f"❌ {nombre_col}: Longitud cambió {len(serie_orig)} → {len(serie_proc)}")
            return
        
        # 2. Validar preservación de NaN
        nan_orig = serie_orig.isnull().sum()
        nan_proc = serie_proc.isnull().sum()
        
        if nan_orig != nan_proc:
            self.errores.append(f"❌ {nombre_col}: NaN cambió {nan_orig} → {nan_proc}")
        else:
            if nan_orig > 0:
                self.detalles.append(f"✓ {nombre_col}: NaN preservados ({nan_orig})")
        
        # 3. Validar que posiciones de NaN sean idénticas
        mask_nan_orig = serie_orig.isnull()
        mask_nan_proc = serie_proc.isnull()
        
        if not mask_nan_orig.equals(mask_nan_proc):
            self.errores.append(f"❌ {nombre_col}: Posiciones de NaN cambiaron")
        
        # 4. Validar valores no-NaN
        valores_no_nan_orig = serie_orig[~mask_nan_orig]
        valores_no_nan_proc = serie_proc[~mask_nan_proc]
        
        if len(valores_no_nan_orig) != len(valores_no_nan_proc):
            self.errores.append(f"❌ {nombre_col}: Cantidad de valores no-NaN cambió")
        
        # 5. Validar que no hay conversiones problemáticas
        if serie_proc.dtype == 'object':
            string_nan_count = (serie_proc == 'nan').sum()
            if string_nan_count > 0:
                self.errores.append(f"❌ {nombre_col}: {string_nan_count} NaN convertidos a string 'nan'")
        
        # 6. Para valores numéricos, validar que los valores sean equivalentes
        if pd.api.types.is_numeric_dtype(serie_orig) and pd.api.types.is_numeric_dtype(serie_proc):
            try:
                # Comparar solo valores no-NaN
                if not pd.testing.assert_series_equal(valores_no_nan_orig, valores_no_nan_proc, check_dtype=False, check_names=False):
                    self.detalles.append(f"✓ {nombre_col}: Valores numéricos preservados")
            except AssertionError as e:
                self.errores.append(f"❌ {nombre_col}: Valores numéricos cambiaron")
    
    def validar_alineacion_tipos(self, df_existente: pd.DataFrame, df_nuevos: pd.DataFrame) -> bool:
        """Valida que la función alinear_tipos_datos_puro preserve la integridad estructural"""
        
        print("🎯 VALIDACIÓN EXHAUSTIVA - INTEGRIDAD ESTRUCTURAL")
        print("=" * 80)
        
        # Crear copias para preservar originales
        df_existente_orig = df_existente.copy()
        df_nuevos_orig = df_nuevos.copy()
        
        print("📊 ESTRUCTURAS INICIALES:")
        print(f"  df_existente: {len(df_existente)} filas, {len(df_existente.columns)} columnas")
        print(f"  df_nuevos: {len(df_nuevos)} filas, {len(df_nuevos.columns)} columnas")
        
        # Mostrar tipos de datos iniciales
        print(f"\n📋 TIPOS DE DATOS INICIALES:")
        for col in df_existente.columns:
            tipo_exist = df_existente[col].dtype
            tipo_nuevo = df_nuevos[col].dtype if col in df_nuevos.columns else "FALTANTE"
            print(f"  {col}: existente={tipo_exist}, nuevos={tipo_nuevo}")
        
        # APLICAR LA FUNCIÓN
        print(f"\n🔧 APLICANDO alinear_tipos_datos_puro()...")
        df_existente_alineado, df_nuevos_alineado = alinear_tipos_datos_puro(df_existente, df_nuevos)
        
        # VALIDAR INTEGRIDAD ESTRUCTURAL
        print(f"\n🔍 VALIDANDO INTEGRIDAD ESTRUCTURAL...")
        
        # Validar DataFrame existente
        integridad_existente = self.comparar_estructuras_origen_procesado(
            df_existente_orig, df_existente_alineado, "df_existente"
        )
        
        # Validar DataFrame nuevos
        integridad_nuevos = self.comparar_estructuras_origen_procesado(
            df_nuevos_orig, df_nuevos_alineado, "df_nuevos"
        )
        
        # VALIDAR COMPATIBILIDAD ENTRE DATAFRAMES
        print(f"\n🔗 VALIDANDO COMPATIBILIDAD ENTRE DATAFRAMES...")
        self._validar_compatibilidad_post_alineacion(df_existente_alineado, df_nuevos_alineado)
        
        # MOSTRAR RESULTADOS
        self._mostrar_resultados_validacion()
        
        return len(self.errores) == 0
    
    def _validar_compatibilidad_post_alineacion(self, df_existente: pd.DataFrame, df_nuevos: pd.DataFrame):
        """Valida que los DataFrames sean compatibles después de la alineación"""
        
        # Verificar que todas las columnas existan en ambos
        cols_existente = set(df_existente.columns)
        cols_nuevos = set(df_nuevos.columns)
        
        if cols_existente != cols_nuevos:
            cols_faltantes_existente = cols_nuevos - cols_existente
            cols_faltantes_nuevos = cols_existente - cols_nuevos
            
            if cols_faltantes_existente:
                self.errores.append(f"❌ Columnas faltantes en df_existente: {list(cols_faltantes_existente)}")
            if cols_faltantes_nuevos:
                self.errores.append(f"❌ Columnas faltantes en df_nuevos: {list(cols_faltantes_nuevos)}")
        else:
            self.detalles.append(f"✓ Compatibilidad: Ambos DataFrames tienen las mismas columnas ({len(cols_existente)})")
        
        # Verificar que los tipos sean compatibles para pd.concat()
        for col in cols_existente.intersection(cols_nuevos):
            tipo_existente = df_existente[col].dtype
            tipo_nuevos = df_nuevos[col].dtype
            
            if tipo_existente != tipo_nuevos:
                self.errores.append(f"❌ Tipos incompatibles en {col}: {tipo_existente} vs {tipo_nuevos}")
            else:
                self.detalles.append(f"✓ Compatibilidad: {col} tipos alineados ({tipo_existente})")
    
    def _mostrar_resultados_validacion(self):
        """Muestra los resultados de la validación"""
        
        print(f"\n{'='*80}")
        print(f"📈 RESULTADOS DE VALIDACIÓN")
        print(f"{'='*80}")
        
        # Mostrar errores críticos
        if self.errores:
            print(f"❌ ERRORES CRÍTICOS ({len(self.errores)}):")
            for error in self.errores:
                print(f"   {error}")
        
        # Mostrar warnings
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   {warning}")
        
        # Mostrar validaciones exitosas
        if self.detalles:
            print(f"\n✅ VALIDACIONES EXITOSAS ({len(self.detalles)}):")
            for detalle in self.detalles[:10]:  # Mostrar solo las primeras 10
                print(f"   {detalle}")
            if len(self.detalles) > 10:
                print(f"   ... y {len(self.detalles) - 10} más")
        
        # Resultado final
        print(f"\n{'='*80}")
        if len(self.errores) == 0:
            print(f"🎉 INTEGRIDAD ESTRUCTURAL PERFECTA")
            print(f"   💧 Origen y destino son 'como dos gotas de agua'")
            print(f"   🔒 Estructura preservada al 100%")
            print(f"   ✨ Función alinear_tipos_datos_puro() APROBADA")
        else:
            print(f"🚨 PROBLEMAS DE INTEGRIDAD DETECTADOS")
            print(f"   ❌ {len(self.errores)} errores críticos")
            print(f"   🔧 Requiere corrección inmediata")


def ejecutar_validacion_completa():
    """Ejecuta una validación completa con diferentes escenarios"""
    
    validador = ValidadorIntegridadEstructural()
    
    # ESCENARIO 1: Tipos diferentes + NaN + Columnas faltantes
    print("🎬 ESCENARIO 1: Caso complejo MTX_WALLET_ORA")
    
    df_existente = pd.DataFrame({
        'TRANSACTION_ID': ['TXN_001', 'TXN_002', 'TXN_003'],
        'ISSUER_ID': ['BANK_A', 'BANK_B', 'BANK_C'],
        'AMOUNT': [100.50, 200.75, 300.25],  # float64
        'STATUS': ['COMPLETED', 'PENDING', 'COMPLETED']
    })
    
    df_nuevos = pd.DataFrame({
        'TRANSACTION_ID': ['TXN_004', 'TXN_005', 'TXN_006'],
        'ISSUER_ID': [np.nan, 'BANK_D', np.nan],  # object con NaN
        'AMOUNT': [150, 250, 350],  # int64 - tipo diferente
        'STATUS': ['PENDING', 'COMPLETED', 'FAILED'],
        'NUEVA_COLUMNA': ['VAL1', 'VAL2', 'VAL3']  # columna extra
    })
    
    resultado_1 = validador.validar_alineacion_tipos(df_existente, df_nuevos)
    
    return resultado_1


if __name__ == "__main__":
    exito = ejecutar_validacion_completa()
    sys.exit(0 if exito else 1)
