#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para analizar datos reales y encontrar problemas de llaves primarias
"""

import boto3
import pandas as pd
import logging
from io import BytesIO
from botocore.exceptions import ClientError
from tabla_config_simple import detectar_clave_primaria_tabla, validar_clave_primaria, TABLA_KEYS_CONFIG

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s: %(message)s')

# Configuración S3
S3_LANDING_BUCKET = "prd-datalake-bronze-zone-637423440311"

def analizar_datos_reales_s3():
    """Analiza datos reales desde S3 para identificar problemas de llaves primarias"""
    
    print("\n" + "="*80)
    print("🔍 ANÁLISIS DE DATOS REALES - Problemas de Llaves Primarias")
    print("="*80)
    
    s3_client = boto3.client('s3')
    
    # 1. Buscar archivos de tablas configuradas
    print(f"\n📋 TABLAS CONFIGURADAS A ANALIZAR:")
    for tabla in TABLA_KEYS_CONFIG.keys():
        print(f"   • {tabla} → {TABLA_KEYS_CONFIG[tabla]}")
    
    # 2. Analizar cada tabla configurada
    for tabla_nombre, claves_configuradas in TABLA_KEYS_CONFIG.items():
        print(f"\n" + "="*60)
        print(f"🔍 ANALIZANDO: {tabla_nombre}")
        print(f"🔑 Claves configuradas: {claves_configuradas}")
        print("="*60)
        
        # Buscar archivos de esta tabla en S3
        archivos_encontrados = buscar_archivos_tabla_s3(s3_client, tabla_nombre)
        
        if not archivos_encontrados:
            print(f"   ❌ No se encontraron archivos para {tabla_nombre}")
            continue
        
        print(f"   📁 Archivos encontrados: {len(archivos_encontrados)}")
        
        # Analizar una muestra de archivos
        analizar_muestra_archivos(s3_client, tabla_nombre, archivos_encontrados[:3], claves_configuradas)
    
    print(f"\n" + "="*80)
    print("✅ ANÁLISIS COMPLETADO")
    print("="*80)

def buscar_archivos_tabla_s3(s3_client, tabla_nombre):
    """Busca archivos de una tabla específica en S3"""
    archivos = []
    
    # Posibles rutas donde pueden estar los archivos
    rutas_busqueda = [
        f"{tabla_nombre}/",
        f"CARGA/{tabla_nombre}/",
        f"RAW/{tabla_nombre}/",
        f"Bronze/{tabla_nombre}/",
    ]
    
    for ruta in rutas_busqueda:
        try:
            paginator = s3_client.get_paginator('list_objects_v2')
            for page in paginator.paginate(Bucket=S3_LANDING_BUCKET, Prefix=ruta):
                if 'Contents' in page:
                    for obj in page['Contents']:
                        if obj['Key'].endswith('.parquet'):
                            archivos.append(obj['Key'])
            
            if archivos:
                print(f"   📍 Encontrados en ruta: {ruta}")
                break
                
        except Exception as e:
            logging.warning(f"Error buscando en {ruta}: {str(e)}")
    
    return archivos

def analizar_muestra_archivos(s3_client, tabla_nombre, archivos, claves_configuradas):
    """Analiza una muestra de archivos para identificar problemas"""
    
    for i, archivo_key in enumerate(archivos):
        print(f"\n   📄 Archivo {i+1}: {archivo_key}")
        
        try:
            # Leer archivo desde S3
            response = s3_client.get_object(Bucket=S3_LANDING_BUCKET, Key=archivo_key)
            buffer = BytesIO(response['Body'].read())
            df = pd.read_parquet(buffer)
            
            print(f"      📊 Registros: {len(df)}")
            print(f"      📋 Columnas disponibles: {list(df.columns)}")
            
            # Verificar si las columnas configuradas existen
            columnas_faltantes = [col for col in claves_configuradas if col not in df.columns]
            columnas_existentes = [col for col in claves_configuradas if col in df.columns]
            
            if columnas_faltantes:
                print(f"      ❌ PROBLEMA: Columnas configuradas faltantes: {columnas_faltantes}")
                print(f"      💡 Sugerencia: Verificar configuración o nombres de columnas")
                
                # Buscar columnas similares
                columnas_similares = buscar_columnas_similares(df.columns, columnas_faltantes)
                if columnas_similares:
                    print(f"      🔍 Posibles columnas similares: {columnas_similares}")
            
            if columnas_existentes:
                print(f"      ✅ Columnas configuradas encontradas: {columnas_existentes}")
                
                # Analizar calidad de datos en columnas clave
                for col in columnas_existentes:
                    analizar_calidad_columna_clave(df, col)
            
            # Detectar y validar clave primaria
            claves_detectadas = detectar_clave_primaria_tabla(tabla_nombre, df)
            print(f"      🔑 Claves detectadas: {claves_detectadas}")
            
            if claves_detectadas:
                es_valida = validar_clave_primaria(df, claves_detectadas)
                print(f"      ✅ ¿Clave válida?: {es_valida}")
                
                if not es_valida:
                    diagnosticar_problemas_validacion(df, claves_detectadas)
            
        except Exception as e:
            print(f"      ❌ Error leyendo archivo: {str(e)}")

def buscar_columnas_similares(columnas_disponibles, columnas_buscadas):
    """Busca columnas con nombres similares"""
    similares = {}
    
    for col_buscada in columnas_buscadas:
        col_buscada_lower = col_buscada.lower()
        for col_disponible in columnas_disponibles:
            col_disponible_lower = col_disponible.lower()
            
            # Coincidencia parcial
            if col_buscada_lower in col_disponible_lower or col_disponible_lower in col_buscada_lower:
                if col_buscada not in similares:
                    similares[col_buscada] = []
                similares[col_buscada].append(col_disponible)
    
    return similares

def analizar_calidad_columna_clave(df, columna):
    """Analiza la calidad de datos en una columna clave"""
    print(f"         🔍 Análisis de {columna}:")
    
    total = len(df)
    nulos = df[columna].isna().sum()
    vacios = (df[columna].astype(str).str.strip() == '').sum()
    unicos = df[columna].nunique()
    
    print(f"            • Total registros: {total}")
    print(f"            • Valores únicos: {unicos}")
    print(f"            • Valores nulos: {nulos} ({nulos/total*100:.1f}%)")
    print(f"            • Valores vacíos: {vacios} ({vacios/total*100:.1f}%)")
    print(f"            • Unicidad: {unicos/total*100:.1f}%")
    
    # Muestra de valores
    valores_muestra = df[columna].dropna().head(5).tolist()
    print(f"            • Muestra valores: {valores_muestra}")

def diagnosticar_problemas_validacion(df, claves):
    """Diagnostica por qué falló la validación"""
    print(f"      🚨 DIAGNÓSTICO DE PROBLEMAS:")
    
    for col in claves:
        if col in df.columns:
            # Valores problemáticos
            mask_nulos = df[col].isna()
            mask_vacios = (df[col].astype(str).str.strip() == '')
            
            if mask_nulos.any():
                indices_nulos = df[mask_nulos].index.tolist()[:5]
                print(f"         ❌ {col}: Valores nulos en filas {indices_nulos}")
            
            if mask_vacios.any():
                indices_vacios = df[mask_vacios].index.tolist()[:5]
                print(f"         ❌ {col}: Valores vacíos en filas {indices_vacios}")
            
            # Duplicados
            duplicados = df[col].duplicated()
            if duplicados.any():
                valores_duplicados = df[duplicados][col].unique()[:5]
                print(f"         ❌ {col}: Valores duplicados: {valores_duplicados}")

if __name__ == "__main__":
    try:
        analizar_datos_reales_s3()
    except Exception as e:
        print(f"❌ Error en análisis: {str(e)}")
        print("💡 Asegúrate de tener configuradas las credenciales de AWS")
