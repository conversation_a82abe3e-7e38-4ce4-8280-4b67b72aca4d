#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import sys
import warnings

warnings.filterwarnings('ignore')

# Importar la función que vamos a probar
sys.path.append('/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/Flow_ETL_Landing')
from app_raw_consolidado import normalizar_tipos_datos

def test_manejo_null_completo():
    """
    Prueba exhaustiva del manejo de valores NULL en todas sus formas.
    """
    print("🧪 INICIANDO PRUEBA EXHAUSTIVA DE MANEJO DE NULLs")
    print("=" * 60)
    
    # Crear DataFrame de prueba con TODOS los tipos de NULL posibles
    datos_prueba = {
        'USER_GRADE': ['OPT', 'WALLET', None, 'null', '<null>', 'NULL', np.nan, 'None', 'OPT'],
        'DESCRIPTION': ['Expense Account', None, 'null', '<null>', 'None', 'Expense Account', '', 'n/a', 'Valid Description'],
        'WALLET_REF_ID': [None, 'null', '<null>', 'NULL', 'None', '', 'WALLET123', 'undefined', 'WALLET456'],
        'ACCOUNT_GROUP_ID': [None, np.nan, 'null', '<null>', 'None', 'N/A', '', '101', '102'],
        'ISSUER_ID': ['200', '201', None, 'null', '<null>', 'None', 'S20', '203', '204']
    }
    
    df_original = pd.DataFrame(datos_prueba)
    
    print("📊 DATOS ORIGINALES:")
    print(df_original)
    print(f"\nTipos de datos originales:")
    print(df_original.dtypes)
    print()
    
    # Aplicar normalización
    df_normalizado = normalizar_tipos_datos(df_original)
    
    print("🔧 DATOS DESPUÉS DE NORMALIZACIÓN:")
    print(df_normalizado)
    print(f"\nTipos de datos normalizados:")
    print(df_normalizado.dtypes)
    print()
    
    # Verificaciones específicas
    print("🔍 VERIFICACIONES DE MANEJO DE NULL:")
    print("-" * 40)
    
    # 1. Verificar que no hay valores null/None/NULL en ninguna columna
    for col in df_normalizado.columns:
        valores_null = df_normalizado[col].isin([
            'null', 'NULL', '<null>', 'None', 'NONE', '<None>',
            'nan', 'NaN', 'nil', 'NIL', 'undefined', 'N/A', 'n/a'
        ]).sum()
        
        print(f"   {col}: {valores_null} valores NULL encontrados")
        
        if valores_null == 0:
            print(f"   ✅ {col}: Todos los NULLs convertidos correctamente")
        else:
            print(f"   ❌ {col}: Aún quedan {valores_null} valores NULL")
    
    print()
    
    # 2. Verificar que los valores categóricos válidos se mantuvieron
    print("📋 VERIFICACIÓN DE VALORES CATEGÓRICOS:")
    print("-" * 40)
    
    # USER_GRADE debe mantener OPT y WALLET
    opt_count = (df_normalizado['USER_GRADE'] == 'OPT').sum()
    wallet_count = (df_normalizado['USER_GRADE'] == 'WALLET').sum()
    print(f"   USER_GRADE - OPT: {opt_count} (esperado: 2)")
    print(f"   USER_GRADE - WALLET: {wallet_count} (esperado: 1)")
    
    # Verificar que hay cadenas vacías donde había NULLs
    empty_strings = (df_normalizado == '').sum().sum()
    print(f"   Total cadenas vacías creadas: {empty_strings}")
    print()
    
    # 3. Mostrar valores únicos por columna
    print("📈 VALORES ÚNICOS POR COLUMNA:")
    print("-" * 40)
    for col in df_normalizado.columns:
        valores_unicos = df_normalizado[col].unique()
        print(f"   {col}: {valores_unicos}")
    
    print()
    
    # 4. Verificación de tipos de datos
    print("📊 VERIFICACIÓN DE TIPOS DE DATOS:")
    print("-" * 40)
    for col in df_normalizado.columns:
        tipo = df_normalizado[col].dtype
        tiene_nulos = df_normalizado[col].isna().sum()
        print(f"   {col}: {tipo}, NaN: {tiene_nulos}")
    
    print()
    print("🎯 RESUMEN DE LA PRUEBA:")
    print("=" * 60)
    
    # Verificación final
    total_nulls_restantes = 0
    for col in df_normalizado.columns:
        valores_null = df_normalizado[col].isin([
            'null', 'NULL', '<null>', 'None', 'NONE', '<None>',
            'nan', 'NaN', 'nil', 'NIL', 'undefined', 'N/A', 'n/a'
        ]).sum()
        total_nulls_restantes += valores_null
    
    if total_nulls_restantes == 0:
        print("✅ ÉXITO: Todos los valores NULL fueron convertidos a cadenas vacías")
        print("✅ Los valores categóricos (OPT, WALLET) se preservaron correctamente")
        print("✅ El sistema está listo para procesar cualquier tabla sin pérdida de datos")
    else:
        print(f"❌ FALLO: Aún quedan {total_nulls_restantes} valores NULL sin convertir")
    
    return total_nulls_restantes == 0

if __name__ == "__main__":
    exito = test_manejo_null_completo()
    
    if exito:
        print("\n🚀 SISTEMA LISTO PARA CONSOLIDACIÓN SIN PÉRDIDA DE DATOS")
        sys.exit(0)
    else:
        print("\n⚠️  NECESITA AJUSTES ADICIONALES")
        sys.exit(1)
